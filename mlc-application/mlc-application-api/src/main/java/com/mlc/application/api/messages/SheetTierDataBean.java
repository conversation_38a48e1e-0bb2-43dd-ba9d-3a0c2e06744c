package com.mlc.application.api.messages;


import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.mlc.base.common.beans.filter.IFilterConvertible;
import com.mlc.base.common.beans.sort.FrontSortBean;
import com.mlc.base.core.worksheet.controls.base.BasicControl;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import lombok.Getter;
import lombok.Setter;


/**
 * 工作表视图数据Bean
 */
@Getter
@Setter
public class SheetTierDataBean {

    private String worksheetId;

    // 表单中配置的所有的控件
    private List<BasicControl> worksheetControls;

    // 从树结构中获取 工作表/视图/控件:ID -> 资源ID 的映射
    private Map<String, String> castToResourceIdMap;

    // 视图层级列表
    private List<ViewTierDataBean> viewTierList;

    public SheetTierDataBean(String worksheetId, List<BasicControl> worksheetControls, Map<String, String> castToResourceIdMap) {
        this.worksheetId = worksheetId;
        this.worksheetControls =  worksheetControls;
        this.castToResourceIdMap = castToResourceIdMap;
    }

    public void addViewTier(ViewTierDataBean viewTierDataBean) {
        if (viewTierList == null) {
            viewTierList = new ArrayList<>();
        }
        viewTierList.add(viewTierDataBean);
    }

    @Getter
    @Setter
    public static class ViewTierDataBean {
        // 视图ID
        private String viewId;

        // 视图类型
        private Integer viewType;

        // 看板等分组控件ID
        private String viewControl;

        // 初始过滤
        private String filters;

        // 快速过滤
        private String fastFilters;

        // 导航分组过滤
        private String navGroup;

        // 排序
        private String moreSort;


        // 当前视图下需要对用户隐藏的控件
        private Set<String> hiddenControls;

        // 当前视图下<排除需要对用户隐藏的控件>，其余的需要显示的控件
        private Set<String> showControls;

        /**
         * 获取排序条件列表
         */
        public List<FrontSortBean> getMoreSorts() {
            try {
                return new ObjectMapper().readValue(this.moreSort, new TypeReference<>() {});
            } catch (JsonProcessingException e) {
                throw new RuntimeException(e);
            }
        }

        /**
         * 获取默认筛选条件列表
         */
        public List<IFilterConvertible> convertDefaultFilters() {
            return IFilterConvertible.deserializeFilterList(this.filters);
        }

        /**
         * 获取快速筛选条件列表
         */
        public List<IFilterConvertible> convertFastFilters() {
            return IFilterConvertible.deserializeFilterList(this.fastFilters);
        }

        /**
         * 获取导航分组筛选条件列表
         */
        public List<IFilterConvertible> convertNavGroupFilter() {
            return IFilterConvertible.deserializeFilterList(this.navGroup);
        }
    }
}
