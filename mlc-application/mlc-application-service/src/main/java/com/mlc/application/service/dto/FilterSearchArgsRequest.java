package com.mlc.application.service.dto;


import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.mlc.application.service.MlcApplicationErrors;
import com.mlc.base.common.beans.filter.FrontViewFilterNormalBean;
import com.mlc.base.common.beans.filter.IFilterConvertible;
import com.mlc.base.common.beans.filter.NormalFilterBean;
import com.mlc.base.common.exception.errors.CommonErrors;
import com.mlc.base.common.utils.convert.WorkSheetFilterConvertQueryBean;
import io.nop.api.core.annotations.data.DataBean;
import io.nop.api.core.annotations.meta.PropMeta;
import io.nop.api.core.beans.query.QueryBean;
import io.nop.api.core.exceptions.NopException;
import io.nop.core.lang.xml.XNode;
import java.util.List;
import java.util.Map;
import lombok.Getter;
import lombok.Setter;
import org.springframework.util.CollectionUtils;

@Getter
@Setter
@DataBean
public class FilterSearchArgsRequest {

    // 工作表id
    private String worksheetId;

    // 模型对象实体id
    private String modelObjectEntityId;

    /**
     * 已知前端备注
     * 7: 获取表全部数据
     * 21: 草稿箱
     */
    private Integer getType;

    // 查询列
    private Object filterControls;

    // 快速筛选
    private List<NormalFilterBean> fastFilters;

    private List filtersGroup;

    // 导航分组筛选
    private List<NormalFilterBean> navGroupFilters;

    // 组件筛选
    private List<FrontViewFilterNormalBean> groupFilters;

    private List controls;

    // 排序列
    private List<String> sortControls;

    // 关键词
    private String keyWords;

    // 页大小
    @PropMeta(mandatory = true, min = 20, max = 100)
    private Integer pageSize;

    // 页码
    @PropMeta(mandatory = true, min = 1)
    private Integer pageIndex;

    private Integer searchType;

    private Integer status;

    // 是否已读
    private Boolean isUnRead;

    // 是否查询工作表的详情
    private Boolean isGetWorksheet;

    // 视图Id
    @PropMeta(mandatory = true)
    private String viewId;  // 当前所在列表操作的视图id

    private String relationOpenViewId; // 打开关联/子表控件列表的视图id
//    private String currentViewId; // 当前所在列表操作的视图id

    // 应用Id
    private String appId;

    // relationWorksheetId
    private String relationWorksheetId;

    // 行id
    private String rowId;

    // 控件Id
    private String controlId;

    // 看板参数
    private String kanbanKey;

    // 层级视图加载层数
    private Integer layer;

    // 日历视图开始时间
    private String beginTime;

    // 日历视图结束时间
    private String endTime;

    // 看板页大小
    private Integer kanbanSize;

    // 看板页码
    private Integer kanbanIndex;

    // 公开表单ID
    private String formId;

    // 填写链接id
    private String linkId;

    // 统计图ID
    private String reportId;

    // 不获取总记录数
    private Boolean notGetTotal;

    // 额外的请求参数
    private Map<String, Object> requestParams;

    private String checkView;

    private Boolean getWorksheet;

    private Boolean getTemplate;

    private Boolean getRules;

    private Integer langType;

    //根据 pageIndex 和 pageSize 计算出Offset
    public Integer getOffset(Integer paraPageIndex, Integer paraPageSize) {
        return (paraPageIndex - 1) * paraPageSize;
    }

    public QueryBean toPageQueryBean() {
        if ((this.pageIndex == null || this.pageSize == null) && (this.kanbanIndex == null || this.kanbanSize == null)) {
            throw new NopException(CommonErrors.ERR_COMMON_INVALID_PAGE_INFO);
        }

        QueryBean queryBean = new QueryBean();
        if (this.kanbanIndex != null && this.kanbanSize != null) {
            queryBean.setLimit(this.kanbanSize);
            queryBean.setOffset(getOffset(this.kanbanIndex, this.kanbanSize));
        } else {
            queryBean.setLimit(this.pageSize);
            queryBean.setOffset(getOffset(this.pageIndex, this.pageSize));
        }
        return queryBean;
    }

    // 设置默认分页
    public void setDefaultPage() {
        if (this.pageSize == null) {
            this.pageSize = 20;
        }
        if (this.pageIndex == null) {
            this.pageIndex = 1;
        }
    }

    public void toFilterQueryBean(QueryBean queryBean) {
        QueryBean bean = queryBean;
        if (bean == null) {
            bean = new QueryBean();
        }
        
        // 添加快速筛选条件
        addFilterToQuery(bean, this.fastFilters);
        
        // 添加导航分组筛选条件
        addFilterToQuery(bean, this.navGroupFilters);
        
        // 添加控件筛选条件
        addFilterToQuery(bean, this.covFilterControls());
        
        // 添加组件筛选条件
        addFilterToQuery(bean, this.groupFilters);
    }
    
    /**
     * 统一的筛选条件添加方法
     * @param queryBean 查询对象
     * @param filters 筛选条件列表
     */
    private void addFilterToQuery(QueryBean queryBean, List<? extends IFilterConvertible> filters) {
        if (!CollectionUtils.isEmpty(filters)) {
            XNode convertFilters = WorkSheetFilterConvertQueryBean.toFilter(filters);
            if (convertFilters != null) {
                queryBean.addFilter(convertFilters.toTreeBean());
            }
        }
    }

    // 将filterControls转换为IFilterConvertible列表
    public List<IFilterConvertible> covFilterControls() {
        List<IFilterConvertible> iFilterConvertibles;
        try {
            String mapperStr = new ObjectMapper().writeValueAsString(this.filterControls);
            iFilterConvertibles = IFilterConvertible.deserializeFilterList(mapperStr);
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }

        return iFilterConvertibles;
    }

    public String getId() {
        if (this.rowId == null) {
            throw new NopException(MlcApplicationErrors.ERR_APPLICATION_QUERY_DATA);
        }
        return this.rowId;
    }
}
