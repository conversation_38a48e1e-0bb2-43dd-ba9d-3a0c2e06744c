package com.mlc.application.service.dto;

import static com.mlc.application.service.MlcApplicationConstants.ORM_FIELD_CREATE_TIME;
import static com.mlc.application.service.MlcApplicationConstants.ORM_FIELD_UPDATE_TIME;
import static com.mlc.base.common.exception.errors.InternalErrors.ERR_INTERNAL_UNKNOWN_DATA_CONVERT;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.mlc.application.service.biz.permission.RowPermissionBuilder;
import com.mlc.base.core.auth.ExtUserContextImpl;
import io.nop.api.core.exceptions.NopException;
import io.nop.core.context.IServiceContext;
import io.nop.graphql.core.reflection.GraphQLBizModel;
import java.util.Map;
import lombok.Data;

/**
 * 行详情Bean
 */
@Data
public class RowDetailResponse {

    private String rowData;

    private Map<String, Object> rowDataMap;

    private String createTime;

    private String updateTime;

    private String titleName;

    private boolean allowEdit;

    private boolean allowDelete;

    private String projectId;

    private OperateAccount ownerAccount;

    private OperateAccount createAccount;

    private OperateAccount editAccount;

    private int shareRange;

    private int resultCode = 1;

    private int roleType;

    private View view;

    private String groupId;

    private boolean isViewData;

    private boolean isFavorite;

    private boolean contentEncrypted;

    public RowDetailResponse(FilterSearchArgsRequest filterSearchArgs, IServiceContext context, GraphQLBizModel bizModel) {

        // -- 构建行权限 -- //
        Map<String, Object> build = RowPermissionBuilder.builder(context, bizModel.getBizObjName(), filterSearchArgs.getViewId())
                                        .allowEdit().allowDelete().build();
        this.allowEdit = (Boolean) build.get(RowPermissionBuilder.ALLOW_EDIT_KEY);
        this.allowDelete = (Boolean) build.get(RowPermissionBuilder.ALLOW_DELETE_KEY);
        // -- 构建行权限 -- //

        // -- 构建角色信息 -- //
        ExtUserContextImpl userContext = (ExtUserContextImpl) context.getUserContext();
        this.roleType = userContext.getUserApplicationRole().calcMaxRoleType();
        // -- 构建角色信息 -- //
    }

    public void setRowData(Map<String, Object> rowData) {
        this.setRowDataMap(rowData);
        try {
            this.rowData = new ObjectMapper().writeValueAsString(rowData);
        } catch (JsonProcessingException e) {
            throw new NopException(ERR_INTERNAL_UNKNOWN_DATA_CONVERT, e);
        }
        // todo:要等 xmeta 重构后再处理,现在 view 视图中还没有这些信息，包括 ownerAccount, createAccount, editAccount
        this.createTime = (String) rowData.get(ORM_FIELD_CREATE_TIME);
        this.updateTime = (String) rowData.get(ORM_FIELD_UPDATE_TIME);
        // 要等 xmeta 重构后再处理,现在 view 视图中还没有这些信息，包括 ownerAccount, createAccount, editAccount
    }

    @Data
    private static class OperateAccount {
        private String accountId;
        private String fullname;
        private String avatar;
        private boolean isPortal;
        private int status;
    }

    @Data
    private static class View {
        private String viewId;
        private String name;
        private String[] controls;
    }
}
