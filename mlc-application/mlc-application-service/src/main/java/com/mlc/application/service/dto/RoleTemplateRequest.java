package com.mlc.application.service.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.mlc.base.common.enums.application.AppMemberPermissionType;
import io.nop.api.core.annotations.data.DataBean;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class RoleTemplateRequest {

    private String projectId;
    private String appId;

    private String roleId;
    private String name;
    private String description;
    private Boolean hideAppForMembers;
    private Integer roleType;
    /**
     * {@link com.mlc.base.common.enums.application.PermissionWayEnum}
     */
    private Integer permissionWay;

    // 差异化资源权限, resourceId -> (permission -> value)
    private Map<String, Map<String, Object>> deepDiffResources;

    private List<Sheet> sheets;
    private List<Page> pages;
    private List<String> extendAttrs;
    private List<String> optionalControls;

    private Boolean generalAdd;
    private Boolean generalShare;
    private Boolean generalImport;
    private Boolean generalExport;
    private Boolean generalDiscussion;
    private Boolean generalSystemPrinting;
    private Boolean generalAttachmentDownload;
    private Boolean generalLogging;

    public void addSheet(Sheet sheet) {
        sheets.add(sheet);
    }

    public RoleTemplateRequest(String projectId, String appId) {
        this.projectId = projectId;
        this.appId = appId;

        this.permissionWay = 0;
        this.hideAppForMembers = false;
        this.roleType = 0;

        this.sheets = new ArrayList<>();
        this.pages = new ArrayList<>();
        this.optionalControls = new ArrayList<>();
        this.extendAttrs = new ArrayList<>();
    }

    public interface IResourceId {
        String getResourceId();
    }

    @Data
    public static class Sheet implements IResourceId {

        private String resourceId;
        private String sheetId;
        private String sheetName;
        private Integer sortIndex;
        private String iconUrl;

        private List<View> views;
        private List<Field> fields;

        private Boolean canAdd;
        private Boolean navigateHide;

        private Integer readLevel;
        private Integer editLevel;
        private Integer removeLevel;
        private List<UserPermissionField> userFields;

        private List<String> canReadExtendAttrs;
        private List<String> canEditExtendAttrs;
        private List<String> canRemoveExtendAttrs;

        private Boolean worksheetShareView;
        private Boolean worksheetImport;
        private Boolean worksheetExport;
        private Boolean worksheetDiscuss;
        private Boolean worksheetLogging;
        private Boolean worksheetBatchOperation;
        private Boolean recordShare;
        private Boolean recordDiscussion;
        private Boolean recordSystemPrinting;
        private Boolean recordAttachmentDownload;
        private Boolean recordLogging;
        private Boolean payment;

        private List<String> unableCustomButtons;
        private List<String> unablePrintTemplates;

        public void addView(View view) {
            views.add(view);
        }

        public void addField(Field field) {
            fields.add(field);
        }

        public Sheet(String resourceId, String sheetId, String sheetName) {
            this.sortIndex = 0;
            this.iconUrl = "https://fp1.mingdaoyun.cn/customIcon/table.svg";
            this.navigateHide = false;

            this.resourceId = resourceId;
            this.sheetId = sheetId;
            this.sheetName = sheetName;

            this.views = new ArrayList<>();
            this.fields = new ArrayList<>();
            this.canReadExtendAttrs = new ArrayList<>();
            this.canEditExtendAttrs = new ArrayList<>();
            this.canRemoveExtendAttrs = new ArrayList<>();
            this.unableCustomButtons = new ArrayList<>();
            this.unablePrintTemplates = new ArrayList<>();
            this.userFields = new ArrayList<>();
        }
    }

    @Data
    public static class Page {
        private String pageId;
        private String name;
        private Boolean checked;
        private Boolean navigateHide;
        private Integer sortIndex;
        private String iconUrl;

        public Page(Boolean init){
            this.iconUrl = "https://fp1.mingdaoyun.cn/customIcon/dashboard.svg";
            this.navigateHide = false;
            this.checked = false;
            this.sortIndex = 0;
        }
    }

    @Data
    public static class View implements IResourceId {
        private String resourceId;
        private String viewId;
        private String viewName;
        private Integer type;
        private Boolean canRead;
        private Boolean canEdit;
        private Boolean canRemove;

        @JsonIgnore
        private Boolean navigateHide; // 可能是前端的 bug，点击工作表-全部就会出现这个字段，先排除

        public View(String resourceId, String viewId, String viewName, Integer type) {
            this.resourceId = resourceId;
            this.viewId = viewId;
            this.viewName = viewName;
            this.type = type;
        }
    }

    @Data
    public static class Field implements IResourceId {
        private String resourceId;
        private String fieldId;
        private String fieldName;
        private Integer type;
        private String sectionId;
        private Boolean notRead;
        private Boolean notEdit;
        private Boolean notAdd;
        private Boolean isDecrypt;
        private Boolean hideWhenAdded;
        private Boolean isHide;

        public Field(String resourceId, String fieldId,String fieldName, Integer type) {
            this.resourceId = resourceId;
            this.fieldId = fieldId;
            this.type = type;
            this.fieldName = fieldName;
        }
    }

    @Data
    @DataBean
    public static class UserPermissionField {
        private String id;
        private String name;

        /**
         *  {@link AppMemberPermissionType}
         */
        private Integer userPermission;

        public UserPermissionField(String id, String name, Integer userPermission) {
            this.id = id;
            this.name = name;
            this.userPermission = userPermission;
        }
    }
}