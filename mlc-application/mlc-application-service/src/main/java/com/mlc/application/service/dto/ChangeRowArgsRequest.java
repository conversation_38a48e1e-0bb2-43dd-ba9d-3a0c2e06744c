package com.mlc.application.service.dto;

import com.google.common.base.Strings;
import com.mlc.application.core.MlcApplicationErrors;
import com.mlc.base.core.worksheet.controls.base.BasicControl;
import io.nop.api.core.annotations.meta.PropMeta;
import io.nop.api.core.exceptions.NopException;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.Getter;
import lombok.Setter;
import org.springframework.util.CollectionUtils;

@Getter
@Setter
public class ChangeRowArgsRequest {

    // 行ID，也是主键
    private String rowId;

    // 多行ID
    private List<String> rowIds;

    private Integer deleteType;

    @PropMeta(description = "应用Id", mandatory = true)
    private String appId;

    @PropMeta(description = "工作表id", mandatory = true)
    private String worksheetId;

    @PropMeta(description = "视图Id", mandatory = true)
    private String viewId;

    @PropMeta(description = "模型对象id", mandatory = true)
    private String modelObjectEntityId;

    private String modelObjectEntityName;

    private String projectID;

    // 该行所有的cell
    private List<Map<String, Object>> receiveControls;

    // 批量新增所有rows
    private List<BasicControl> receiveRows;

    // 自定义按钮ID
    private String btnId;

    // 按钮备注
    private String btnRemark;

    // 点击按钮对应的工作表ID
    private String btnWorksheetId;

    // 点击按钮对应的行记录ID
    private String btnRowId;

    private Boolean hasFilters;

    // 主记录ID,编辑时用的
    private Object masterRecord;

    // 1：正常 21：草稿箱
    // @PropMeta(description = "行状态", mandatory = true)
    private Integer rowStatus;

    // 草稿ID
    private String draftRowId;

    /**
     * 新旧控件数据，value 是新控件的值
     */
    private List<Map<String, Object>> newOldControl;

    // 好像是用来标识分享类型的，但是好像是废弃的
    @Deprecated
    private Integer getType;

    // -- 验证码相关 -- //
    private String ticket;

    private String randStr;

    // 验证码类型（默认腾讯云）
    private Object captchaType;

    // 验证码【根据配置来校验是否必填】
    private String verifyCode;
    // -- 验证码相关 -- //

    // 推送ID
    private String pushUniqueId;

    // 未登录用户临时登录凭据
    private String clientId;

    public Set<String> getRowIds() {
        if (CollectionUtils.isEmpty(rowIds) || rowIds.stream().anyMatch(Strings::isNullOrEmpty)) {
            throw new NopException(MlcApplicationErrors.ERR_APPLICATION_QUERY_DATA);
        }
        return rowIds.stream().collect(Collectors.toUnmodifiableSet());
    }

    /**
     * 获取主键
     */
    public String getId() {
        if(Strings.isNullOrEmpty(rowId)) {
            throw new NopException(MlcApplicationErrors.ERR_APPLICATION_QUERY_DATA);
        }
        return rowId;
    }
}
