package com.mlc.system.service.biz;


import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.mlc.base.common.constant.TenantConstant;
import com.mlc.base.common.enums.uop.LangTypeEnum;
import com.mlc.system.service.biz.bean.Account;
import com.mlc.system.service.biz.bean.FileStoreConfig;
import com.mlc.system.service.biz.bean.GuideSettings;
import com.mlc.system.service.biz.bean.MdConfig;
import com.mlc.system.service.biz.bean.MdGlobal;
import com.mlc.system.service.biz.bean.Project;
import com.mlc.system.service.biz.bean.RootSetting;
import com.mlc.uop.api.IUserBaseInfoApi;
import com.mlc.uop.api.IUserTenantInfoApi;
import com.mlc.uop.api.messages.UserBaseInfo;
import com.mlc.uop.api.messages.UserTenantInfo;
import io.nop.api.core.annotations.biz.BizModel;
import io.nop.api.core.annotations.biz.BizQuery;
import io.nop.core.context.IServiceContext;
import jakarta.inject.Inject;
import java.util.List;
import java.util.Map;

/**
 * 全局配置
 */
@BizModel("Global")
public class GlobalSettingBizModel {

    @Inject
    IUserTenantInfoApi userTenantInfoApi;

    @Inject
    IUserBaseInfoApi userInfoApi;

    private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper();

    @BizQuery("GetGlobalMeta")
    public Map getGlobalMeta(IServiceContext context) {

        MdConfig mdConfig = new MdConfig().setDefaultLang(LangTypeEnum.ZH_HANS.lang())
                                          .setCaptchaType(1)
                                          .setVersion("1.0.0");
        FileStoreConfig fileStoreConfig = new FileStoreConfig().setPictureHost("https://p1.mingdaoyun.cn/")
                                                               .setPubHost("https://fp1.mingdaoyun.cn/")
                                                               .setMediaHost("https://m1.mingdaoyun.cn/")
                                                               .setDocumentHost("https://d1.mingdaoyun.cn/");

        MdGlobal mdGlobal = new MdGlobal().setConfig(mdConfig).setFileStoreConfig(fileStoreConfig);

        if(context.getUserContext() != null){
            mdConfig.setPlatformLocal(true);
            UserBaseInfo userInfo = userInfoApi.getUserInfo(context.getUserId());

            Account account = new Account();
            account.setAccountId(userInfo.getUserId());
            account.setFullname(userInfo.getFullName());
            account.setAvatar(userInfo.getEmail());
            account.setMobilePhone(userInfo.getMobilePhone());
            account.setCreateTime(userInfo.getCreatedAt().toString());
            account.setSuperAdmin(true);
            account.setPortal(false);
            account.setLang(LangTypeEnum.ZH_HANS.lang());

            GuideSettings guideSettings = new GuideSettings();
            if (account.getMobilePhone() != null) {
                guideSettings.setAccountMobilePhone(true);
            } else if (account.getEmail() != null){
                guideSettings.setAccountEmail(true);
            }

            List<UserTenantInfo> userTenantInfoList = userTenantInfoApi.getUserTenantInfoList(context.getUserId());
            userTenantInfoList.forEach(userTenantInfo -> {
                 Project project = new Project().setProjectId(userTenantInfo.getTenantId())
                                               .setCompanyName(userTenantInfo.getCompanyName())
                                               .setIsSuperAdmin(context.getUserContext().isUserInRole(TenantConstant.SUPER_ADMIN_IDENTITY));
                account.getProjects().add(project);
            });

            account.setGuideSettings(guideSettings);
            mdGlobal.setAccount(account);
        }

        RootSetting rootSettingBean = new RootSetting().setMdGlobal(mdGlobal);
        OBJECT_MAPPER.setSerializationInclusion(JsonInclude.Include.NON_NULL);
        return OBJECT_MAPPER.convertValue(rootSettingBean, Map.class);
    }

    @BizQuery("LoadExtraDatas")
    public List loadExtraDatas() throws JsonProcessingException {
        String res = "[\n"
            + "                    {\n"
            + "                        \"id\": \"industryId\",\n"
            + "                        \"name\": \"行业\",\n"
            + "                        \"required\": 1,\n"
            + "                        \"type\": 3,\n"
            + "                        \"multiple\": 0,\n"
            + "                        \"options\": [\n"
            + "                            {\n"
            + "                                \"id\": \"2\",\n"
            + "                                \"name\": \"信息传输、软件和信息技术服务业\"\n"
            + "                            },\n"
            + "                            {\n"
            + "                                \"id\": \"7\",\n"
            + "                                \"name\": \"制造业\"\n"
            + "                            },\n"
            + "                            {\n"
            + "                                \"id\": \"1\",\n"
            + "                                \"name\": \"租赁和商务服务业\"\n"
            + "                            },\n"
            + "                            {\n"
            + "                                \"id\": \"13\",\n"
            + "                                \"name\": \"教育\"\n"
            + "                            },\n"
            + "                            {\n"
            + "                                \"id\": \"5\",\n"
            + "                                \"name\": \"金融业\"\n"
            + "                            },\n"
            + "                            {\n"
            + "                                \"id\": \"6\",\n"
            + "                                \"name\": \"建筑业\"\n"
            + "                            },\n"
            + "                            {\n"
            + "                                \"id\": \"16\",\n"
            + "                                \"name\": \"科学研究和技术服务业\"\n"
            + "                            },\n"
            + "                            {\n"
            + "                                \"id\": \"8\",\n"
            + "                                \"name\": \"批发和零售业\"\n"
            + "                            },\n"
            + "                            {\n"
            + "                                \"id\": \"17\",\n"
            + "                                \"name\": \"住宿和餐饮业\"\n"
            + "                            },\n"
            + "                            {\n"
            + "                                \"id\": \"10\",\n"
            + "                                \"name\": \"文化、体育和娱乐业\"\n"
            + "                            },\n"
            + "                            {\n"
            + "                                \"id\": \"18\",\n"
            + "                                \"name\": \"房地产业\"\n"
            + "                            },\n"
            + "                            {\n"
            + "                                \"id\": \"9\",\n"
            + "                                \"name\": \"交通运输、仓储和邮政业\"\n"
            + "                            },\n"
            + "                            {\n"
            + "                                \"id\": \"19\",\n"
            + "                                \"name\": \"卫生和社会工作\"\n"
            + "                            },\n"
            + "                            {\n"
            + "                                \"id\": \"12\",\n"
            + "                                \"name\": \"公共管理、社会保障和社会组织\"\n"
            + "                            },\n"
            + "                            {\n"
            + "                                \"id\": \"20\",\n"
            + "                                \"name\": \"电力、热力、燃气及水生产和供应业\"\n"
            + "                            },\n"
            + "                            {\n"
            + "                                \"id\": \"21\",\n"
            + "                                \"name\": \"水利、环境和公共设施管理业\"\n"
            + "                            },\n"
            + "                            {\n"
            + "                                \"id\": \"22\",\n"
            + "                                \"name\": \"居民服务、修理和其他服务业\"\n"
            + "                            },\n"
            + "                            {\n"
            + "                                \"id\": \"11\",\n"
            + "                                \"name\": \"农、林、牧、渔业\"\n"
            + "                            },\n"
            + "                            {\n"
            + "                                \"id\": \"23\",\n"
            + "                                \"name\": \"采矿业\"\n"
            + "                            },\n"
            + "                            {\n"
            + "                                \"id\": \"24\",\n"
            + "                                \"name\": \"国际组织\"\n"
            + "                            },\n"
            + "                            {\n"
            + "                                \"id\": \"15\",\n"
            + "                                \"name\": \"其他\"\n"
            + "                            }\n"
            + "                        ]\n"
            + "                    },\n"
            + "                    {\n"
            + "                        \"id\": \"scaleId\",\n"
            + "                        \"name\": \"规模\",\n"
            + "                        \"required\": 1,\n"
            + "                        \"type\": 3,\n"
            + "                        \"multiple\": 0,\n"
            + "                        \"options\": [\n"
            + "                            {\n"
            + "                                \"id\": \"1\",\n"
            + "                                \"name\": \"21-99人\"\n"
            + "                            },\n"
            + "                            {\n"
            + "                                \"id\": \"2\",\n"
            + "                                \"name\": \"100-499人\"\n"
            + "                            },\n"
            + "                            {\n"
            + "                                \"id\": \"3\",\n"
            + "                                \"name\": \"500-999人\"\n"
            + "                            },\n"
            + "                            {\n"
            + "                                \"id\": \"4\",\n"
            + "                                \"name\": \"1000-9999人\"\n"
            + "                            },\n"
            + "                            {\n"
            + "                                \"id\": \"5\",\n"
            + "                                \"name\": \"10000人以上\"\n"
            + "                            }\n"
            + "                        ]\n"
            + "                    },\n"
            + "                    {\n"
            + "                        \"id\": \"jobType\",\n"
            + "                        \"name\": \"您的职级\",\n"
            + "                        \"required\": 1,\n"
            + "                        \"type\": 3,\n"
            + "                        \"multiple\": 0,\n"
            + "                        \"options\": [\n"
            + "                            {\n"
            + "                                \"id\": \"1\",\n"
            + "                                \"name\": \"总裁/总经理/CEO\"\n"
            + "                            },\n"
            + "                            {\n"
            + "                                \"id\": \"2\",\n"
            + "                                \"name\": \"副总裁/副总经理/VP\"\n"
            + "                            },\n"
            + "                            {\n"
            + "                                \"id\": \"3\",\n"
            + "                                \"name\": \"总监/主管/经理\"\n"
            + "                            },\n"
            + "                            {\n"
            + "                                \"id\": \"4\",\n"
            + "                                \"name\": \"员工/专员/执行\"\n"
            + "                            },\n"
            + "                            {\n"
            + "                                \"id\": \"5\",\n"
            + "                                \"name\": \"其他\"\n"
            + "                            }\n"
            + "                        ]\n"
            + "                    },\n"
            + "                    {\n"
            + "                        \"id\": \"departmentType\",\n"
            + "                        \"name\": \"您的部门\",\n"
            + "                        \"required\": 1,\n"
            + "                        \"type\": 3,\n"
            + "                        \"multiple\": 0,\n"
            + "                        \"options\": [\n"
            + "                            {\n"
            + "                                \"id\": \"1\",\n"
            + "                                \"name\": \"总经办\"\n"
            + "                            },\n"
            + "                            {\n"
            + "                                \"id\": \"2\",\n"
            + "                                \"name\": \"技术/IT/研发\"\n"
            + "                            },\n"
            + "                            {\n"
            + "                                \"id\": \"3\",\n"
            + "                                \"name\": \"产品/设计\"\n"
            + "                            },\n"
            + "                            {\n"
            + "                                \"id\": \"4\",\n"
            + "                                \"name\": \"销售/市场/运营\"\n"
            + "                            },\n"
            + "                            {\n"
            + "                                \"id\": \"5\",\n"
            + "                                \"name\": \"人事/财务/行政\"\n"
            + "                            },\n"
            + "                            {\n"
            + "                                \"id\": \"6\",\n"
            + "                                \"name\": \"资源/仓储/采购\"\n"
            + "                            },\n"
            + "                            {\n"
            + "                                \"id\": \"7\",\n"
            + "                                \"name\": \"其他\"\n"
            + "                            }\n"
            + "                        ]\n"
            + "                    }\n"
            + "                ]";

        return new ObjectMapper().readValue(res, List.class);
    }

    @BizQuery("GetGlobalMetaBak")
    public Map<String, Object> getGlobalMetaBak() throws JsonProcessingException {

        String returnData = "{\n"
            + "        \"config\": {\n"
            + "            \"HTTP_SERVER\": \"https://chatmq.mingdao.com\",\n"
            + "            \"SERVER_NAME\": \"https://chat.mingdao.com\",\n"
            + "            \"GROUP_SERVER_NAME\": \"https://avatar.mingdao.com/avatar\",\n"
            + "            \"FilePath\": \"https://m1.mingdaoyun.cn/\",\n"
            + "            \"AttrPath\": \"https://p1.mingdaoyun.cn/UserAvatar/\"\n"
            + "        },\n"
            + "        \"VirtualPath\": \"/\",\n"
            + "        \"md.global\": {\n"
            + "            \"Config\": {\n"
            + "                \"AjaxApiUrl\": \"https://www.mingdao.com/api/\",\n"
            + "                \"WorkFlowUrl\": \"https://api.mingdao.com/workflow\",\n"
            + "                \"WsReportUrl\": \"https://api.mingdao.com/report\",\n"
            + "                \"FormOAUrl\": \"https://approval.mingdao.com\",\n"
            + "                \"HrCheckUrl\": \"https://check.mingdao.com/\",\n"
            + "                \"HrDossierUrl\": \"https://dossier.mingdao.com\",\n"
            + "                \"WebUrl\": \"https://www.mingdao.com/\",\n"
            + "                \"AppFileServer\": \"https://excelapi.mingdao.com/package/\",\n"
            + "                \"WorksheetDownUrl\": \"https://excelapi.mingdao.com\",\n"
            + "                \"DataPipelineUrl\": \"https://api.mingdao.com/datapipeline\",\n"
            + "                \"PluginRuntimeUrl\": \"https://alifile.mingdaocloud.com/p/www/\",\n"
            + "                \"PublicFormWebUrl\": \"\",\n"
            + "                \"IntegrationAPIUrl\": \"https://api.mingdao.com/integration\",\n"
            + "                \"DocviewStartUrl\": \"https://docviewtp.mingdao.com/wv/WordViewer/request.pdf?WOPIsrc=https%3A%2F%2Fdocviewtp%2Emingdao%2Ecom%2Foh%2Fwopi%2Ffiles%2F%40%2FwFileId%3FwFileId%3D\",\n"
            + "                \"WpsUrl\": \"https://docview.mingdao.com/wps/docpreview\",\n"
            + "                \"ForbidSuites\": [],\n"
            + "                \"Logo\": \"https://p1.mingdaoyun.cn/ProjectLogo/default.png\",\n"
            + "                \"IsLocal\": false,\n"
            + "                \"CaptchaType\": 1,\n"
            + "                \"CaptchaAppId\": 2098831062,\n"
            + "                \"ServerTime\": \"2024-01-12 13:34:21\",\n"
            + "                \"DefaultLang\": \"zh-Hans\",\n"
            + "                \"MdNoticeServer\": \"https://updatenotice.mingdao.com\",\n"
            + "                \"WorkWXApp\": \"ww798a6bd29d298f01\"\n"
            + "            },\n"
            + "            \"SysSettings\": {\n"
            + "                \"enableCreateProject\": true\n"
            + "            },\n"
            + "            \"SystemAccounts\": {\n"
            + "                \"undefined\": {\n"
            + "                    \"accountId\": \"user-undefined\",\n"
            + "                    \"fullname\": \"未指定\",\n"
            + "                    \"avatar\": \"https://p1.mingdaoyun.cn/UserAvatar/undefined.gif?imageView2/1/w/48/h/48/q/90\"\n"
            + "                }\n"
            + "            },\n"
            + "            \"APPInfo\": {\n"
            + "                \"taskAppID\": \"ab99d0bb-3249-46f9-8a60-6952cb76cac2\",\n"
            + "                \"taskFolderAppID\": \"66d51996-6a56-41ba-be15-0d388b548f00\",\n"
            + "                \"calendarAppID\": \"42c96ef1-3ab6-4269-9824-e21436f34a38\",\n"
            + "                \"worksheetAppID\": \"1e31c859-1605-4d8d-b3be-437ff871f02d\",\n"
            + "                \"worksheetRowAppID\": \"c8bc1b25-2bbe-4334-b1e3-be1b207f3126\"\n"
            + "            },\n"
            + "            \"FileStoreConfig\": {\n"
            + "                \"uploadHost\": \"https://upload.qiniup.com/\",\n"
            + "                \"documentHost\": \"https://d1.mingdaoyun.cn/\",\n"
            + "                \"pictureHost\": \"https://p1.mingdaoyun.cn/\",\n"
            + "                \"mediaHost\": \"https://m1.mingdaoyun.cn/\",\n"
            + "                \"pubHost\": \"https://fp1.mingdaoyun.cn/\"\n"
            + "            },\n"
            + "            \"Versions\": [\n"
            + "                {\n"
            + "                    \"VersionIdV2\": \"-1\",\n"
            + "                    \"Name\": \"免费版\",\n"
            + "                    \"Products\": [\n"
            + "                        {\n"
            + "                            \"Type\": \"2\",\n"
            + "                            \"ProductType\": 1\n"
            + "                        },\n"
            + "                        {\n"
            + "                            \"Type\": \"2\",\n"
            + "                            \"ProductType\": 21\n"
            + "                        },\n"
            + "                        {\n"
            + "                            \"Type\": \"1\",\n"
            + "                            \"ProductType\": 2\n"
            + "                        },\n"
            + "                        {\n"
            + "                            \"Type\": \"2\",\n"
            + "                            \"ProductType\": 3\n"
            + "                        },\n"
            + "                        {\n"
            + "                            \"Type\": \"2\",\n"
            + "                            \"ProductType\": 4\n"
            + "                        },\n"
            + "                        {\n"
            + "                            \"Type\": \"2\",\n"
            + "                            \"ProductType\": 22\n"
            + "                        },\n"
            + "                        {\n"
            + "                            \"Type\": \"2\",\n"
            + "                            \"ProductType\": 5\n"
            + "                        },\n"
            + "                        {\n"
            + "                            \"Type\": \"2\",\n"
            + "                            \"ProductType\": 6\n"
            + "                        },\n"
            + "                        {\n"
            + "                            \"Type\": \"2\",\n"
            + "                            \"ProductType\": 7\n"
            + "                        },\n"
            + "                        {\n"
            + "                            \"Type\": \"2\",\n"
            + "                            \"ProductType\": 8\n"
            + "                        },\n"
            + "                        {\n"
            + "                            \"Type\": \"2\",\n"
            + "                            \"ProductType\": 10\n"
            + "                        },\n"
            + "                        {\n"
            + "                            \"Type\": \"1\",\n"
            + "                            \"ProductType\": 11\n"
            + "                        },\n"
            + "                        {\n"
            + "                            \"Type\": \"2\",\n"
            + "                            \"ProductType\": 12\n"
            + "                        },\n"
            + "                        {\n"
            + "                            \"Type\": \"2\",\n"
            + "                            \"ProductType\": 13\n"
            + "                        },\n"
            + "                        {\n"
            + "                            \"Type\": \"2\",\n"
            + "                            \"ProductType\": 14\n"
            + "                        },\n"
            + "                        {\n"
            + "                            \"Type\": \"2\",\n"
            + "                            \"ProductType\": 15\n"
            + "                        },\n"
            + "                        {\n"
            + "                            \"Type\": \"2\",\n"
            + "                            \"ProductType\": 23\n"
            + "                        },\n"
            + "                        {\n"
            + "                            \"Type\": \"2\",\n"
            + "                            \"ProductType\": 16\n"
            + "                        },\n"
            + "                        {\n"
            + "                            \"Type\": \"2\",\n"
            + "                            \"ProductType\": 17\n"
            + "                        },\n"
            + "                        {\n"
            + "                            \"Type\": \"2\",\n"
            + "                            \"ProductType\": 18\n"
            + "                        },\n"
            + "                        {\n"
            + "                            \"Type\": \"2\",\n"
            + "                            \"ProductType\": 19\n"
            + "                        },\n"
            + "                        {\n"
            + "                            \"Type\": \"2\",\n"
            + "                            \"ProductType\": 20\n"
            + "                        },\n"
            + "                        {\n"
            + "                            \"Type\": \"2\",\n"
            + "                            \"ProductType\": 24\n"
            + "                        },\n"
            + "                        {\n"
            + "                            \"Type\": \"2\",\n"
            + "                            \"ProductType\": 25\n"
            + "                        },\n"
            + "                        {\n"
            + "                            \"Type\": \"2\",\n"
            + "                            \"ProductType\": 26\n"
            + "                        },\n"
            + "                        {\n"
            + "                            \"Type\": \"2\",\n"
            + "                            \"ProductType\": 27\n"
            + "                        },\n"
            + "                        {\n"
            + "                            \"Type\": \"2\",\n"
            + "                            \"ProductType\": 28\n"
            + "                        },\n"
            + "                        {\n"
            + "                            \"Type\": \"2\",\n"
            + "                            \"ProductType\": 29\n"
            + "                        },\n"
            + "                        {\n"
            + "                            \"Type\": \"2\",\n"
            + "                            \"ProductType\": 30\n"
            + "                        },\n"
            + "                        {\n"
            + "                            \"Type\": \"2\",\n"
            + "                            \"ProductType\": 31\n"
            + "                        },\n"
            + "                        {\n"
            + "                            \"Type\": \"2\",\n"
            + "                            \"ProductType\": 32\n"
            + "                        },\n"
            + "                        {\n"
            + "                            \"Type\": \"2\",\n"
            + "                            \"ProductType\": 33\n"
            + "                        },\n"
            + "                        {\n"
            + "                            \"Type\": \"2\",\n"
            + "                            \"ProductType\": 34\n"
            + "                        },\n"
            + "                        {\n"
            + "                            \"Type\": \"2\",\n"
            + "                            \"ProductType\": 0\n"
            + "                        }\n"
            + "                    ]\n"
            + "                },\n"
            + "                {\n"
            + "                    \"VersionIdV2\": \"0\",\n"
            + "                    \"Name\": \"单应用版\",\n"
            + "                    \"Products\": [\n"
            + "                        {\n"
            + "                            \"Type\": \"2\",\n"
            + "                            \"ProductType\": 1\n"
            + "                        },\n"
            + "                        {\n"
            + "                            \"Type\": \"1\",\n"
            + "                            \"ProductType\": 21\n"
            + "                        },\n"
            + "                        {\n"
            + "                            \"Type\": \"1\",\n"
            + "                            \"ProductType\": 2\n"
            + "                        },\n"
            + "                        {\n"
            + "                            \"Type\": \"1\",\n"
            + "                            \"ProductType\": 3\n"
            + "                        },\n"
            + "                        {\n"
            + "                            \"Type\": \"1\",\n"
            + "                            \"ProductType\": 4\n"
            + "                        },\n"
            + "                        {\n"
            + "                            \"Type\": \"2\",\n"
            + "                            \"ProductType\": 22\n"
            + "                        },\n"
            + "                        {\n"
            + "                            \"Type\": \"1\",\n"
            + "                            \"ProductType\": 5\n"
            + "                        },\n"
            + "                        {\n"
            + "                            \"Type\": \"2\",\n"
            + "                            \"ProductType\": 6\n"
            + "                        },\n"
            + "                        {\n"
            + "                            \"Type\": \"1\",\n"
            + "                            \"ProductType\": 7\n"
            + "                        },\n"
            + "                        {\n"
            + "                            \"Type\": \"2\",\n"
            + "                            \"ProductType\": 8\n"
            + "                        },\n"
            + "                        {\n"
            + "                            \"Type\": \"1\",\n"
            + "                            \"ProductType\": 10\n"
            + "                        },\n"
            + "                        {\n"
            + "                            \"Type\": \"1\",\n"
            + "                            \"ProductType\": 11\n"
            + "                        },\n"
            + "                        {\n"
            + "                            \"Type\": \"1\",\n"
            + "                            \"ProductType\": 12\n"
            + "                        },\n"
            + "                        {\n"
            + "                            \"Type\": \"2\",\n"
            + "                            \"ProductType\": 13\n"
            + "                        },\n"
            + "                        {\n"
            + "                            \"Type\": \"2\",\n"
            + "                            \"ProductType\": 14\n"
            + "                        },\n"
            + "                        {\n"
            + "                            \"Type\": \"1\",\n"
            + "                            \"ProductType\": 15\n"
            + "                        },\n"
            + "                        {\n"
            + "                            \"Type\": \"2\",\n"
            + "                            \"ProductType\": 23\n"
            + "                        },\n"
            + "                        {\n"
            + "                            \"Type\": \"1\",\n"
            + "                            \"ProductType\": 16\n"
            + "                        },\n"
            + "                        {\n"
            + "                            \"Type\": \"2\",\n"
            + "                            \"ProductType\": 17\n"
            + "                        },\n"
            + "                        {\n"
            + "                            \"Type\": \"1\",\n"
            + "                            \"ProductType\": 18\n"
            + "                        },\n"
            + "                        {\n"
            + "                            \"Type\": \"1\",\n"
            + "                            \"ProductType\": 19\n"
            + "                        },\n"
            + "                        {\n"
            + "                            \"Type\": \"2\",\n"
            + "                            \"ProductType\": 20\n"
            + "                        },\n"
            + "                        {\n"
            + "                            \"Type\": \"2\",\n"
            + "                            \"ProductType\": 24\n"
            + "                        },\n"
            + "                        {\n"
            + "                            \"Type\": \"2\",\n"
            + "                            \"ProductType\": 25\n"
            + "                        },\n"
            + "                        {\n"
            + "                            \"Type\": \"2\",\n"
            + "                            \"ProductType\": 26\n"
            + "                        },\n"
            + "                        {\n"
            + "                            \"Type\": \"2\",\n"
            + "                            \"ProductType\": 27\n"
            + "                        },\n"
            + "                        {\n"
            + "                            \"Type\": \"1\",\n"
            + "                            \"ProductType\": 28\n"
            + "                        },\n"
            + "                        {\n"
            + "                            \"Type\": \"2\",\n"
            + "                            \"ProductType\": 29\n"
            + "                        },\n"
            + "                        {\n"
            + "                            \"Type\": \"1\",\n"
            + "                            \"ProductType\": 30\n"
            + "                        },\n"
            + "                        {\n"
            + "                            \"Type\": \"1\",\n"
            + "                            \"ProductType\": 31\n"
            + "                        },\n"
            + "                        {\n"
            + "                            \"Type\": \"2\",\n"
            + "                            \"ProductType\": 32\n"
            + "                        },\n"
            + "                        {\n"
            + "                            \"Type\": \"2\",\n"
            + "                            \"ProductType\": 33\n"
            + "                        },\n"
            + "                        {\n"
            + "                            \"Type\": \"2\",\n"
            + "                            \"ProductType\": 34\n"
            + "                        },\n"
            + "                        {\n"
            + "                            \"Type\": \"2\",\n"
            + "                            \"ProductType\": 0\n"
            + "                        }\n"
            + "                    ]\n"
            + "                },\n"
            + "                {\n"
            + "                    \"VersionIdV2\": \"1\",\n"
            + "                    \"Name\": \"标准版\",\n"
            + "                    \"Products\": [\n"
            + "                        {\n"
            + "                            \"Type\": \"2\",\n"
            + "                            \"ProductType\": 1\n"
            + "                        },\n"
            + "                        {\n"
            + "                            \"Type\": \"1\",\n"
            + "                            \"ProductType\": 21\n"
            + "                        },\n"
            + "                        {\n"
            + "                            \"Type\": \"1\",\n"
            + "                            \"ProductType\": 2\n"
            + "                        },\n"
            + "                        {\n"
            + "                            \"Type\": \"1\",\n"
            + "                            \"ProductType\": 3\n"
            + "                        },\n"
            + "                        {\n"
            + "                            \"Type\": \"1\",\n"
            + "                            \"ProductType\": 4\n"
            + "                        },\n"
            + "                        {\n"
            + "                            \"Type\": \"2\",\n"
            + "                            \"ProductType\": 22\n"
            + "                        },\n"
            + "                        {\n"
            + "                            \"Type\": \"1\",\n"
            + "                            \"ProductType\": 5\n"
            + "                        },\n"
            + "                        {\n"
            + "                            \"Type\": \"2\",\n"
            + "                            \"ProductType\": 6\n"
            + "                        },\n"
            + "                        {\n"
            + "                            \"Type\": \"1\",\n"
            + "                            \"ProductType\": 7\n"
            + "                        },\n"
            + "                        {\n"
            + "                            \"Type\": \"2\",\n"
            + "                            \"ProductType\": 8\n"
            + "                        },\n"
            + "                        {\n"
            + "                            \"Type\": \"1\",\n"
            + "                            \"ProductType\": 10\n"
            + "                        },\n"
            + "                        {\n"
            + "                            \"Type\": \"1\",\n"
            + "                            \"ProductType\": 11\n"
            + "                        },\n"
            + "                        {\n"
            + "                            \"Type\": \"1\",\n"
            + "                            \"ProductType\": 12\n"
            + "                        },\n"
            + "                        {\n"
            + "                            \"Type\": \"2\",\n"
            + "                            \"ProductType\": 13\n"
            + "                        },\n"
            + "                        {\n"
            + "                            \"Type\": \"2\",\n"
            + "                            \"ProductType\": 14\n"
            + "                        },\n"
            + "                        {\n"
            + "                            \"Type\": \"1\",\n"
            + "                            \"ProductType\": 15\n"
            + "                        },\n"
            + "                        {\n"
            + "                            \"Type\": \"2\",\n"
            + "                            \"ProductType\": 23\n"
            + "                        },\n"
            + "                        {\n"
            + "                            \"Type\": \"1\",\n"
            + "                            \"ProductType\": 16\n"
            + "                        },\n"
            + "                        {\n"
            + "                            \"Type\": \"2\",\n"
            + "                            \"ProductType\": 17\n"
            + "                        },\n"
            + "                        {\n"
            + "                            \"Type\": \"1\",\n"
            + "                            \"ProductType\": 18\n"
            + "                        },\n"
            + "                        {\n"
            + "                            \"Type\": \"1\",\n"
            + "                            \"ProductType\": 19\n"
            + "                        },\n"
            + "                        {\n"
            + "                            \"Type\": \"2\",\n"
            + "                            \"ProductType\": 20\n"
            + "                        },\n"
            + "                        {\n"
            + "                            \"Type\": \"2\",\n"
            + "                            \"ProductType\": 24\n"
            + "                        },\n"
            + "                        {\n"
            + "                            \"Type\": \"2\",\n"
            + "                            \"ProductType\": 25\n"
            + "                        },\n"
            + "                        {\n"
            + "                            \"Type\": \"2\",\n"
            + "                            \"ProductType\": 26\n"
            + "                        },\n"
            + "                        {\n"
            + "                            \"Type\": \"2\",\n"
            + "                            \"ProductType\": 27\n"
            + "                        },\n"
            + "                        {\n"
            + "                            \"Type\": \"1\",\n"
            + "                            \"ProductType\": 28\n"
            + "                        },\n"
            + "                        {\n"
            + "                            \"Type\": \"2\",\n"
            + "                            \"ProductType\": 29\n"
            + "                        },\n"
            + "                        {\n"
            + "                            \"Type\": \"1\",\n"
            + "                            \"ProductType\": 30\n"
            + "                        },\n"
            + "                        {\n"
            + "                            \"Type\": \"1\",\n"
            + "                            \"ProductType\": 31\n"
            + "                        },\n"
            + "                        {\n"
            + "                            \"Type\": \"2\",\n"
            + "                            \"ProductType\": 32\n"
            + "                        },\n"
            + "                        {\n"
            + "                            \"Type\": \"2\",\n"
            + "                            \"ProductType\": 33\n"
            + "                        },\n"
            + "                        {\n"
            + "                            \"Type\": \"2\",\n"
            + "                            \"ProductType\": 34\n"
            + "                        },\n"
            + "                        {\n"
            + "                            \"Type\": \"2\",\n"
            + "                            \"ProductType\": 0\n"
            + "                        }\n"
            + "                    ]\n"
            + "                },\n"
            + "                {\n"
            + "                    \"VersionIdV2\": \"2\",\n"
            + "                    \"Name\": \"专业版\",\n"
            + "                    \"Products\": [\n"
            + "                        {\n"
            + "                            \"Type\": \"1\",\n"
            + "                            \"ProductType\": 1\n"
            + "                        },\n"
            + "                        {\n"
            + "                            \"Type\": \"1\",\n"
            + "                            \"ProductType\": 21\n"
            + "                        },\n"
            + "                        {\n"
            + "                            \"Type\": \"1\",\n"
            + "                            \"ProductType\": 2\n"
            + "                        },\n"
            + "                        {\n"
            + "                            \"Type\": \"1\",\n"
            + "                            \"ProductType\": 3\n"
            + "                        },\n"
            + "                        {\n"
            + "                            \"Type\": \"1\",\n"
            + "                            \"ProductType\": 4\n"
            + "                        },\n"
            + "                        {\n"
            + "                            \"Type\": \"2\",\n"
            + "                            \"ProductType\": 22\n"
            + "                        },\n"
            + "                        {\n"
            + "                            \"Type\": \"1\",\n"
            + "                            \"ProductType\": 5\n"
            + "                        },\n"
            + "                        {\n"
            + "                            \"Type\": \"2\",\n"
            + "                            \"ProductType\": 6\n"
            + "                        },\n"
            + "                        {\n"
            + "                            \"Type\": \"1\",\n"
            + "                            \"ProductType\": 7\n"
            + "                        },\n"
            + "                        {\n"
            + "                            \"Type\": \"1\",\n"
            + "                            \"ProductType\": 8\n"
            + "                        },\n"
            + "                        {\n"
            + "                            \"Type\": \"1\",\n"
            + "                            \"ProductType\": 10\n"
            + "                        },\n"
            + "                        {\n"
            + "                            \"Type\": \"1\",\n"
            + "                            \"ProductType\": 11\n"
            + "                        },\n"
            + "                        {\n"
            + "                            \"Type\": \"1\",\n"
            + "                            \"ProductType\": 12\n"
            + "                        },\n"
            + "                        {\n"
            + "                            \"Type\": \"1\",\n"
            + "                            \"ProductType\": 13\n"
            + "                        },\n"
            + "                        {\n"
            + "                            \"Type\": \"1\",\n"
            + "                            \"ProductType\": 14\n"
            + "                        },\n"
            + "                        {\n"
            + "                            \"Type\": \"1\",\n"
            + "                            \"ProductType\": 15\n"
            + "                        },\n"
            + "                        {\n"
            + "                            \"Type\": \"1\",\n"
            + "                            \"ProductType\": 23\n"
            + "                        },\n"
            + "                        {\n"
            + "                            \"Type\": \"1\",\n"
            + "                            \"ProductType\": 16\n"
            + "                        },\n"
            + "                        {\n"
            + "                            \"Type\": \"1\",\n"
            + "                            \"ProductType\": 17\n"
            + "                        },\n"
            + "                        {\n"
            + "                            \"Type\": \"1\",\n"
            + "                            \"ProductType\": 18\n"
            + "                        },\n"
            + "                        {\n"
            + "                            \"Type\": \"1\",\n"
            + "                            \"ProductType\": 19\n"
            + "                        },\n"
            + "                        {\n"
            + "                            \"Type\": \"1\",\n"
            + "                            \"ProductType\": 20\n"
            + "                        },\n"
            + "                        {\n"
            + "                            \"Type\": \"1\",\n"
            + "                            \"ProductType\": 24\n"
            + "                        },\n"
            + "                        {\n"
            + "                            \"Type\": \"1\",\n"
            + "                            \"ProductType\": 25\n"
            + "                        },\n"
            + "                        {\n"
            + "                            \"Type\": \"2\",\n"
            + "                            \"ProductType\": 26\n"
            + "                        },\n"
            + "                        {\n"
            + "                            \"Type\": \"1\",\n"
            + "                            \"ProductType\": 27\n"
            + "                        },\n"
            + "                        {\n"
            + "                            \"Type\": \"1\",\n"
            + "                            \"ProductType\": 28\n"
            + "                        },\n"
            + "                        {\n"
            + "                            \"Type\": \"2\",\n"
            + "                            \"ProductType\": 29\n"
            + "                        },\n"
            + "                        {\n"
            + "                            \"Type\": \"1\",\n"
            + "                            \"ProductType\": 30\n"
            + "                        },\n"
            + "                        {\n"
            + "                            \"Type\": \"1\",\n"
            + "                            \"ProductType\": 31\n"
            + "                        },\n"
            + "                        {\n"
            + "                            \"Type\": \"2\",\n"
            + "                            \"ProductType\": 32\n"
            + "                        },\n"
            + "                        {\n"
            + "                            \"Type\": \"1\",\n"
            + "                            \"ProductType\": 33\n"
            + "                        },\n"
            + "                        {\n"
            + "                            \"Type\": \"1\",\n"
            + "                            \"ProductType\": 34\n"
            + "                        },\n"
            + "                        {\n"
            + "                            \"Type\": \"1\",\n"
            + "                            \"ProductType\": 0\n"
            + "                        }\n"
            + "                    ]\n"
            + "                },\n"
            + "                {\n"
            + "                    \"VersionIdV2\": \"3\",\n"
            + "                    \"Name\": \"旗舰版\",\n"
            + "                    \"Products\": [\n"
            + "                        {\n"
            + "                            \"Type\": \"1\",\n"
            + "                            \"ProductType\": 1\n"
            + "                        },\n"
            + "                        {\n"
            + "                            \"Type\": \"1\",\n"
            + "                            \"ProductType\": 21\n"
            + "                        },\n"
            + "                        {\n"
            + "                            \"Type\": \"1\",\n"
            + "                            \"ProductType\": 2\n"
            + "                        },\n"
            + "                        {\n"
            + "                            \"Type\": \"1\",\n"
            + "                            \"ProductType\": 3\n"
            + "                        },\n"
            + "                        {\n"
            + "                            \"Type\": \"1\",\n"
            + "                            \"ProductType\": 4\n"
            + "                        },\n"
            + "                        {\n"
            + "                            \"Type\": \"1\",\n"
            + "                            \"ProductType\": 22\n"
            + "                        },\n"
            + "                        {\n"
            + "                            \"Type\": \"1\",\n"
            + "                            \"ProductType\": 5\n"
            + "                        },\n"
            + "                        {\n"
            + "                            \"Type\": \"1\",\n"
            + "                            \"ProductType\": 6\n"
            + "                        },\n"
            + "                        {\n"
            + "                            \"Type\": \"1\",\n"
            + "                            \"ProductType\": 7\n"
            + "                        },\n"
            + "                        {\n"
            + "                            \"Type\": \"1\",\n"
            + "                            \"ProductType\": 8\n"
            + "                        },\n"
            + "                        {\n"
            + "                            \"Type\": \"1\",\n"
            + "                            \"ProductType\": 10\n"
            + "                        },\n"
            + "                        {\n"
            + "                            \"Type\": \"1\",\n"
            + "                            \"ProductType\": 11\n"
            + "                        },\n"
            + "                        {\n"
            + "                            \"Type\": \"1\",\n"
            + "                            \"ProductType\": 12\n"
            + "                        },\n"
            + "                        {\n"
            + "                            \"Type\": \"1\",\n"
            + "                            \"ProductType\": 13\n"
            + "                        },\n"
            + "                        {\n"
            + "                            \"Type\": \"1\",\n"
            + "                            \"ProductType\": 14\n"
            + "                        },\n"
            + "                        {\n"
            + "                            \"Type\": \"1\",\n"
            + "                            \"ProductType\": 15\n"
            + "                        },\n"
            + "                        {\n"
            + "                            \"Type\": \"1\",\n"
            + "                            \"ProductType\": 23\n"
            + "                        },\n"
            + "                        {\n"
            + "                            \"Type\": \"1\",\n"
            + "                            \"ProductType\": 16\n"
            + "                        },\n"
            + "                        {\n"
            + "                            \"Type\": \"1\",\n"
            + "                            \"ProductType\": 17\n"
            + "                        },\n"
            + "                        {\n"
            + "                            \"Type\": \"1\",\n"
            + "                            \"ProductType\": 18\n"
            + "                        },\n"
            + "                        {\n"
            + "                            \"Type\": \"1\",\n"
            + "                            \"ProductType\": 19\n"
            + "                        },\n"
            + "                        {\n"
            + "                            \"Type\": \"1\",\n"
            + "                            \"ProductType\": 20\n"
            + "                        },\n"
            + "                        {\n"
            + "                            \"Type\": \"1\",\n"
            + "                            \"ProductType\": 24\n"
            + "                        },\n"
            + "                        {\n"
            + "                            \"Type\": \"1\",\n"
            + "                            \"ProductType\": 25\n"
            + "                        },\n"
            + "                        {\n"
            + "                            \"Type\": \"1\",\n"
            + "                            \"ProductType\": 26\n"
            + "                        },\n"
            + "                        {\n"
            + "                            \"Type\": \"1\",\n"
            + "                            \"ProductType\": 27\n"
            + "                        },\n"
            + "                        {\n"
            + "                            \"Type\": \"1\",\n"
            + "                            \"ProductType\": 28\n"
            + "                        },\n"
            + "                        {\n"
            + "                            \"Type\": \"1\",\n"
            + "                            \"ProductType\": 29\n"
            + "                        },\n"
            + "                        {\n"
            + "                            \"Type\": \"1\",\n"
            + "                            \"ProductType\": 30\n"
            + "                        },\n"
            + "                        {\n"
            + "                            \"Type\": \"1\",\n"
            + "                            \"ProductType\": 31\n"
            + "                        },\n"
            + "                        {\n"
            + "                            \"Type\": \"1\",\n"
            + "                            \"ProductType\": 32\n"
            + "                        },\n"
            + "                        {\n"
            + "                            \"Type\": \"1\",\n"
            + "                            \"ProductType\": 33\n"
            + "                        },\n"
            + "                        {\n"
            + "                            \"Type\": \"1\",\n"
            + "                            \"ProductType\": 34\n"
            + "                        },\n"
            + "                        {\n"
            + "                            \"Type\": \"1\",\n"
            + "                            \"ProductType\": 0\n"
            + "                        }\n"
            + "                    ]\n"
            + "                }\n"
            + "            ],\n"
            + "            \"Account\": {\n"
            + "                \"lang\": \"zh-Hans\",\n"
            + "                \"defaultLang\": \"zh-Hans\",\n"
            + "                \"accountId\": \"c42de15d-aa34-4922-a280-6bd1f5d9a16a\",\n"
            + "                \"fullname\": \"刘\",\n"
            + "                \"email\": \"<EMAIL>\",\n"
            + "                \"mobilePhone\": \"+*************\",\n"
            + "                \"avatarMiddle\": \"https://p1.mingdaoyun.cn/UserAvatar/default7.png?watermark/2/text/5YiY/font/5b6u6L2v6ZuF6buR/fontsize/700/fill/d2hpdGU=/dissolve/100/gravity/Center/dx/0/dy/0%7CimageView2/1/w/48/h/48/q/90\",\n"
            + "                \"avatar\": \"https://p1.mingdaoyun.cn/UserAvatar/default7.png?watermark/2/text/5YiY/font/5b6u6L2v6ZuF6buR/fontsize/700/fill/d2hpdGU=/dissolve/100/gravity/Center/dx/0/dy/0%7CimageView2/1/w/100/h/100/q/90\",\n"
            + "                \"createTime\": \"2023-12-29 18:58:12\",\n"
            + "                \"numLogin\": 0,\n"
            + "                \"companyName\": \"TeamA\",\n"
            + "                \"profession\": \"经理\",\n"
            +                   "\"superAdmin\": true,\n"
            + "                \"projects\": [\n"
            + "                    {\n"
            + "                        \"projectId\": \"78b9647a1abf44b1b376689d7298577b\",\n"
            + "                        \"companyName\": \"TeamA\",\n"
            + "                        \"effectiveDataPipelineJobCount\": 0,\n"
            + "                        \"limitDataPipelineJobCount\": 0,\n"
            + "                        \"effectiveDataPipelineRowCount\": 0,\n"
            + "                        \"limitDataPipelineRowCount\": 0,\n"
            + "                        \"isProjectAppManager\": true,\n"
            + "                        \"isProjectAdmin\": true,\n"
            + "                        \"isSuperAdmin\": true,\n"
            + "                        \"licenseType\": 0,\n"
            + "                        \"createAccountId\": \"c42de15d-aa34-4922-a280-6bd1f5d9a16a\",\n"
            + "                        \"paidCount\": 0,\n"
            + "                        \"isHrVisible\": false,\n"
            + "                        \"cannotCreateApp\": false,\n"
            + "                        \"enabledWatermark\": false\n"
            + "                    },\n"
            + "                    {\n"
            + "                        \"projectId\": \"78b9647a1abf44b1b376689d72985722\",\n"
            + "                        \"companyName\": \"TeamBBB\",\n"
            + "                        \"effectiveDataPipelineJobCount\": 0,\n"
            + "                        \"limitDataPipelineJobCount\": 0,\n"
            + "                        \"effectiveDataPipelineRowCount\": 0,\n"
            + "                        \"limitDataPipelineRowCount\": 0,\n"
            + "                        \"isProjectAppManager\": true,\n"
            + "                        \"isProjectAdmin\": true,\n"
            + "                        \"isSuperAdmin\": true,\n"
            + "                        \"licenseType\": 0,\n"
            + "                        \"createAccountId\": \"c42de15d-aa34-4922-a280-6bd1f5d9a16a\",\n"
            + "                        \"paidCount\": 0,\n"
            + "                        \"isHrVisible\": false,\n"
            + "                        \"cannotCreateApp\": false,\n"
            + "                        \"enabledWatermark\": false\n"
            + "                    }\n"
            + "                ],\n"
            + "                \"guideSettings\": {\n"
            + "                    \"mdAppLogin\": true,\n"
            + "                    \"accountMobilePhone\": false,\n"
            + "                    \"accountEmail\": false,\n"
            + "                    \"createCompany\": false,\n"
            + "                    \"calendarRepeat\": true\n"
            + "                },\n"
            + "                \"hrVisible\": false,\n"
            + "                \"isPortal\": false\n"
            + "            },\n"
            + "            \"PorjectColor\": [\n"
            + "                {\n"
            + "                    \"themeColor\": {\n"
            + "                        \"system\": [],\n"
            + "                        \"custom\": []\n"
            + "                    },\n"
            + "                    \"chartColor\": {\n"
            + "                        \"system\": [],\n"
            + "                        \"custom\": []\n"
            + "                    },\n"
            + "                    \"projectId\": \"855f6f44-293a-4b3c-a4a1-e2e18ae32910\"\n"
            + "                }\n"
            + "            ]\n"
            + "        }\n"
            + "    } ";
        return  new ObjectMapper().readValue(returnData, Map.class);
    }
}
