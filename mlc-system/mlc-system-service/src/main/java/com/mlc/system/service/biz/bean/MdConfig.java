package com.mlc.system.service.biz.bean;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@JsonAutoDetect(fieldVisibility = JsonAutoDetect.Visibility.ANY,getterVisibility = JsonAutoDetect.Visibility.NONE)
public class MdConfig {

    private String AjaxApiUrl;
    private String WorkFlowUrl;
    private String WsReportUrl;
    private String FormOAUrl;
    private String HrCheckUrl;
    private String HrDossierUrl;
    private String WebUrl;
    private String AppFileServer;
    private String WorksheetDownUrl;
    private String DataPipelineUrl;
    private String PluginRuntimeUrl;
    private String PublicFormWebUrl;
    private String IntegrationAPIUrl;
    private String DocviewStartUrl;
    private String WpsUrl;
    private String[] ForbidSuites;
    private String Logo;
    private boolean IsLocal;
    private int CaptchaType;
    private int CaptchaAppId;
    private String DefaultLang;
    private int DefaultTimeZone;
    private String ServerTime;
    private String MdNoticeServer;
    private String WorkWXApp;
    @JsonProperty("IsPlatformLocal")
    private boolean isPlatformLocal;
    private String Version;

}
