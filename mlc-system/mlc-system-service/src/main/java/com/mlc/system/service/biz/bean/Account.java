package com.mlc.system.service.biz.bean;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.ArrayList;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;


@Data
@Accessors(chain = true)
public class Account {

    private String lang;
    private boolean langModified;
    private int timeZone;
    private String accountId;
    private String fullname;
    private String email;
    private String mobilePhone;
    private String avatarMiddle;
    private String avatar;
    private String createTime;
    private int numLogin;
    private String companyName;
    private String profession;
    private boolean superAdmin;
    private List<Project> projects = new ArrayList<>();
    private GuideSettings guideSettings;
    @JsonProperty("isPortal")
    private boolean isPortal;
}
