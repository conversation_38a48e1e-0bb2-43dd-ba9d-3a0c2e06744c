package com.mlc.system.service.beans.captcha;


import io.nop.api.core.annotations.data.DataBean;
import io.nop.api.core.annotations.meta.PropMeta;
import lombok.Getter;
import lombok.Setter;


/**
 * 验证码验证
 */
@DataBean
@Getter
@Setter
public class CaptchaCheckerRequest {

    /**
     * 项目id
     */
    private String projectId;

    /**
     * 是否需要验证
     */
    private String isNoneVerification;

    /**
     * 获取 redis 中验证码的唯一标识
     */
    private String randStr;

    /**
     * 图形验证码里面的内容
     */
    private String ticket;

    /**
     * 验证码类型
     */
    private Integer captchaType;

    /**
     * 密码校验
     */
    private String password;


    @PropMeta(mandatory = true)
    public void setTicket(String ticket) {
        this.ticket = ticket;
    }

    @PropMeta(mandatory = true)
    public void setRandStr(String randStr) {
        this.randStr = randStr;
    }

    @PropMeta(mandatory = true)
    public void setPassword(String password) {
        this.password = password;
    }
}
