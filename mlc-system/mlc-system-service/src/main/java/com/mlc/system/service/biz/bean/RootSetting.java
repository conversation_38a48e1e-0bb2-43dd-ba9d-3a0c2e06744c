package com.mlc.system.service.biz.bean;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@JsonAutoDetect(fieldVisibility = JsonAutoDetect.Visibility.ANY,getterVisibility = JsonAutoDetect.Visibility.NONE)
public class RootSetting {

    private LevelConfig config;

    private String VirtualPath;

    @JsonProperty("md.global")
    private MdGlobal mdGlobal;

}
