package com.mlc.system.service.beans.captcha;


import lombok.Data;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.Accessors;


/**
 * 验证码生成
 */
@Data
@RequiredArgsConstructor(staticName = "of")
@Accessors(chain = true)
public class CaptchaGenRespon {

    /**
     * 验证码code,用于获取 redis 中的验证码
     */
    @NonNull
    private String code;
    /**
     * 验证码图片base64
     */
    @NonNull
    private String imgConvert;

}
