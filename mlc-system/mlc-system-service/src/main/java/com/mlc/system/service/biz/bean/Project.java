package com.mlc.system.service.biz.bean;

import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class Project {

    private String projectId;
    private String companyName;
    private Boolean isSuperAdmin;
    private int effectiveDataPipelineJobCount;
    private int limitDataPipelineJobCount;
    private int effectiveDataPipelineRowCount;
    private int limitDataPipelineRowCount;
    private int licenseType;
    private String createAccountId;
    private int paidCount;
    private boolean cannotCreateApp;
    private boolean enabledWatermark;
    private boolean allowAPIIntegration;
    private boolean allowDataPipeline;
    private boolean allowPlugin;
}
