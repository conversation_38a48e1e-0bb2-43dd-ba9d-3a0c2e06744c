package com.mlc.system.service.biz.bean;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@JsonAutoDetect(fieldVisibility = JsonAutoDetect.Visibility.ANY,getterVisibility = JsonAutoDetect.Visibility.NONE)
public class MdGlobal {
    
    private MdConfig Config;
    private Map<String, SystemAccounts> SystemAccounts = new HashMap<>();
    private Map<String, Object> SysSettings = new HashMap<>();
    private AppInfo APPInfo;
    private FileStoreConfig FileStoreConfig;
    private Account Account;
    private List<PorjectColor> PorjectColor = new ArrayList<>();

    public MdGlobal() {
        SysSettings.put("worksheetRowRecycleDays", "60");
        SysSettings.put("enableMobilePhoneRegister", true);
        SysSettings.put("enableEmailRegister", true);
    }
}
