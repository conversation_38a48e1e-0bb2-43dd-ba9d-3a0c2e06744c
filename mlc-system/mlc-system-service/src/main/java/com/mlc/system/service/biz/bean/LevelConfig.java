package com.mlc.system.service.biz.bean;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@JsonAutoDetect(fieldVisibility = JsonAutoDetect.Visibility.ANY,getterVisibility = JsonAutoDetect.Visibility.NONE)
public class LevelConfig {

    private String HTTP_SERVER;
    private String SERVER_NAME;
    private String GROUP_SERVER_NAME;
    private String FilePath;
    private String AttrPath;

}
