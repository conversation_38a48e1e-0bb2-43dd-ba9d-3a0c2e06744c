<?xml version="1.0" encoding="UTF-8" ?>
<beans x:schema="/nop/schema/beans.xdef" xmlns:x="/nop/schema/xdsl.xdef"
       xmlns="http://www.springframework.org/schema/beans" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
        http://www.springframework.org/schema/beans/spring-beans-2.5.xsd">

    <import resource="_dao.beans.xml"/>

    <import resource="_service.beans.xml"/>

    <bean id="com.mlc.workflow.service.biz.ProcessBizModel" ioc:type="@bean:id" ioc:default="true"/>
    <bean id="com.mlc.workflow.service.biz.ProcessNodeBizModel" ioc:type="@bean:id" ioc:default="true"/>

    <bean id="com.mlc.workflow.service.landing.core.impl.NodeManagementServiceImpl" ioc:type="@bean:id" ioc:default="true"/>
</beans>