package com.mlc.workflow.service.landing.query.strategy.impl;

import com.mlc.base.common.enums.workflow.NodeTypeEnum;
import com.mlc.workflow.core.editor.model.BaseNodeProperties;
import com.mlc.workflow.core.editor.runtime.beans.ProcessNode;
import com.mlc.workflow.service.landing.query.strategy.AbstractNodeQueryStrategy;

import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * 默认节点查询策略
 * 为没有特定查询逻辑的节点类型提供默认实现
 * 
 * <AUTHOR> Generated
 */
@Component
public class DefaultNodeQueryStrategy extends AbstractNodeQueryStrategy {

    @Override
    public NodeTypeEnum getSupportedNodeType() {
        // 默认策略不绑定特定节点类型
        return null;
    }

    @Override
    protected void doExecuteQuery(ProcessNode canvasData, BaseNodeProperties propertiesNodes, Map<String, Object> result) {
        // 执行默认查询逻辑
        executeComplexQuery(canvasData, propertiesNodes, result);
        executeSimpleQuery(canvasData, propertiesNodes, result);

        result.put("strategy", "default");
    }

    @Override
    public boolean supports(NodeTypeEnum nodeType) {
        // 默认策略支持所有节点类型
        return true;
    }
}



















