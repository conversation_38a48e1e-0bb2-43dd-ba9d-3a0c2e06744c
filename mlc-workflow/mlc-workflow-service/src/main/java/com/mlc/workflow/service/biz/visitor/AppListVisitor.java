package com.mlc.workflow.service.biz.visitor;

import com.mlc.application.api.IWorksheetInfoApi;
import com.mlc.application.api.messages.WorksheetDataInfo;
import com.mlc.workflow.core.editor.model.properties.ActionNodeProperties;
import com.mlc.workflow.core.editor.model.properties.AigcNodeProperties;
import com.mlc.workflow.core.editor.model.properties.ApprovalNodeProperties;
import com.mlc.workflow.core.editor.model.properties.CcNodeProperties;
import com.mlc.workflow.core.editor.model.properties.FileNodeProperties;
import com.mlc.workflow.core.editor.model.properties.FormulaNodeProperties;
import com.mlc.workflow.core.editor.model.properties.LinkNodeProperties;
import com.mlc.workflow.core.editor.model.properties.SearchNodeProperties;
import com.mlc.workflow.core.editor.model.properties.TemplateNodeProperties;
import com.mlc.workflow.core.editor.model.properties.WebhookNodeProperties;
import com.mlc.workflow.core.editor.model.properties.WriteNodeProperties;
import com.mlc.workflow.core.editor.model.properties.startevent.SheetEventTriggerProperties;
import com.mlc.workflow.core.editor.model.visitor.properties.AbstractNodeVisitor;
import io.nop.api.core.ioc.BeanContainer;
import java.util.List;
import lombok.Setter;

/**
 * 应用列表访问者
 */
public class AppListVisitor extends AbstractNodeVisitor {

    public static final String VISITOR_NAME = "appList";

    private final String appId;

    @Setter
    private List<WorksheetDataInfo> worksheetList;

    public List<WorksheetDataInfo> getWorksheetListField() {
        return worksheetList;
    }

    public AppListVisitor(String relationId) {
        this.appId = relationId;
    }

    private List<WorksheetDataInfo> getWorksheetList() {
        // 获取工作表信息
        IWorksheetInfoApi worksheetInfoApi = BeanContainer.getBeanByType(IWorksheetInfoApi.class);
        return worksheetInfoApi.getWorksheetListByAppId(appId);
    }

    @Override
    public void visit(SheetEventTriggerProperties node){
        setWorksheetList(this.getWorksheetList());
    }

    @Override
    public void visit(ActionNodeProperties node) {

    }

    @Override
    public void visit(AigcNodeProperties node) {

    }

    @Override
    public void visit(ApprovalNodeProperties node) {

    }

    @Override
    public void visit(CcNodeProperties node) {

    }

    @Override
    public void visit(FileNodeProperties node) {

    }

    @Override
    public void visit(FormulaNodeProperties node) {

    }

    @Override
    public void visit(LinkNodeProperties node) {

    }

    @Override
    public void visit(SearchNodeProperties node) {

    }


    @Override
    public void visit(TemplateNodeProperties node) {

    }

    @Override
    public void visit(WebhookNodeProperties node) {

    }

    @Override
    public void visit(WriteNodeProperties node) {

    }
}
