package com.mlc.workflow.service.landing.factory;

import com.mlc.base.common.enums.workflow.NodeTypeEnum;
import com.mlc.workflow.core.editor.model.BaseNodeCanvas;
import com.mlc.workflow.core.editor.model.BaseNodeProperties;
import com.mlc.workflow.core.editor.model.properties.*;

import org.springframework.stereotype.Component;

/**
 * Properties节点工厂
 * 根据Canvas节点类型创建对应的Properties节点
 */
public class PropertiesNodeFactory {

    /**
     * 根据Canvas节点创建对应的Properties节点
     * 
     * @param canvasNode Canvas节点
     * @param processConfigId 流程配置ID
     * @return Properties节点
     */
    public BaseNodeProperties createPropertiesNode(BaseNodeCanvas canvasNode, String processConfigId) {
        if (canvasNode == null) {
            throw new IllegalArgumentException("Canvas节点不能为空");
        }

        NodeTypeEnum nodeType = canvasNode.getNodeType();
        BaseNodeProperties propertiesNode = createPropertiesNodeByType(nodeType);
        
        // 设置基础属性
        propertiesNode.setFlowNodeType(nodeType.getValue());
        propertiesNode.setName(canvasNode.getName());
        propertiesNode.setSelectNodeId(canvasNode.getId());

        return propertiesNode;
    }

    /**
     * 根据节点类型创建Properties节点实例
     */
    private BaseNodeProperties createPropertiesNodeByType(NodeTypeEnum nodeType) {
        switch (nodeType) {
            case BRANCH:
                return new BranchNodeProperties();
            case BRANCH_ITEM:
                return new ConditionNodeProperties();
            case WRITE:
                return new WriteNodeProperties();
            case APPROVAL:
                return new ApprovalNodeProperties();
            case CC:
                return new CcNodeProperties();
            case ACTION:
                return new ActionNodeProperties();
            case SEARCH:
                return new SearchNodeProperties();
            case WEBHOOK:
                return new WebhookNodeProperties();
            case FORMULA:
                return new FormulaNodeProperties();
            case MESSAGE:
                return new MessageNodeProperties();
            case EMAIL:
                return new EmailNodeProperties();
            case DELAY:
                return new DelayNodeProperties();
            case GET_MORE_RECORD:
                return new GetMoreRecordNodeProperties();
            case CODE:
                return new CodeNodeProperties();
            case LINK:
                return new LinkNodeProperties();
            case SUB_PROCESS:
                return new SubProcessNodeProperties();
            case PUSH:
                return new PushNodeProperties();
            case FILE:
                return new FileNodeProperties();
            case TEMPLATE:
                return new TemplateNodeProperties();
            case PBC:
                return new PbcNodeProperties();
            case JSON_PARSE:
                return new JsonParseNodeProperties();
            case AUTHENTICATION:
                return new AuthenticationNodeProperties();
            case PARAMETER:
                return new ParameterNodeProperties();
            case API_PACKAGE:
                return new ApiPackageNodeProperties();
            case API:
                return new ApiNodeProperties();
            case APPROVAL_PROCESS:
                return new ApprovalProcessNodeProperties();
            case NOTICE:
                return new NotifyNodeProperties();
            case SNAPSHOT:
                return new SnapshotNodeProperties();
            case LOOP:
                return new LoopNodeProperties();
            case RETURN:
                return new ReturnNodeProperties();
            case AIGC:
                return new AigcNodeProperties();
            case PLUGIN:
                return new PluginNodeProperties();
            case SYSTEM:
                return new SystemNodeProperties();
            case FIND_SINGLE_MESSAGE:
                return new FindSingleMessageNodeProperties();
            case FIND_MORE_MESSAGE:
                return new FindMoreMessageNodeProperties();
            default:
                throw new IllegalArgumentException("不支持的节点类型: " + nodeType);
        }
    }

    /**
     * 根据节点类型判断是否需要特殊处理
     */
    public boolean needsSpecialHandling(NodeTypeEnum nodeType) {
        // 某些节点类型可能需要特殊的初始化逻辑
        switch (nodeType) {
            case START:
            case APPROVAL:
            case SUB_PROCESS:
            case API:
            case AIGC:
                return true;
            default:
                return false;
        }
    }

    /**
     * 为需要特殊处理的节点进行额外初始化
     */
    public void performSpecialInitialization(BaseNodeProperties propertiesNode, BaseNodeCanvas canvasNode) {
        NodeTypeEnum nodeType = NodeTypeEnum.fromValue(propertiesNode.getFlowNodeType());
        
        switch (nodeType) {
            case START:
                initializeStartEventNode((StartEventNodeProperties) propertiesNode, canvasNode);
                break;
            case APPROVAL:
                initializeApprovalNode((ApprovalNodeProperties) propertiesNode, canvasNode);
                break;
            case SUB_PROCESS:
                initializeSubProcessNode((SubProcessNodeProperties) propertiesNode, canvasNode);
                break;
            case API:
                initializeApiNode((ApiNodeProperties) propertiesNode, canvasNode);
                break;
            case AIGC:
                initializeAigcNode((AigcNodeProperties) propertiesNode, canvasNode);
                break;
            default:
                // 其他节点类型不需要特殊初始化
                break;
        }
    }

    /**
     * 初始化开始事件节点
     */
    private void initializeStartEventNode(StartEventNodeProperties properties, BaseNodeCanvas canvasNode) {
        // 设置开始事件特有的属性
        // 例如：触发条件、触发时间等
    }

    /**
     * 初始化审批节点
     */
    private void initializeApprovalNode(ApprovalNodeProperties properties, BaseNodeCanvas canvasNode) {
        // 设置审批节点特有的属性
        // 例如：审批人、审批规则等
    }

    /**
     * 初始化子流程节点
     */
    private void initializeSubProcessNode(SubProcessNodeProperties properties, BaseNodeCanvas canvasNode) {
        // 设置子流程节点特有的属性
        // 例如：子流程ID、参数映射等
    }

    /**
     * 初始化API节点
     */
    private void initializeApiNode(ApiNodeProperties properties, BaseNodeCanvas canvasNode) {
        // 设置API节点特有的属性
        // 例如：API配置、请求参数等
    }

    /**
     * 初始化AIGC节点
     */
    private void initializeAigcNode(AigcNodeProperties properties, BaseNodeCanvas canvasNode) {
        // 设置AIGC节点特有的属性
        // 例如：模型配置、提示词等
    }
}

