package com.mlc.workflow.service.biz;


import com.mlc.workflow.core.editor.runtime.beans.ProcessNode;
import com.mlc.workflow.core.editor.model.utils.ProcessParserUtil;
import com.mlc.workflow.dao.entity.MlcAppProcessConfig;
import com.mlc.workflow.service.dto.GetNodeDetailRequest;
import com.mlc.workflow.service.entity.MlcAppProcessConfigBizModel;
import com.mlc.workflow.service.biz.handler.NodeDetailHandlerFactory;
import com.mlc.workflow.service.landing.core.INodeManagementService;
import io.nop.api.core.annotations.biz.BizModel;
import io.nop.api.core.annotations.biz.BizQuery;
import io.nop.api.core.annotations.biz.RequestBean;
import io.nop.api.core.annotations.core.Name;
import io.nop.core.context.IServiceContext;
import jakarta.inject.Inject;
import java.util.Map;
import org.apache.commons.lang3.StringUtils;

/**
 * 流程节点相关接口
 */
@BizModel("WorkflowProcessNode")
public class ProcessNodeBizModel {

    @Inject
    MlcAppProcessConfigBizModel mlcAppProcessConfigBizModel;
    
    @Inject
    NodeDetailHandlerFactory nodeDetailHandlerFactory;

    @Inject
    INodeManagementService nodeManagementService;

    /**
     * 获取流程定义
     *
     * @param processId 流程ID
     * @param context   服务上下文
     * @return 流程定义对象
     */
    @BizQuery
    public ProcessNode getProcess(@Name("processId") String processId, IServiceContext context){
        MlcAppProcessConfig mlcAppProcessConfig = mlcAppProcessConfigBizModel.get(processId, true, context);
        String jsonStr = mlcAppProcessConfig.getJsonStr();
        return ProcessParserUtil.INSTANCE.parseProcessNode(jsonStr);
    }


    /**
     * 获取节点详情
     *
     * @param getNodeDetailRequest 获取节点详情请求参数
     * @param context          服务上下文
     * @return 节点详情，包含节点的配置信息等
     */
    @BizQuery
    public Map<String, Object> getNodeDetail(@RequestBean GetNodeDetailRequest getNodeDetailRequest, IServiceContext context){
        // 参数校验
        if (getNodeDetailRequest == null) {
            throw new IllegalArgumentException("请求参数不能为空");
        }
        
        if (StringUtils.isEmpty(getNodeDetailRequest.getFlowNodeType())) {
            throw new IllegalArgumentException("节点类型不能为空");
        }

        Map<String, Object> resMap = nodeManagementService.queryPropertiesNode(getNodeDetailRequest, context);
        // 根据节点类型获取对应的处理器
//        Map<String, Object> nodeDetail = nodeDetailHandlerFactory.getHandler(getNodeDetailRequest.getFlowNodeType())
//                                                                 .getNodeDetail(getNodeDetailRequest, context);
        return resMap;
    }

}
