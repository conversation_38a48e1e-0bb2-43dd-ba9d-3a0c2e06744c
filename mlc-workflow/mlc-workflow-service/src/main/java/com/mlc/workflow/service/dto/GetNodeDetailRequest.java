package com.mlc.workflow.service.dto;

import lombok.Getter;
import lombok.Setter;

/**
 * 获取节点详情请求参数
 */
@Getter
@Setter
public class GetNodeDetailRequest {

    /** 流程实例ID */
    private String processId;

    /** 节点ID */
    private String nodeId;

    /**
     * 节点类型
     * @see com.mlc.base.common.enums.workflow.NodeTypeEnum
     */
    private String flowNodeType;

    /** 应用ID */
    private String appId;

    /** 字段 */
    private String fields;

    /** 流程实例ID */
    private String instanceId;

}
