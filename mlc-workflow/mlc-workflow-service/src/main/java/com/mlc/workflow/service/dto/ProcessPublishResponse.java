package com.mlc.workflow.service.dto;


import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class ProcessPublishResponse {

    private String id;
    private String companyId;
    private String groupId;
    private String iconColor;
    private String iconName;
    private String name;
    private String explain;
    private Boolean enabled;
    private Boolean publish;
    private Date lastPublishDate;
    private Integer publishStatus;
    private Integer relationType;
    private String relationId;
    private Integer startAppType;
    private String startAppId;
    private String startNodeId;
    private String startTriggerId;
    private Boolean child;
    private String parentId;
    private Integer executeType;
    private List<Object> debugEvents;
    private Integer index;
    private String versionName;
}
