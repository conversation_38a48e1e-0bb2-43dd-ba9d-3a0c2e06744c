# 工作流节点管理系统

## 系统概述

本系统是一个完整的工作流节点管理解决方案，支持工作流节点的初始化、增删改查操作。系统采用了多种设计模式，提供了高度可扩展和可维护的架构。

## 核心功能

### 1. 初始化操作（Init）
- **步骤1**：调用 `FlowNodeBuilder` 构建初始化节点 → `processNode.toJson()` 结果并入库
- **步骤2**：用步骤1生成的Canvas节点 ID 循环创建对应的Properties节点 → Properties节点入库

### 2. 增加操作（Add）
- **步骤1**：区分节点类型 → 组装Canvas数据 → `WorkflowEditor` 不同类型的方法 → `processNode.toJson()` 结果并入库
- **步骤2**：基于步骤1的结果处理新建Properties节点逻辑 → 入库
- **步骤3**：执行节点特定逻辑查询并返回结果（需组装数据）

### 3. 删除操作（Delete）
- **步骤1**：区分节点类型 → 组装Canvas数据 → `WorkflowEditor` 不同类型的方法 → `processNode.toJson()` 结果并入库
- **步骤2**：从数据库删除Properties节点
- **步骤3**：执行节点特定逻辑查询并返回结果（需组装数据）

### 4. 修改操作（Update）
- **步骤1**：区分节点类型 → 组装Canvas数据 → `WorkflowEditor` 不同类型的方法 → `processNode.toJson()` 结果并入库
- **步骤2**：修改对应的Properties节点 → 更新数据库
- **步骤3**：执行节点特定逻辑查询并返回结果（需组装数据）

### 5. 查询操作（Query）
- **Canvas查询**：直接查询数据库返回节点数据
- **Properties查询**：
  - 步骤1：从数据库查询Canvas节点数据
  - 步骤2：按节点类型执行特定逻辑查询
  - 步骤3：将Canvas节点数据与特定逻辑数据组装后返回
  - **优化要求**：40种节点中存在可复用的逻辑（已抽象共用组件）

## 架构设计

### 设计模式应用

#### 1. 工厂模式（Factory Pattern）
- **NodeOperationExecutorFactory**：根据操作类型创建对应的执行器
- **PropertiesNodeFactory**：根据Canvas节点类型创建对应的Properties节点
- **NodeQueryStrategyFactory**：根据节点类型创建对应的查询策略

#### 2. 策略模式（Strategy Pattern）
- **NodeOperationExecutor**：定义不同操作的执行策略
- **NodeQueryStrategy**：定义不同节点类型的查询策略

#### 3. 模板方法模式（Template Method Pattern）
- **AbstractNodeOperationExecutor**：定义操作执行的基本流程
- **AbstractNodeQueryStrategy**：定义查询执行的基本流程

### 核心组件

#### 服务层
- `NodeManagementService`：主要服务接口
- `NodeManagementServiceImpl`：服务实现类

#### 执行器层
- `NodeOperationExecutor`：操作执行器接口
- `BasicNodeOperationExecutor`：基础节点操作执行器
- `GatewayOperationExecutor`：网关操作执行器
- `BranchOperationExecutor`：分支操作执行器

#### 数据管理层
- `CanvasDataManager`：Canvas数据管理器
- `PropertiesDataManager`：Properties数据管理器

#### 查询优化层
- `NodeQueryOptimizer`：节点查询优化器
- `NodeQueryStrategy`：查询策略接口
- 多种具体查询策略实现

#### 控制器层
- `WorkflowNodeController`：REST API控制器

## API接口

### 初始化接口
```http
POST /api/workflow/nodes/init
```

### 节点操作接口
```http
POST /api/workflow/nodes/insert          # 插入节点
PUT /api/workflow/nodes/{processConfigId}/{nodeId}  # 更新节点
DELETE /api/workflow/nodes/{processConfigId}/{nodeId}  # 删除节点
```

### 查询接口
```http
GET /api/workflow/nodes/canvas/{processConfigId}     # 查询Canvas节点
GET /api/workflow/nodes/properties/{processConfigId} # 查询Properties节点
```

### 网关操作接口
```http
POST /api/workflow/nodes/gateway/add                 # 新增网关
DELETE /api/workflow/nodes/gateway/{processConfigId}/{gatewayId}  # 删除网关
```

### 分支操作接口
```http
POST /api/workflow/nodes/branch/add                  # 新增分支
DELETE /api/workflow/nodes/branch/{processConfigId}/{gatewayId}/{branchLeafId}  # 删除分支
```

## 支持的节点类型

系统支持40多种不同类型的节点，包括：

- **基础节点**：开始节点、分支节点、条件节点等
- **业务节点**：审批节点、抄送节点、填写节点等
- **系统节点**：API节点、代码节点、公式节点等
- **高级节点**：子流程节点、AIGC节点、插件节点等

## 查询优化

### 共用逻辑抽象
- 通过`NodeQueryOptimizer`抽象40种节点的共用查询逻辑
- 使用策略模式为不同节点类型提供特定的查询策略
- 支持批量查询优化和缓存优化

### 查询策略
- `DefaultNodeQueryStrategy`：默认查询策略
- `ApprovalNodeQueryStrategy`：审批节点查询策略
- `ApiNodeQueryStrategy`：API节点查询策略
- `SubProcessNodeQueryStrategy`：子流程节点查询策略
- 可扩展支持更多特定节点类型的查询策略

## 使用示例

### 1. 初始化工作流
```java
NodeOperationResult result = nodeManagementService.initializeWorkflow(
    "process-config-123", 
    StartEventAppTypeEnum.WORKSHEET.getValue()
);
```

### 2. 插入节点
```java
NodeOperationRequest request = NodeOperationRequest.builder()
    .processConfigId("process-config-123")
    .operationType(NodeOperationType.INSERT_NODE)
    .targetNodeId("target-node-id")
    .canvasNode(newCanvasNode)
    .build();
    
NodeOperationResult result = nodeManagementService.executeNodeOperation(request);
```

### 3. 查询Properties节点
```java
NodeOperationResult result = nodeManagementService.queryPropertiesNodes(
    "process-config-123", 
    "specific-node-id"  // 可选
);
```

## 扩展性

### 添加新的节点类型
1. 在`NodeTypeEnum`中添加新的节点类型
2. 创建对应的Canvas和Properties类
3. 在`PropertiesNodeFactory`中添加创建逻辑
4. 如需特定查询逻辑，实现对应的`NodeQueryStrategy`

### 添加新的操作类型
1. 在`NodeOperationType`中添加新的操作类型
2. 实现对应的`NodeOperationExecutor`
3. 在`NodeOperationExecutorFactory`中注册新的执行器

## 事务管理

系统使用Spring的声明式事务管理，确保操作的原子性：
- 所有写操作都在事务中执行
- 支持操作回滚
- 保证数据一致性

## 异常处理

- 自定义`WorkflowNodeException`异常类
- 统一的错误码和错误信息
- 完整的异常日志记录

## 性能优化

- 使用连接池优化数据库访问
- 支持批量查询操作
- 预留缓存接口
- 查询结果分页支持

## 配置说明

通过`WorkflowNodeConfiguration`配置类自动装配所有组件，无需额外配置。

## 日志记录

使用SLF4J进行日志记录，包含：
- 操作请求日志
- 执行过程日志
- 异常错误日志
- 性能监控日志

