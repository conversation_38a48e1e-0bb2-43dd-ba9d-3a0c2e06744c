package com.mlc.workflow.service.landing.core.impl;

import com.fasterxml.jackson.core.type.TypeReference;
import com.mlc.application.api.messages.WorksheetDataInfo;
import com.mlc.workflow.core.editor.model.BaseNodeCanvas;
import com.mlc.workflow.core.editor.model.BaseNodeProperties;
import com.mlc.workflow.core.editor.model.build.PropertiesNodeBuilder;
import com.mlc.workflow.core.editor.model.canvas.StartEventNodeCanvas;
import com.mlc.workflow.core.editor.model.utils.PropertiesNodeParseUtil;
import com.mlc.workflow.core.editor.runtime.beans.ProcessNode;
import com.mlc.workflow.core.editor.model.build.CanvasNodeBuilder;
import com.mlc.workflow.core.editor.model.utils.ProcessParserUtil;
import com.mlc.workflow.dao.entity.MlcAppProcessConfig;
import com.mlc.workflow.dao.entity.MlcProcessNodeConfig;
import com.mlc.workflow.service.biz.visitor.AppListVisitor;
import com.mlc.workflow.service.dto.AddProcessConfigRequest;
import com.mlc.workflow.service.dto.GetNodeDetailRequest;
import com.mlc.workflow.service.entity.MlcAppProcessConfigBizModel;
import com.mlc.workflow.service.entity.MlcProcessNodeConfigBizModel;
import com.mlc.workflow.service.landing.core.INodeManagementService;
import com.mlc.workflow.service.landing.dto.NodeOperationRequest;
import com.mlc.workflow.service.landing.dto.NodeOperationResult;
import com.mlc.workflow.service.landing.factory.NodeOperationExecutorFactory;
import com.mlc.workflow.service.landing.strategy.INodeOperationExecutor;
import com.mlc.workflow.service.landing.query.NodeQueryOptimizer;

import io.nop.api.core.beans.FilterBeans;
import io.nop.api.core.beans.TreeBean;
import io.nop.core.context.IServiceContext;
import jakarta.inject.Inject;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.annotation.Transactional;

import java.util.Map;

/**
 * 工作流节点管理服务实现类
 *
 */
@Slf4j
public class NodeManagementServiceImpl implements INodeManagementService {

    @Inject
    NodeOperationExecutorFactory executorFactory;

    @Inject
    NodeQueryOptimizer queryOptimizer;

    @Inject
    MlcAppProcessConfigBizModel appProcessConfigBizModel;

    @Inject
    MlcProcessNodeConfigBizModel processNodeConfigBizModel;

    /**
     * 初始化工作流节点
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public String initializeProcess(AddProcessConfigRequest addProcessBean, IServiceContext context) {

        Map<String, BaseNodeCanvas> initialNodes = CanvasNodeBuilder.builder()
                .systemNodes()
                .getMoreRecordNode()
                .startEventNode(addProcessBean.getStartAppType())
                .buildMap();

        ProcessNode processNode = createProcessNode(initialNodes);

        String processConfigId = appProcessConfigBizModel.saveProcessConfig(addProcessBean, processNode, context);

        // 用生成的Canvas节点 ID 循环创建对应的Properties节点
        BaseNodeCanvas baseNodeCanvas = processNode.getFlowNodeMap().get(processNode.getStartEventId());
        String propertiesNode = this.createPropertiesNode(addProcessBean.getStartAppType());
        processNodeConfigBizModel.savePropertiesNode(processConfigId, baseNodeCanvas.getId(), propertiesNode, context);

        log.info("工作流节点初始化完成，processConfigId: {}", processConfigId);
        return processConfigId;
    }

    /**
     * 从所有节点中找到开始事件节点，并创建流程节点
     * @param initialNodes 所有节点
     * @return 流程节点
     */
    private static ProcessNode createProcessNode(Map<String, BaseNodeCanvas> initialNodes) {
        StartEventNodeCanvas startEventNode = null;
        for (BaseNodeCanvas node : initialNodes.values()) {
            if (node instanceof StartEventNodeCanvas) {
                startEventNode = (StartEventNodeCanvas) node;
                break;
            }
        }

        if (startEventNode == null) {
            throw new IllegalStateException("未能创建开始事件节点");
        }

        // 创建流程节点
        ProcessNode processNode = new ProcessNode();
        processNode.setStartEventId(startEventNode.getId());
        processNode.setFlowNodeMap(initialNodes);
        return processNode;
    }

    /**
     * 查询Canvas节点数据
     */
    @Override
    public ProcessNode queryCanvasNode(String processConfigId, IServiceContext context) {
        MlcAppProcessConfig mlcAppProcessConfig =
            appProcessConfigBizModel.get(processConfigId, true, context);
        return ProcessParserUtil.INSTANCE.parseProcessNode(mlcAppProcessConfig.getJsonStr());
    }

    /**
     * 查询Properties节点数据
     */
    @Override
    public Map<String, Object> queryPropertiesNode(GetNodeDetailRequest getNodeDetailRequest, IServiceContext context) {
        try {
            log.info("查询Properties节点数据，processConfigId: {}, nodeId: {}",
                     getNodeDetailRequest.getProcessId(), getNodeDetailRequest.getNodeId());

            // 步骤1：从数据库查询 Canvas节点数据
            ProcessNode canvasData = this.queryCanvasNode(getNodeDetailRequest.getProcessId(), context);

            // 查询节点配置信息
            TreeBean treeBean = FilterBeans.and(
                FilterBeans.eq(MlcProcessNodeConfig.PROP_NAME_processConfigId, getNodeDetailRequest.getProcessId()),
                FilterBeans.eq(MlcProcessNodeConfig.PROP_NAME_nodeId, getNodeDetailRequest.getNodeId()));
            MlcProcessNodeConfig nodeConfig = processNodeConfigBizModel.findFirst(treeBean.toQueryBean(), null, context);

            // 步骤2：按节点类型执行特定逻辑查询 & 步骤3：组装数据
//            NodeOperationResult result = queryOptimizer.optimizedQuery(canvasData, getNodeDetailRequest.getNodeId());

            String configValue = nodeConfig.getConfigValue();
//            log.info("Properties节点数据查询完成，processConfigId: {}", processConfigId);
            BaseNodeProperties nodeProperties = PropertiesNodeParseUtil.INSTANCE.fromJsonString(configValue, new TypeReference<>(){});
            AppListVisitor appListVisitor = new AppListVisitor(getNodeDetailRequest.getAppId());
            nodeProperties.accept(appListVisitor);
            List<WorksheetDataInfo> worksheetList = appListVisitor.getWorksheetListField();

            Map<String, Object> resMap = PropertiesNodeParseUtil.INSTANCE.fromJsonString(configValue, new TypeReference<>(){});
            resMap.put(AppListVisitor.VISITOR_NAME, worksheetList);
            return resMap;
        } catch (Exception e) {
//            log.error("查询Properties节点数据失败，processConfigId: {}", processConfigId, e);
            throw e;
        }
    }


    public static void main(String[] args) {
        String aaa = """
            {"name":"WORKSHEET","desc":"","flowNodeType":"0","appType":"1","triggerId":"2","operateCondition":[],"assignFieldId":"","assignFieldIds":[]}
            """;
        BaseNodeProperties nodeProperties = PropertiesNodeParseUtil.INSTANCE.fromJsonString(aaa, new TypeReference<BaseNodeProperties>(){});
        System.out.println(nodeProperties);
    }

    /**
     * 执行节点操作
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public NodeOperationResult executeNodeOperation(NodeOperationRequest request) {
        try {
            log.info("执行节点操作，operationType: {}, processConfigId: {}",
                     request.getOperationType(), request.getProcessConfigId());

            // 使用工厂模式获取对应的执行器
            INodeOperationExecutor executor = executorFactory.getExecutor(request.getOperationType());

            // 使用策略模式执行具体操作
            return executor.execute(request);
        } catch (Exception e) {
            log.error("执行节点操作失败，operationType: {}", request.getOperationType(), e);
            throw e;
        }
    }

    /**
     * 从Canvas节点创建Properties节点
     */
    private String createPropertiesNode(Integer startEventAppType){
        List<BaseNodeProperties> build = new PropertiesNodeBuilder().buildStartEventNode(startEventAppType).build();
        BaseNodeProperties baseNodeProperties = build.get(0);
        return PropertiesNodeParseUtil.INSTANCE.toJsonString(baseNodeProperties);
    }
}
