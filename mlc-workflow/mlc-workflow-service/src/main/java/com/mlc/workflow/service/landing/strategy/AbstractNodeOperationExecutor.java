package com.mlc.workflow.service.landing.strategy;

import com.mlc.workflow.core.editor.runtime.beans.ProcessNode;
import com.mlc.workflow.service.landing.dto.NodeOperationRequest;
import com.mlc.workflow.service.landing.dto.NodeOperationResult;

import lombok.extern.slf4j.Slf4j;

/**
 * 节点操作执行器抽象基类
 * 应用模板方法模式，定义操作执行的基本流程
 * 
 * <AUTHOR> Generated
 */
@Slf4j
public abstract class AbstractNodeOperationExecutor implements INodeOperationExecutor {

//    @Autowired
//    protected CanvasDataManager canvasDataManager;

//    @Autowired
//    protected PropertiesDataManager propertiesDataManager;

    /**
     * 模板方法：执行节点操作的基本流程
     */
    @Override
    public final NodeOperationResult execute(NodeOperationRequest request) {
        try {
            // 前置检查
            NodeOperationResult preCheckResult = preCheck(request);
//            if (!preCheckResult.isSuccess()) {
//                return preCheckResult;
//            }

            // 获取当前流程数据
            ProcessNode processNode = getCurrentProcessNode(request.getProcessConfigId());

            // 执行具体操作（由子类实现）
            NodeOperationResult operationResult = doExecute(request, processNode);
//            if (!operationResult.isSuccess()) {
//                return operationResult;
//            }

            // 保存Canvas数据
            saveCanvasData(request.getProcessConfigId(), processNode);

            // 处理Properties数据（由子类实现）
            NodeOperationResult propertiesResult = handlePropertiesData(request, operationResult);

            // 后置处理
            return postProcess(request, propertiesResult);

        } catch (Exception e) {
            log.error("执行节点操作失败", e);
            throw new RuntimeException("执行节点操作失败: " + e.getMessage(), e);
        }
    }

    /**
     * 前置检查（模板方法）
     * 子类可以重写此方法进行特定的检查
     */
    protected NodeOperationResult preCheck(NodeOperationRequest request) {
//        if (request == null) {
//            return NodeOperationResult.failure("INVALID_REQUEST", "请求参数不能为空");
//        }
//        if (request.getProcessConfigId() == null) {
//            return NodeOperationResult.failure("INVALID_PROCESS_CONFIG_ID", "流程配置ID不能为空");
//        }
        return NodeOperationResult.builder().build();
    }

    /**
     * 获取当前流程节点数据
     */
    protected ProcessNode getCurrentProcessNode(String processConfigId) {
//        String canvasJson = canvasDataManager.getCanvasData(processConfigId);
        return parseProcessNodeFromJson(null);
    }

    /**
     * 从JSON字符串解析ProcessNode
     */
    private ProcessNode parseProcessNodeFromJson(String json) {
        try {
            com.fasterxml.jackson.databind.ObjectMapper mapper = new com.fasterxml.jackson.databind.ObjectMapper();
            return mapper.readValue(json, ProcessNode.class);
        } catch (Exception e) {
            throw new RuntimeException("解析ProcessNode JSON失败: " + e.getMessage(), e);
        }
    }

    /**
     * 保存Canvas数据
     */
    protected void saveCanvasData(String processConfigId, ProcessNode processNode) {
        String canvasJson = processNode.toJsonString();
//        canvasDataManager.saveCanvasData(processConfigId, canvasJson);
    }

    /**
     * 执行具体操作（抽象方法，由子类实现）
     */
    protected abstract NodeOperationResult doExecute(NodeOperationRequest request, ProcessNode processNode);

    /**
     * 处理Properties数据（抽象方法，由子类实现）
     */
    protected abstract NodeOperationResult handlePropertiesData(NodeOperationRequest request, NodeOperationResult operationResult);

    /**
     * 后置处理（模板方法）
     * 子类可以重写此方法进行特定的后置处理
     */
    protected NodeOperationResult postProcess(NodeOperationRequest request, NodeOperationResult result) {
        return result;
    }
}
