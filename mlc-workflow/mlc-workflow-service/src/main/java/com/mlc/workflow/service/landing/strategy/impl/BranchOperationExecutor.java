package com.mlc.workflow.service.landing.strategy.impl;

import com.mlc.workflow.core.editor.model.canvas.ConditionNodeCanvas;
import com.mlc.workflow.core.editor.runtime.beans.ProcessNode;
import com.mlc.workflow.core.editor.structure.WorkflowEditor;
import com.mlc.workflow.service.landing.dto.NodeOperationRequest;
import com.mlc.workflow.service.landing.dto.NodeOperationResult;
import com.mlc.workflow.service.landing.enums.NodeOperationType;
import com.mlc.workflow.service.landing.strategy.AbstractNodeOperationExecutor;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 分支操作执行器
 * 处理ADD_BRANCH、DELETE_BRANCH、REORDER_BRANCHES、DUPLICATE_BRANCH操作
 * 
 * <AUTHOR> Generated
 */
@Component
public class BranchOperationExecutor extends AbstractNodeOperationExecutor {

    private WorkflowEditor workflowEditor;

    @Override
    public boolean supports(NodeOperationType operationType) {
        return operationType.isBranchOperation();
    }

    @Override
    protected NodeOperationResult doExecute(NodeOperationRequest request, ProcessNode processNode) {
        NodeOperationType operationType = request.getOperationType();
        
        switch (operationType) {
            case ADD_BRANCH:
                return handleAddBranch(request, processNode);
            case DELETE_BRANCH:
                return handleDeleteBranch(request, processNode);
            case REORDER_BRANCHES:
                return handleReorderBranches(request, processNode);
            case DUPLICATE_BRANCH:
                return handleDuplicateBranch(request, processNode);
            default:
                return null;
        }
    }

    @Override
    protected NodeOperationResult handlePropertiesData(NodeOperationRequest request, NodeOperationResult operationResult) {
        NodeOperationType operationType = request.getOperationType();
        
        switch (operationType) {
            case ADD_BRANCH:
                return handleAddBranchProperties(request, operationResult);
            case DELETE_BRANCH:
                return handleDeleteBranchProperties(request, operationResult);
            case REORDER_BRANCHES:
                return handleReorderBranchesProperties(request, operationResult);
            case DUPLICATE_BRANCH:
                return handleDuplicateBranchProperties(request, operationResult);
            default:
                return operationResult;
        }
    }

    /**
     * 处理新增分支
     */
    private NodeOperationResult handleAddBranch(NodeOperationRequest request, ProcessNode processNode) {
        try {
            NodeOperationRequest.BranchParams params = request.getBranchParams();
            if (params == null || params.getGatewayId() == null) {
                throw new IllegalArgumentException("缺少分支参数");
            }

            ConditionNodeCanvas branchNode = workflowEditor.addBranch(
                processNode, 
                params.getGatewayId(), 
                params.getPosition() != null ? params.getPosition() : 0
            );
            
            return NodeOperationResult.builder().build();
        } catch (Exception e) {
            throw e;
        }
    }

    /**
     * 处理删除分支
     */
    private NodeOperationResult handleDeleteBranch(NodeOperationRequest request, ProcessNode processNode) {
        try {
            NodeOperationRequest.BranchParams params = request.getBranchParams();
            if (params == null || params.getGatewayId() == null || params.getBranchLeafId() == null) {
                throw new IllegalArgumentException("缺少分支参数");
            }

            workflowEditor.deleteBranch(processNode, params.getGatewayId(), params.getBranchLeafId());
            return NodeOperationResult.builder().build();
        } catch (Exception e) {
            throw e;
        }
    }

    /**
     * 处理调整分支顺序
     */
    private NodeOperationResult handleReorderBranches(NodeOperationRequest request, ProcessNode processNode) {
        try {
            NodeOperationRequest.BranchParams params = request.getBranchParams();
            if (params == null || params.getGatewayId() == null || params.getNewOrder() == null) {
                throw new IllegalArgumentException("缺少分支参数");
            }

            workflowEditor.reorderBranches(processNode, params.getGatewayId(), params.getNewOrder());
            return NodeOperationResult.builder().build();
        } catch (Exception e) {
            throw e;
        }
    }

    /**
     * 处理复制分支
     */
    private NodeOperationResult handleDuplicateBranch(NodeOperationRequest request, ProcessNode processNode) {
        try {
            NodeOperationRequest.BranchParams params = request.getBranchParams();
            if (params == null || params.getGatewayId() == null || params.getBranchLeafId() == null) {
                throw new IllegalArgumentException("缺少分支参数");
            }

            ConditionNodeCanvas duplicatedBranch = workflowEditor.duplicateBranch(
                processNode, 
                params.getGatewayId(), 
                params.getBranchLeafId(), 
                params.getPosition() != null ? params.getPosition() : 0
            );
            
            return NodeOperationResult.builder().build();
        } catch (Exception e) {
            throw e;
        }
    }

    /**
     * 处理新增分支的Properties数据
     */
    private NodeOperationResult handleAddBranchProperties(NodeOperationRequest request, NodeOperationResult operationResult) {
        try {
            ConditionNodeCanvas branchNode = (ConditionNodeCanvas) operationResult.getResultCanvasNode();
            if (branchNode != null) {
                // 创建分支条件的Properties配置
//                var propertiesNode = propertiesDataManager.createPropertiesNode(
//                    branchNode, request.getProcessConfigId()
//                );
//                propertiesDataManager.savePropertiesNode(propertiesNode);
//                operationResult.setResultPropertiesNode(propertiesNode);
            }
            return operationResult;
        } catch (Exception e) {
            throw e;
        }
    }

    /**
     * 处理删除分支的Properties数据
     */
    private NodeOperationResult handleDeleteBranchProperties(NodeOperationRequest request, NodeOperationResult operationResult) {
        try {
            NodeOperationRequest.BranchParams params = request.getBranchParams();
            if (params != null && params.getBranchLeafId() != null) {
//                propertiesDataManager.deletePropertiesNode(request.getProcessConfigId(), params.getBranchLeafId());
            }
            return operationResult;
        } catch (Exception e) {
            throw e;
        }
    }

    /**
     * 处理调整分支顺序的Properties数据
     */
    private NodeOperationResult handleReorderBranchesProperties(NodeOperationRequest request, NodeOperationResult operationResult) {
        try {
            // 调整分支顺序通常不需要修改Properties
            // 如果需要，可以在这里处理
            return operationResult;
        } catch (Exception e) {
            throw e;
        }
    }

    /**
     * 处理复制分支的Properties数据
     */
    private NodeOperationResult handleDuplicateBranchProperties(NodeOperationRequest request, NodeOperationResult operationResult) {
        try {
            ConditionNodeCanvas duplicatedBranch = (ConditionNodeCanvas) operationResult.getResultCanvasNode();
            if (duplicatedBranch != null) {
                // 创建复制分支的Properties配置
//                var propertiesNode = propertiesDataManager.createPropertiesNode(
//                    duplicatedBranch, request.getProcessConfigId()
//                );
//                propertiesDataManager.savePropertiesNode(propertiesNode);
//                operationResult.setResultPropertiesNode(propertiesNode);
            }
            return operationResult;
        } catch (Exception e) {
            throw e;
        }
    }
}

