package com.mlc.workflow.service.biz.handler.impl;

import com.mlc.base.common.enums.workflow.NodeTypeEnum;
import com.mlc.workflow.service.dto.GetNodeDetailRequest;
import com.mlc.workflow.service.biz.handler.AbstractNodeDetailHandler;
import io.nop.core.context.IServiceContext;
import java.util.Map;
import org.springframework.stereotype.Component;

/**
 * 审批节点详情处理器
 */
@Component
public class ApprovalNodeDetailHandler extends AbstractNodeDetailHandler {

    @Override
    protected String getSupportedNodeType() {
        return NodeTypeEnum.TYPE_APPROVAL;
    }

    @Override
    protected Map<String, Object> queryAdditionalData(GetNodeDetailRequest request, IServiceContext context) {
        // 实现审批节点的详情获取逻辑
        // 这里只是示例，实际实现需要根据业务需求进行开发
        
        // 可以根据request中的processId和nodeId获取节点配置信息
        String processId = request.getProcessId();
        String nodeId = request.getNodeId();
        
        // 获取审批人、审批规则等信息
        
        // 返回JSON格式的节点详情
        return Map.of(
            "nodeType", "approval",
            "processId", processId,
            "nodeId", nodeId,
            "approvalType", "normal",
            "approvers", new String[]{} // 示例中返回空数组，实际应返回具体审批人列表
        );
    }
}
