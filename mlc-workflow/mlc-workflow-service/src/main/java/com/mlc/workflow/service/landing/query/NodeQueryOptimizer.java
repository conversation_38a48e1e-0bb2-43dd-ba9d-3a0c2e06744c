package com.mlc.workflow.service.landing.query;

import com.mlc.base.common.enums.workflow.NodeTypeEnum;
import com.mlc.workflow.core.editor.model.BaseNodeProperties;
import com.mlc.workflow.core.editor.runtime.beans.ProcessNode;
import com.mlc.workflow.service.landing.dto.NodeOperationResult;
import com.mlc.workflow.service.landing.query.strategy.INodeQueryStrategy;
import com.mlc.workflow.service.landing.query.strategy.NodeQueryStrategyFactory;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * 节点查询优化器
 * 抽象40种节点的共用逻辑，提供优化的查询策略
 * 
 * <AUTHOR> Generated
 */
@Slf4j
@Component
public class NodeQueryOptimizer {

//    @Autowired
//    private PropertiesDataManager propertiesDataManager;

    @Autowired
    private NodeQueryStrategyFactory queryStrategyFactory;

    /**
     * 优化查询：将Canvas节点数据与特定逻辑数据组装后返回
     * 
     * @param canvasData Canvas节点数据
     * @param nodeId 节点ID
     * @return 查询结果
     */
    public NodeOperationResult optimizedQuery(ProcessNode canvasData, String nodeId) {
        try {
            log.debug("开始优化查询，processConfigId: {}, nodeId: {}", canvasData.getId(), nodeId);

            // 步骤1：从数据库查询Properties节点数据
//            List<BaseNodeProperties> propertiesNodes = propertiesDataManager.queryPropertiesNodes(
//                canvasData.getId(), nodeId
//            );

            // 步骤2：按节点类型执行特定逻辑查询
            Map<String, Object> specificLogicData = executeSpecificLogicQueries(canvasData, null);

            log.debug("优化查询完成，processConfigId: {}", canvasData.getId());
            return NodeOperationResult.builder().build();
        } catch (Exception e) {
            log.error("优化查询失败", e);
            throw new RuntimeException("优化查询失败: " + e.getMessage(), e);
        }
    }

    /**
     * 按节点类型执行特定逻辑查询
     * 这里抽象了40种节点的共用逻辑
     */
    private Map<String, Object> executeSpecificLogicQueries(ProcessNode canvasData, BaseNodeProperties baseNodeProperties) {
        Map<String, Object> specificLogicData = new HashMap<>();

        // 为每种节点类型执行特定查询
        NodeTypeEnum nodeType = baseNodeProperties.getNodeType();
        try {
            // 使用策略模式获取对应的查询策略
            INodeQueryStrategy strategy = queryStrategyFactory.getStrategy(nodeType);
            Map<String, Object> typeSpecificData = strategy.executeQuery(canvasData, baseNodeProperties);

            // 将查询结果按节点类型存储
            specificLogicData.put(nodeType.name(), typeSpecificData);

        } catch (Exception e) {
            log.warn("执行节点类型 {} 的特定查询失败: {}", nodeType, e.getMessage());
            // 继续处理其他节点类型，不中断整个查询过程
        }

        return specificLogicData;
    }

}
