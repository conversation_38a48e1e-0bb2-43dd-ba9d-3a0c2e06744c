package com.mlc.workflow.service.landing.query.strategy;

import com.mlc.workflow.core.editor.model.BaseNodeProperties;
import com.mlc.workflow.core.editor.runtime.beans.ProcessNode;

import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.Map;

/**
 * 节点查询策略抽象基类
 * 提供通用的查询逻辑和模板方法
 * 
 * <AUTHOR> Generated
 */
@Slf4j
public abstract class AbstractNodeQueryStrategy implements INodeQueryStrategy {

    @Override
    public final Map<String, Object> executeQuery(ProcessNode canvasData, BaseNodeProperties propertiesNodes) {
        try {
            log.debug("执行节点查询策略，nodeType: {}, nodeCount: {}", 
                    getSupportedNodeType(), propertiesNodes);

            Map<String, Object> result = new HashMap<>();

            // 前置处理
            preProcess(canvasData, propertiesNodes, result);

            // 执行特定查询逻辑
            doExecuteQuery(canvasData, propertiesNodes, result);

            // 后置处理
            postProcess(canvasData, propertiesNodes, result);

            log.debug("节点查询策略执行完成，nodeType: {}", getSupportedNodeType());
            return result;

        } catch (Exception e) {
            log.error("执行节点查询策略失败，nodeType: {}", getSupportedNodeType(), e);
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("error", e.getMessage());
            errorResult.put("nodeType", getSupportedNodeType().name());
            return errorResult;
        }
    }

    /**
     * 前置处理（模板方法）
     * 子类可以重写进行特定的前置处理
     */
    protected void preProcess(ProcessNode canvasData, BaseNodeProperties propertiesNodes, Map<String, Object> result) {
        // 添加基础信息
        result.put("nodeType", getSupportedNodeType().name());
        result.put("nodeCount", propertiesNodes == null ? 0 : 1);
        result.put("processConfigId", canvasData.getId());
    }

    /**
     * 执行特定查询逻辑（抽象方法）
     * 子类必须实现具体的查询逻辑
     */
    protected abstract void doExecuteQuery(ProcessNode canvasData, BaseNodeProperties propertiesNodes, Map<String, Object> result);

    /**
     * 后置处理（模板方法）
     * 子类可以重写进行特定的后置处理
     */
    protected void postProcess(ProcessNode canvasData, BaseNodeProperties propertiesNodes, Map<String, Object> result) {
        // 添加执行时间戳
        result.put("executedAt", System.currentTimeMillis());
    }

    /**
     * 获取节点基础信息
     */
    protected Map<String, Object> getNodeBasicInfo(BaseNodeProperties propertiesNode) {
        Map<String, Object> info = new HashMap<>();
        info.put("name", propertiesNode.getName());
        info.put("flowNodeType", propertiesNode.getFlowNodeType());
        return info;
    }


    /**
     * 执行简单查询
     */
    protected void executeSimpleQuery(ProcessNode canvasData, BaseNodeProperties propertiesNodes, Map<String, Object> result) {
        // 默认实现：只返回节点基础信息
    }

    /**
     * 执行复杂查询
     */
    protected void executeComplexQuery(ProcessNode canvasData, BaseNodeProperties propertiesNodes, Map<String, Object> result) {
        // 默认实现：调用简单查询
        executeSimpleQuery(canvasData, propertiesNodes, result);
        result.put("queryType", "complex");
    }
}
