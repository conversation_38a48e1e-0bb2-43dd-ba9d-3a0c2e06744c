package com.mlc.workflow.service.dto;

import java.util.List;
import lombok.Getter;
import lombok.Setter;

/**
 * 包含流程组、流程和账户信息的嵌套结构
 */
@Getter
@Setter
public class ProcessGroupResponse {
    private String groupId;
    private Integer processListType;
    private Integer groupIndex;
    private String groupName;
    private String icon;
    private String iconColor;
    private String iconUrl;
    private List<Process> processList;


    /**
     * 流程信息
     */
    @Getter
    @Setter
    public static class Process {
        private String id;
        private String companyId;
        private String groupId;
        private String name;
        private String explain;
        private String createdDate;
        private String lastModifiedBy;
        private String lastModifiedDate;
        private Boolean deleted;
        private Boolean enabled;
        private OwnerAccount ownerAccount;
        private Boolean isOwner;
        private Integer publishStatus;
        private Integer relationType;
        private String relationId;
        private Integer startAppType;
        private String appId;
        private String triggerId;
        private Boolean isApkAdmin;
        private Boolean child;
        private Integer processListType;
        private Boolean hasAuth;
    }

    /**
     * 账户信息
     */
    @Getter
    @Setter
    public static class OwnerAccount {
        private String accountId;
        private String fullName;
        private String avatar;
        private Integer status;
        private Boolean isOwner;
        private String fullname;
    }

    /**
     * 创建单个流程实例
     */
    public static Process createProcess() {
        return new Process();
    }

    /**
     * 创建账户信息实例
     */
    public static OwnerAccount createOwnerAccount() {
        return new OwnerAccount();
    }
}
