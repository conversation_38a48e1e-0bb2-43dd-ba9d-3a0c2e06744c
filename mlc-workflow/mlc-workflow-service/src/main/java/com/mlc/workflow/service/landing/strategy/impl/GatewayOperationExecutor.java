package com.mlc.workflow.service.landing.strategy.impl;

import com.mlc.workflow.core.editor.model.canvas.GatewayNodeCanvas;
import com.mlc.workflow.core.editor.runtime.beans.ProcessNode;
import com.mlc.workflow.core.editor.structure.WorkflowEditor;
import com.mlc.workflow.core.editor.structure.operation.GatewayOperations;
import com.mlc.workflow.service.landing.dto.NodeOperationRequest;
import com.mlc.workflow.service.landing.dto.NodeOperationResult;
import com.mlc.workflow.service.landing.enums.NodeOperationType;
import com.mlc.workflow.service.landing.strategy.AbstractNodeOperationExecutor;

import org.springframework.stereotype.Component;

/**
 * 网关操作执行器
 * 处理ADD_GATEWAY、DELETE_GATEWAY、SWITCH_GATEWAY_TYPE操作
 * 
 * <AUTHOR> Generated
 */
@Component
public class GatewayOperationExecutor extends AbstractNodeOperationExecutor {

    private WorkflowEditor workflowEditor;

    @Override
    public boolean supports(NodeOperationType operationType) {
        return operationType.isGatewayOperation();
    }

    @Override
    protected NodeOperationResult doExecute(NodeOperationRequest request, ProcessNode processNode) {
        NodeOperationType operationType = request.getOperationType();
        
        switch (operationType) {
            case ADD_GATEWAY:
                return handleAddGateway(request, processNode);
            case DELETE_GATEWAY:
                return handleDeleteGateway(request, processNode);
            case SWITCH_GATEWAY_TYPE:
                return handleSwitchGatewayType(request, processNode);
            default:
                return null;
        }
    }

    @Override
    protected NodeOperationResult handlePropertiesData(NodeOperationRequest request, NodeOperationResult operationResult) {
        NodeOperationType operationType = request.getOperationType();

        return switch (operationType) {
            case ADD_GATEWAY -> handleAddGatewayProperties(request, operationResult);
            case DELETE_GATEWAY -> handleDeleteGatewayProperties(request, operationResult);
            case SWITCH_GATEWAY_TYPE -> handleSwitchGatewayTypeProperties(request, operationResult);
            default -> operationResult;
        };
    }

    /**
     * 处理新增网关
     */
    private NodeOperationResult handleAddGateway(NodeOperationRequest request, ProcessNode processNode) {
        try {
            NodeOperationRequest.GatewayParams params = request.getGatewayParams();
            if (params == null) {
                throw new IllegalArgumentException("缺少网关参数");
            }

            GatewayOperations.PlacementStrategy placement = 
                GatewayOperations.PlacementStrategy.valueOf(params.getPlacement());
            
            GatewayNodeCanvas gatewayNode = workflowEditor.addGateway(
                processNode, 
                request.getTargetNodeId(), 
                placement
            );
            
            return NodeOperationResult.builder().build();
        } catch (Exception e) {
            throw e;
        }
    }

    /**
     * 处理删除网关
     */
    private NodeOperationResult handleDeleteGateway(NodeOperationRequest request, ProcessNode processNode) {
        try {
            NodeOperationRequest.GatewayParams params = request.getGatewayParams();
            if (params == null || params.getGatewayId() == null) {
                throw new IllegalArgumentException("缺少网关参数");
            }

            workflowEditor.deleteGateway(processNode, params.getGatewayId());
            return NodeOperationResult.builder().build();
        } catch (Exception e) {
            throw e;
        }
    }

    /**
     * 处理切换网关类型
     */
    private NodeOperationResult handleSwitchGatewayType(NodeOperationRequest request, ProcessNode processNode) {
        try {
            NodeOperationRequest.GatewayParams params = request.getGatewayParams();
            if (params == null || params.getGatewayId() == null || params.getGatewayType() == null) {
                throw new IllegalArgumentException("缺少网关参数");
            }

            workflowEditor.switchGatewayType(processNode, params.getGatewayId(), params.getGatewayType());
            return NodeOperationResult.builder().build();
        } catch (Exception e) {
            throw e;
        }
    }

    /**
     * 处理新增网关的Properties数据
     */
    private NodeOperationResult handleAddGatewayProperties(NodeOperationRequest request, NodeOperationResult operationResult) {
        try {
            GatewayNodeCanvas gatewayNode = (GatewayNodeCanvas) operationResult.getResultCanvasNode();
            if (gatewayNode != null) {
                // 网关节点通常不需要额外的Properties配置
                // 如果需要，可以在这里创建
                return operationResult;
            }
            return operationResult;
        } catch (Exception e) {
            throw e;
        }
    }

    /**
     * 处理删除网关的Properties数据
     */
    private NodeOperationResult handleDeleteGatewayProperties(NodeOperationRequest request, NodeOperationResult operationResult) {
        try {
            NodeOperationRequest.GatewayParams params = request.getGatewayParams();
            if (params != null && params.getGatewayId() != null) {
//                propertiesDataManager.deletePropertiesNode(request.getProcessConfigId(), params.getGatewayId());
            }
            return operationResult;
        } catch (Exception e) {
            throw e;
        }
    }

    /**
     * 处理切换网关类型的Properties数据
     */
    private NodeOperationResult handleSwitchGatewayTypeProperties(NodeOperationRequest request, NodeOperationResult operationResult) {
        try {
            // 切换网关类型通常不需要修改Properties
            // 如果需要，可以在这里处理
            return operationResult;
        } catch (Exception e) {
            throw e;
        }
    }
}



















