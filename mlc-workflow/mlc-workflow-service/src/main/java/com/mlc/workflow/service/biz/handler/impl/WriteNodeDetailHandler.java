package com.mlc.workflow.service.biz.handler.impl;

import com.mlc.base.common.enums.workflow.NodeTypeEnum;
import com.mlc.workflow.service.dto.GetNodeDetailRequest;
import com.mlc.workflow.service.biz.handler.AbstractNodeDetailHandler;
import io.nop.core.context.IServiceContext;
import java.util.Map;
import org.springframework.stereotype.Component;

/**
 * 填写节点详情处理器
 */
@Component
public class WriteNodeDetailHandler extends AbstractNodeDetailHandler {

    @Override
    protected String getSupportedNodeType() {
        return NodeTypeEnum.TYPE_WRITE;
    }

    @Override
    protected Map<String, Object> queryAdditionalData(GetNodeDetailRequest request, IServiceContext context) {
        // 实现填写节点的详情获取逻辑
        // 这里只是示例，实际实现需要根据业务需求进行开发
        
        String processId = request.getProcessId();
        String nodeId = request.getNodeId();
        String appId = request.getAppId();
        String fields = request.getFields(); // 可能包含需要填写的字段信息
        
        // 获取表单字段配置、填写人等信息
        
        // 返回JSON格式的节点详情
        return Map.of(
            "nodeType", "write",
            "processId", processId,
            "nodeId", nodeId,
            "appId", appId,
            "fields", fields,
            "formFields", new String[]{} // 示例中返回空数组，实际应返回具体表单字段列表
        );
    }
}
