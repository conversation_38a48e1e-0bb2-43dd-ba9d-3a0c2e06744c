package com.mlc.workflow.service.landing.dto;

import com.mlc.workflow.core.editor.model.BaseNodeCanvas;
import com.mlc.workflow.core.editor.model.BaseNodeProperties;
import com.mlc.workflow.service.landing.enums.NodeOperationType;
import lombok.Data;
import lombok.Builder;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

import java.util.Map;

/**
 * 节点操作请求DTO
 * 
 * <AUTHOR> Generated
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class NodeOperationRequest {

    /**
     * 流程配置ID
     */
    private String processConfigId;

    /**
     * 操作类型
     */
    private NodeOperationType operationType;

    /**
     * 节点ID（用于删除、修改、查询操作）
     */
    private String nodeId;

    /**
     * 目标节点ID（用于插入操作，表示在此节点之后插入）
     */
    private String targetNodeId;

    /**
     * Canvas节点数据（用于新增、修改操作）
     */
    private BaseNodeCanvas canvasNode;

    /**
     * Properties节点数据（用于新增、修改操作）
     */
    private BaseNodeProperties propertiesNode;

    /**
     * 更新字段（用于修改操作）
     */
    private Map<String, Object> updateFields;

    /**
     * 网关相关参数
     */
    private GatewayParams gatewayParams;

    /**
     * 分支相关参数
     */
    private BranchParams branchParams;

    /**
     * 网关参数
     */
    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class GatewayParams {
        private String gatewayId;
        private String placement; // 放置策略
        private Integer gatewayType; // 网关类型
    }

    /**
     * 分支参数
     */
    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class BranchParams {
        private String gatewayId;
        private String branchLeafId;
        private Integer position;
        private java.util.List<String> newOrder;
    }
}

