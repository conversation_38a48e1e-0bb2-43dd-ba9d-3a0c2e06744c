package com.mlc.workflow.service.landing.query.strategy.impl;

import com.mlc.base.common.enums.workflow.NodeTypeEnum;
import com.mlc.workflow.core.editor.model.BaseNodeProperties;
import com.mlc.workflow.core.editor.model.properties.ApiNodeProperties;
import com.mlc.workflow.core.editor.runtime.beans.ProcessNode;
import com.mlc.workflow.service.landing.query.strategy.AbstractNodeQueryStrategy;

import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * API节点查询策略
 * 提供API节点特有的查询逻辑
 * 
 * <AUTHOR> Generated
 */
@Component
public class ApiNodeQueryStrategy extends AbstractNodeQueryStrategy {

    @Override
    public NodeTypeEnum getSupportedNodeType() {
        return NodeTypeEnum.API;
    }

    @Override
    protected void doExecuteQuery(ProcessNode canvasData, BaseNodeProperties propertiesNodes, Map<String, Object> result) {
        // API节点特有的查询逻辑
        
        // 4. 基础节点信息
        executeSimpleQuery(canvasData, propertiesNodes, result);
        
        result.put("strategy", "api");
    }


    /**
     * 查询API配置信息
     */
    private Map<String, Object> queryApiConfigurations(List<BaseNodeProperties> propertiesNodes) {
        Map<String, Object> configs = new HashMap<>();
        
        // 这里可以查询API的配置信息
        // 例如：请求方法、请求头、请求参数、响应处理等
        
        configs.put("totalConfigurations", propertiesNodes.size());
        configs.put("configurationList", List.of());
        
        return configs;
    }

    /**
     * 查询API连接状态
     */
    private Map<String, Object> queryApiConnectionStatus(List<BaseNodeProperties> propertiesNodes) {
        Map<String, Object> status = new HashMap<>();
        
        // 这里可以检查API的连接状态
        // 例如：连接测试、响应时间、可用性等
        
        status.put("totalConnections", propertiesNodes.size());
        status.put("activeConnections", 0);
        status.put("failedConnections", 0);
        
        return status;
    }
}

