package com.mlc.workflow.service.landing.strategy.impl;

import com.mlc.workflow.core.editor.model.BaseNodeCanvas;
import com.mlc.workflow.core.editor.runtime.beans.ProcessNode;
import com.mlc.workflow.core.editor.structure.WorkflowEditor;
import com.mlc.workflow.service.landing.dto.NodeOperationRequest;
import com.mlc.workflow.service.landing.dto.NodeOperationResult;
import com.mlc.workflow.service.landing.enums.NodeOperationType;
import com.mlc.workflow.service.landing.strategy.AbstractNodeOperationExecutor;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 基础节点操作执行器
 * 处理INSERT_NODE、DELETE_NODE、UPDATE_NODE、QUERY_NODE操作
 * 
 * <AUTHOR> Generated
 */
@Component
public class BasicNodeOperationExecutor extends AbstractNodeOperationExecutor {

    private WorkflowEditor workflowEditor;

    @Override
    public boolean supports(NodeOperationType operationType) {
        return operationType.isBasicNodeOperation();
    }

    @Override
    protected NodeOperationResult doExecute(NodeOperationRequest request, ProcessNode processNode) {
        NodeOperationType operationType = request.getOperationType();

        return switch (operationType) {
            case INSERT_NODE -> handleInsertNode(request, processNode);
            case DELETE_NODE -> handleDeleteNode(request, processNode);
            case UPDATE_NODE -> handleUpdateNode(request, processNode);
            case QUERY_NODE -> handleQueryNode(request, processNode);
            default -> null;
        };
    }

    @Override
    protected NodeOperationResult handlePropertiesData(NodeOperationRequest request, NodeOperationResult operationResult) {
        NodeOperationType operationType = request.getOperationType();

        return switch (operationType) {
            case INSERT_NODE -> handleInsertNodeProperties(request, operationResult);
            case DELETE_NODE -> handleDeleteNodeProperties(request, operationResult);
            case UPDATE_NODE -> handleUpdateNodeProperties(request, operationResult);
            case QUERY_NODE -> handleQueryNodeProperties(request, operationResult);
            default -> operationResult;
        };
    }

    /**
     * 处理插入节点
     */
    private NodeOperationResult handleInsertNode(NodeOperationRequest request, ProcessNode processNode) {
        try {
            BaseNodeCanvas insertedNode = workflowEditor.insertNode(
                processNode, 
                request.getTargetNodeId(), 
                request.getCanvasNode()
            );
            
            return NodeOperationResult.builder().build();
        } catch (Exception e) {
            throw new RuntimeException("插入节点失败: " + e.getMessage(), e);
        }
    }

    /**
     * 处理删除节点
     */
    private NodeOperationResult handleDeleteNode(NodeOperationRequest request, ProcessNode processNode) {
        try {
            workflowEditor.deleteNode(processNode, request.getNodeId());
            return NodeOperationResult.builder().build();
        } catch (Exception e) {
           throw new RuntimeException("删除节点失败: " + e.getMessage(), e);
        }
    }

    /**
     * 处理更新节点
     */
    private NodeOperationResult handleUpdateNode(NodeOperationRequest request, ProcessNode processNode) {
        try {
            workflowEditor.updateNode(processNode, request.getNodeId(), request.getUpdateFields());
            return NodeOperationResult.builder().build();
        } catch (Exception e) {
            throw new RuntimeException("更新节点失败: " + e.getMessage(), e);
        }
    }

    /**
     * 处理查询节点
     */
    private NodeOperationResult handleQueryNode(NodeOperationRequest request, ProcessNode processNode) {
        try {
            // 查询操作直接返回processNode数据
            return NodeOperationResult.builder().build();
        } catch (Exception e) {
            throw new RuntimeException("查询节点失败: " + e.getMessage(), e);
        }
    }

    /**
     * 处理插入节点的Properties数据
     */
    private NodeOperationResult handleInsertNodeProperties(NodeOperationRequest request, NodeOperationResult operationResult) {
        try {
            BaseNodeCanvas insertedNode = operationResult.getResultCanvasNode();
            if (insertedNode != null) {
//                BaseNodeProperties propertiesNode = propertiesDataManager.createPropertiesNode(
//                    insertedNode, request.getProcessConfigId()
//                );
//                propertiesDataManager.savePropertiesNode(propertiesNode);
//                operationResult.setResultPropertiesNode(propertiesNode);
            }
            return operationResult;
        } catch (Exception e) {
            throw new RuntimeException("插入Properties节点失败: " + e.getMessage(), e);
        }
    }

    /**
     * 处理删除节点的Properties数据
     */
    private NodeOperationResult handleDeleteNodeProperties(NodeOperationRequest request, NodeOperationResult operationResult) {
        try {
//            propertiesDataManager.deletePropertiesNode(request.getProcessConfigId(), request.getNodeId());
            return operationResult;
        } catch (Exception e) {
           throw new RuntimeException("删除Properties节点失败: " + e.getMessage(), e);
        }
    }

    /**
     * 处理更新节点的Properties数据
     */
    private NodeOperationResult handleUpdateNodeProperties(NodeOperationRequest request, NodeOperationResult operationResult) {
        try {
            if (request.getPropertiesNode() != null) {
//                propertiesDataManager.updatePropertiesNode(request.getPropertiesNode());
            }
            return operationResult;
        } catch (Exception e) {
            throw new RuntimeException("更新Properties节点失败: " + e.getMessage(), e);
        }
    }

    /**
     * 处理查询节点的Properties数据
     */
    private NodeOperationResult handleQueryNodeProperties(NodeOperationRequest request, NodeOperationResult operationResult) {
        try {
            // 查询Properties数据由NodeQueryOptimizer处理
            return operationResult;
        } catch (Exception e) {
            throw new RuntimeException("查询Properties节点失败: " + e.getMessage(), e);
        }
    }
}



















