package com.mlc.workflow.service.dto;


import com.mlc.base.common.enums.workflow.StartEventAppTypeEnum;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class AddProcessConfigRequest {

    // 流程名称
    private String name;

    // 说明
    private String explain;

    // 关联关系
    private String relationId;

    // 关联类型
    private Integer relationType;

    /**
     * 发起节点app类型
     * {@link StartEventAppTypeEnum}
     */
    private Integer startAppType;

    private String jsonStr;
}
