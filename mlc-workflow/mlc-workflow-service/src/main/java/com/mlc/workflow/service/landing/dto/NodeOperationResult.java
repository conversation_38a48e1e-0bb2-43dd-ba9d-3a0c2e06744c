package com.mlc.workflow.service.landing.dto;

import com.mlc.workflow.core.editor.model.BaseNodeCanvas;
import com.mlc.workflow.core.editor.model.BaseNodeProperties;
import com.mlc.workflow.core.editor.runtime.beans.ProcessNode;
import lombok.Data;
import lombok.Builder;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;
import lombok.Setter;

/**
 * 节点操作结果DTO
 * 
 * <AUTHOR> Generated
 */
@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class NodeOperationResult {

    /**
     * Canvas节点数据
     */
    private ProcessNode canvasData;

    /**
     * Properties节点数据列表
     */
    private List<BaseNodeProperties> propertiesData;

    /**
     * 单个Canvas节点（用于新增、修改操作返回）
     */
    private BaseNodeCanvas resultCanvasNode;

    /**
     * 单个Properties节点（用于新增、修改操作返回）
     */
    private BaseNodeProperties resultPropertiesNode;

    /**
     * 特定逻辑查询结果
     */
    private Map<String, Object> specificLogicData;

    /**
     * 扩展数据
     */
    private Map<String, Object> extensionData;

}
