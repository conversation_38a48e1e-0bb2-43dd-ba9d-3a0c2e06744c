package com.mlc.workflow.service.biz.handler;

import com.mlc.workflow.core.editor.model.BaseNodeCanvas;
import com.mlc.workflow.service.dto.GetNodeDetailRequest;
import com.mlc.workflow.service.biz.ProcessNodeBizModel;
import io.nop.api.core.ioc.BeanContainer;
import io.nop.core.context.IServiceContext;
import java.util.Map;

/**
 * 节点详情处理器抽象基类
 */
public abstract class AbstractNodeDetailHandler implements NodeDetailHandler {



    @Override
    public boolean supports(String nodeType) {
        return getSupportedNodeType().equals(nodeType);
    }

    /**
     * 获取当前处理器支持的节点类型
     *
     * @return 节点类型
     */
    protected abstract String getSupportedNodeType();


    @Override
    public Map<String, Object> getNodeDetail(GetNodeDetailRequest request, IServiceContext context) {
        // 查询基础数据
        String basicResult = this.queryNodeData(request, context);

        // 查询额外数据
        Map<String, Object> addResult = this.queryAdditionalData(request, context);

        // 执行后置处理
        return this.postProcess(addResult, request, context);
    }
    
    /**
     * 查询节点的基础数据
     * 
     * @param request 请求参数
     * @param context 服务上下文
     */
    protected String queryNodeData(GetNodeDetailRequest request, IServiceContext context) {
        ProcessNodeBizModel flowNodeBizModel = BeanContainer.getBeanByType(ProcessNodeBizModel.class);
        return null;
    }
    
    /**
     * 执行具体的节点详情获取逻辑
     * 
     * @param request 请求参数
     * @param context 服务上下文
     * @return 节点详情数据
     */
    protected abstract Map<String, Object> queryAdditionalData(GetNodeDetailRequest request, IServiceContext context);
    
    /**
     * 后置处理逻辑
     * 
     * @param result 处理结果
     * @param request 请求参数
     * @param context 服务上下文
     * @return 处理后的结果
     */
    protected Map<String, Object> postProcess(Map<String, Object> result, GetNodeDetailRequest request, IServiceContext context) {
        // 默认直接返回结果，子类可根据需要覆盖
        return result;
    }

    public void initNodeDetail(BaseNodeCanvas baseNode, IServiceContext context) {
        // 默认不做任何处理，子类可根据需要覆盖
    }

    public void updateNodeDetail(GetNodeDetailRequest request, IServiceContext context) {
        // 默认不做任何处理，子类可根据需要覆盖
    }

    public void deleteNodeDetail(GetNodeDetailRequest request, IServiceContext context) {
        // 默认不做任何处理，子类可根据需要覆盖
    }

}
