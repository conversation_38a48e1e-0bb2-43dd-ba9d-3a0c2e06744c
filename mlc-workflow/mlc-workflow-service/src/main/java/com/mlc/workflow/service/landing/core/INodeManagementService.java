package com.mlc.workflow.service.landing.core;

import com.mlc.workflow.core.editor.runtime.beans.ProcessNode;
import com.mlc.workflow.service.dto.AddProcessConfigRequest;
import com.mlc.workflow.service.dto.GetNodeDetailRequest;
import com.mlc.workflow.service.landing.dto.NodeOperationRequest;
import com.mlc.workflow.service.landing.dto.NodeOperationResult;
import io.nop.core.context.IServiceContext;
import java.util.Map;

/**
 * 工作流节点管理服务接口
 * 提供节点的初始化、增删改查操作的统一入口
 * 
 * <AUTHOR> Generated
 */
public interface INodeManagementService {

    /**
     * 初始化工作流节点
     *
     * @param addProcessConfigBean 新增流程配置参数
     * @param serviceContext 服务上下文
     * @return 初始化结果
     */
    String initializeProcess(AddProcessConfigRequest addProcessConfigBean, IServiceContext serviceContext);

    /**
     * 执行节点操作
     * 支持增加、删除、修改、查询操作
     * 
     * @param request 节点操作请求
     * @return 操作结果
     */
    NodeOperationResult executeNodeOperation(NodeOperationRequest request);

    /**
     * 查询Canvas节点数据
     * 直接查询数据库返回节点数据
     * 
     * @param processConfigId 流程配置ID
     * @return Canvas节点数据
     */
    ProcessNode queryCanvasNode(String processConfigId, IServiceContext context);

    /**
     * 查询Properties节点数据 步骤1：从数据库查询 Canvas节点数据 步骤2：按节点类型执行特定逻辑查询 步骤3：将 Canvas节点数据 与特定逻辑数据组装后返回
     *
     * @param processConfigId 流程配置ID
     * @param nodeId 节点ID
     * @return Properties节点数据
     */
    Map<String, Object> queryPropertiesNode(GetNodeDetailRequest getNodeDetailRequest, IServiceContext context);
}
