package com.mlc.workflow.service.biz.handler.impl;

import com.mlc.base.common.enums.workflow.NodeTypeEnum;
import com.mlc.workflow.core.editor.model.properties.StartEventNodeProperties;
import com.mlc.workflow.core.editor.model.properties.startevent.UserEventTriggerProperties;
import com.mlc.workflow.service.dto.GetNodeDetailRequest;
import com.mlc.workflow.service.biz.handler.AbstractNodeDetailHandler;
import io.nop.core.context.IServiceContext;
import java.util.Map;
import org.springframework.stereotype.Component;

/**
 * 审批节点详情处理器
 */
@Component
public class StartEventNodeDetailHandler extends AbstractNodeDetailHandler {

    @Override
    protected String getSupportedNodeType() {
        return NodeTypeEnum.TYPE_START;
    }

    /**
     * 需求额外查询的数据
     * 1. 当前应用有哪些工作表 appList
     * 2. 选择某一个工作表查询到的对应的控件 controls
     */
    @Override
    protected Map<String, Object> queryAdditionalData(GetNodeDetailRequest request, IServiceContext context) {
        String processId = request.getProcessId();
        String nodeId = request.getNodeId();

        StartEventNodeProperties startEventNode = new UserEventTriggerProperties();

        // 返回JSON格式的节点详情
        return Map.of(
            "nodeType", "start",
            "processId", processId,
            "nodeId", nodeId
        );
    }
}
