package com.mlc.workflow.service.landing.exception;

/**
 * 工作流节点异常类
 * 
 * <AUTHOR> Generated
 */
public class WorkflowNodeException extends RuntimeException {

    private String errorCode;

    public WorkflowNodeException(String message) {
        super(message);
    }

    public WorkflowNodeException(String errorCode, String message) {
        super(message);
        this.errorCode = errorCode;
    }

    public WorkflowNodeException(String message, Throwable cause) {
        super(message, cause);
    }

    public WorkflowNodeException(String errorCode, String message, Throwable cause) {
        super(message, cause);
        this.errorCode = errorCode;
    }

    public String getErrorCode() {
        return errorCode;
    }

    public void setErrorCode(String errorCode) {
        this.errorCode = errorCode;
    }
}

