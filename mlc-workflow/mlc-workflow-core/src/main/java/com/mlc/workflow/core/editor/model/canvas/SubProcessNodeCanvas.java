package com.mlc.workflow.core.editor.model.canvas;


import com.mlc.base.common.enums.workflow.NodeTypeEnum;
import com.mlc.workflow.core.editor.model.visitor.canvas.ICanvasNodeVisitor;
import io.nop.api.core.annotations.data.DataBean;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

/**
 * 子流程节点 (typeId=16)
 * 用于嵌套子流程
 */
@Getter
@Setter
@EqualsAndHashCode(callSuper = true)
@DataBean
public class SubProcessNodeCanvas extends FlowNodeCanvas {

    /**
     * 子流程ID
     */
    private String subProcessId;

    /**
     * 子流程名称
     */
    private String subProcessName;

    public SubProcessNodeCanvas() {
        this.setName("子流程节点");
        this.setTypeId(NodeTypeEnum.SUB_PROCESS.getValue());
    }

    /**
     * 验证子流程节点
     * @return 是否有效
     */
    @Override
    public boolean isValid() {
        return super.isValid() && subProcessId != null && !subProcessId.trim().isEmpty();
    }
    
    /**
     * 实现访问者模式的accept方法
     * @param visitor Canvas节点访问者
     */
    @Override
    public void accept(ICanvasNodeVisitor visitor) {
        visitor.visit(this);
    }
}
