package com.mlc.workflow.core.editor.model.canvas;

import com.mlc.base.common.enums.workflow.NodeTypeEnum;
import com.mlc.workflow.core.editor.model.BaseNodeCanvas;
import com.mlc.workflow.core.editor.model.visitor.canvas.ICanvasNodeVisitor;
import io.nop.api.core.annotations.data.DataBean;
import lombok.Getter;
import lombok.Setter;

/**
 * 获取记录打印文件节点
 * 对应NodeTypeEnum.FILE (18)
 */
@Getter
@Setter
@DataBean
public class FileNodeCanvas extends BaseNodeCanvas {

    public FileNodeCanvas() {
        this.setTypeId(NodeTypeEnum.FILE.getValue());
        this.setName("获取记录打印文件");
    }

    @Override
    public void accept(ICanvasNodeVisitor visitor) {
        visitor.visit(this);
    }
}
