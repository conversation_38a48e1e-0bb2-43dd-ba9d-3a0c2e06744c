package com.mlc.workflow.core.editor.model.visitor.properties;

import com.mlc.workflow.core.editor.model.canvas.startevent.DateFieldTriggerCanvas;
import com.mlc.workflow.core.editor.model.canvas.startevent.ExternalUserEventTriggerCanvas;
import com.mlc.workflow.core.editor.model.canvas.startevent.PBCTriggerCanvas;
import com.mlc.workflow.core.editor.model.canvas.startevent.ScheduleTriggerCanvas;
import com.mlc.workflow.core.editor.model.canvas.startevent.SheetEventTriggerCanvas;
import com.mlc.workflow.core.editor.model.canvas.startevent.UserEventTriggerCanvas;
import com.mlc.workflow.core.editor.model.canvas.startevent.WebhookTriggerCanvas;
import com.mlc.workflow.core.editor.model.properties.ActionNodeProperties;
import com.mlc.workflow.core.editor.model.properties.AigcNodeProperties;
import com.mlc.workflow.core.editor.model.properties.ApiNodeProperties;
import com.mlc.workflow.core.editor.model.properties.ApiPackageNodeProperties;
import com.mlc.workflow.core.editor.model.properties.ApprovalNodeProperties;
import com.mlc.workflow.core.editor.model.properties.ApprovalProcessNodeProperties;
import com.mlc.workflow.core.editor.model.properties.AuthenticationNodeProperties;
import com.mlc.workflow.core.editor.model.properties.BranchNodeProperties;
import com.mlc.workflow.core.editor.model.properties.CcNodeProperties;
import com.mlc.workflow.core.editor.model.properties.CodeNodeProperties;
import com.mlc.workflow.core.editor.model.properties.ConditionNodeProperties;
import com.mlc.workflow.core.editor.model.properties.DelayNodeProperties;
import com.mlc.workflow.core.editor.model.properties.EmailNodeProperties;
import com.mlc.workflow.core.editor.model.properties.FileNodeProperties;
import com.mlc.workflow.core.editor.model.properties.FindMoreMessageNodeProperties;
import com.mlc.workflow.core.editor.model.properties.FindSingleMessageNodeProperties;
import com.mlc.workflow.core.editor.model.properties.FormulaNodeProperties;
import com.mlc.workflow.core.editor.model.properties.GetMoreRecordNodeProperties;
import com.mlc.workflow.core.editor.model.properties.JsonParseNodeProperties;
import com.mlc.workflow.core.editor.model.properties.LinkNodeProperties;
import com.mlc.workflow.core.editor.model.properties.LoopNodeProperties;
import com.mlc.workflow.core.editor.model.properties.MessageNodeProperties;
import com.mlc.workflow.core.editor.model.properties.NotifyNodeProperties;
import com.mlc.workflow.core.editor.model.properties.ParameterNodeProperties;
import com.mlc.workflow.core.editor.model.properties.PbcNodeProperties;
import com.mlc.workflow.core.editor.model.properties.PluginNodeProperties;
import com.mlc.workflow.core.editor.model.properties.PushNodeProperties;
import com.mlc.workflow.core.editor.model.properties.ReturnNodeProperties;
import com.mlc.workflow.core.editor.model.properties.SearchNodeProperties;
import com.mlc.workflow.core.editor.model.properties.SnapshotNodeProperties;
import com.mlc.workflow.core.editor.model.properties.StartEventNodeProperties;
import com.mlc.workflow.core.editor.model.properties.SubProcessNodeProperties;
import com.mlc.workflow.core.editor.model.properties.SystemNodeProperties;
import com.mlc.workflow.core.editor.model.properties.TemplateNodeProperties;
import com.mlc.workflow.core.editor.model.properties.WebhookNodeProperties;
import com.mlc.workflow.core.editor.model.properties.WriteNodeProperties;
import com.mlc.workflow.core.editor.model.properties.startevent.DateFieldTriggerProperties;
import com.mlc.workflow.core.editor.model.properties.startevent.ExternalUserEventTriggerProperties;
import com.mlc.workflow.core.editor.model.properties.startevent.ScheduleTriggerProperties;
import com.mlc.workflow.core.editor.model.properties.startevent.SheetEventTriggerProperties;
import com.mlc.workflow.core.editor.model.properties.startevent.UserEventTriggerProperties;
import com.mlc.workflow.core.editor.model.properties.startevent.WebhookTriggerProperties;

/**
 * 抽象节点访问者基类
 */
public abstract class AbstractNodeVisitor implements INodeVisitor {

    /**
     *  访问开始事件节点
     */
    @Override
    public void visit(StartEventNodeProperties node) {

    }


    /**
     *  访问工作表事件触发节点
     */
    public void visit(SheetEventTriggerProperties node){

    }

    /**
     *  访问Webhook开始事件节点
     */
    @Override
    public void visit(WebhookTriggerProperties node){

    }

    /**
     *  访问按日期字段触发节点
     */
    @Override
    public void visit(DateFieldTriggerProperties node){

    }

    /**
     *  访问外部用户事件触发节点
     */
    @Override
    public void visit(ExternalUserEventTriggerProperties node){

    }

    /**
     *  访问定时开始事件触发节点
     */
    @Override
    public void visit(ScheduleTriggerProperties node){

    }

    /**
     *  访问人员事件触发节点节点
     */
    @Override
    public void visit(UserEventTriggerProperties node){

    }

    /**
     *  访问分支节点
     */
    @Override
    public void visit(BranchNodeProperties node) {
        
    }
    
    /**
     *  访问条件节点
     */
    @Override
    public void visit(ConditionNodeProperties node) {
        
    }
    
    /**
     *  访问写入节点
     */
    @Override
    public void visit(WriteNodeProperties node) {
        
    }
    
    /**
     *  访问审批节点
     */
    @Override
    public void visit(ApprovalNodeProperties node) {
        
    }
    
    /**
     *  访问抄送节点
     */
    @Override
    public void visit(CcNodeProperties node) {
        
    }
    
    /**
     *  访问动作节点
     */
    @Override
    public void visit(ActionNodeProperties node) {
        
    }
    
    /**
     *  访问搜索节点
     * 
     */
    @Override
    public void visit(SearchNodeProperties node) {
        
    }
    
    /**
     *  访问Webhook节点
     * 
     */
    @Override
    public void visit(WebhookNodeProperties node) {
        
    }
    
    /**
     *  访问公式节点
     */
    @Override
    public void visit(FormulaNodeProperties node) {
        
    }
    
    /**
     *  访问消息节点
     */
    @Override
    public void visit(MessageNodeProperties node) {
        
    }
    
    /**
     *  访问邮件节点
     */
    @Override
    public void visit(EmailNodeProperties node) {
        
    }
    
    /**
     *  访问延时节点
     */
    @Override
    public void visit(DelayNodeProperties node) {
        
    }
    
    /**
     *  访问获取更多记录节点
     */
    @Override
    public void visit(GetMoreRecordNodeProperties node) {
        
    }
    
    /**
     *  访问代码节点
     */
    @Override
    public void visit(CodeNodeProperties node) {
        
    }
    
    /**
     *  访问链接节点
     */
    @Override
    public void visit(LinkNodeProperties node) {
        
    }
    
    /**
     *  访问子流程节点
     */
    @Override
    public void visit(SubProcessNodeProperties node) {
        
    }
    
    /**
     *  访问推送节点
     */
    @Override
    public void visit(PushNodeProperties node) {
        
    }
    
    /**
     *  访问文件节点
     */
    @Override
    public void visit(FileNodeProperties node) {
        
    }
    
    /**
     *  访问模板节点
     */
    @Override
    public void visit(TemplateNodeProperties node) {
        
    }
    
    /**
     *  访问PBC节点
     */
    @Override
    public void visit(PbcNodeProperties node) {
        
    }
    
    /**
     *  访问JSON解析节点
     */
    @Override
    public void visit(JsonParseNodeProperties node) {
        
    }
    
    /**
     *  访问认证节点
     */
    @Override
    public void visit(AuthenticationNodeProperties node) {
        
    }
    
    /**
     *  访问参数节点
     */
    @Override
    public void visit(ParameterNodeProperties node) {
        
    }
    
    /**
     *  访问API包节点
     */
    @Override
    public void visit(ApiPackageNodeProperties node) {
        
    }
    
    /**
     *  访问API节点
     */
    @Override
    public void visit(ApiNodeProperties node) {
        
    }
    
    /**
     *  访问审批流程节点
     */
    @Override
    public void visit(ApprovalProcessNodeProperties node) {
        
    }
    
    /**
     *  访问通知节点
     */
    @Override
    public void visit(NotifyNodeProperties node) {
        
    }
    
    /**
     *  访问快照节点
     */
    @Override
    public void visit(SnapshotNodeProperties node) {
        
    }
    
    /**
     *  访问循环节点
     */
    @Override
    public void visit(LoopNodeProperties node) {
        
    }
    
    /**
     *  访问返回节点
     */
    @Override
    public void visit(ReturnNodeProperties node) {
        
    }
    
    /**
     *  访问AIGC节点
     */
    @Override
    public void visit(AigcNodeProperties node) {
        
    }
    
    /**
     *  访问插件节点
     */
    @Override
    public void visit(PluginNodeProperties node) {
        
    }
    
    /**
     *  访问系统节点
     */
    @Override
    public void visit(SystemNodeProperties node) {
        
    }
    
    /**
     *  访问查找单条消息节点
     */
    @Override
    public void visit(FindSingleMessageNodeProperties node) {
        
    }
    
    /**
     *  访问查找多条消息节点
     */
    @Override
    public void visit(FindMoreMessageNodeProperties node) {
    }
    
    /**
     *  访问按日期字段触发节点
     */
    @Override
    public void visit(DateFieldTriggerCanvas node) {
        
    }
    
    /**
     *  访问外部用户事件触发节点
     */
    @Override
    public void visit(UserEventTriggerCanvas node) {
         
    }

    /**
     *  访问外部用户事件触发节点
     */
    @Override
    public void visit(ExternalUserEventTriggerCanvas node) {
         
    }

    /**
     *  访问PBC触发节点
     */
    @Override
    public void visit(PBCTriggerCanvas node) {
         
    }

    /**
     *  访问定时触发节点
     */
    @Override
    public void visit(ScheduleTriggerCanvas node) {
         
    }

    /**
     *  访问表单事件触发节点
     */
    @Override
    public void visit(SheetEventTriggerCanvas node) {
         
    }

    /**
     *  访问Webhook触发节点
     */
    @Override
    public void visit(WebhookTriggerCanvas node) {

    }

}
