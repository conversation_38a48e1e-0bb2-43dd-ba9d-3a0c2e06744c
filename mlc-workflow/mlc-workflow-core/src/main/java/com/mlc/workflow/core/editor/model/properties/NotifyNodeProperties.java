package com.mlc.workflow.core.editor.model.properties;

import com.mlc.base.common.enums.workflow.NodeTypeEnum;
import com.mlc.workflow.core.editor.model.BaseNodeProperties;
import com.mlc.workflow.core.editor.model.visitor.properties.INodeVisitor;
import lombok.Getter;
import lombok.Setter;

/**
 * 站内通知任务节点
 * 对应NodeTypeEnum.NOTICE (27)
 */
@Getter
@Setter
public class NotifyNodeProperties extends BaseNodeProperties {

    public NotifyNodeProperties() {
        this.setFlowNodeType(NodeTypeEnum.NOTICE.getValue());
        this.setName("站内通知任务");
    }

    @Override
    public void accept(INodeVisitor visitor) {
        visitor.visit(this);
    }
}
