package com.mlc.workflow.core.editor.model.canvas.startevent;

import com.mlc.workflow.core.editor.model.canvas.StartEventNodeCanvas;
import com.mlc.workflow.core.editor.model.visitor.canvas.ICanvasNodeVisitor;
import java.util.List;
import lombok.Getter;
import lombok.Setter;

/**
 * 工作表事件触发节点属性 (appType=1)
 */
@Getter
@Setter
public class SheetEventTriggerCanvas extends StartEventNodeCanvas {

    public SheetEventTriggerCanvas() {
        super();
        this.setAppType(1);
        this.setName("工作表事件触发");
    }

    /**
     * 应用名称
     */
    private String appName;

    /**
     * 应用ID
     */
    private String appId;

    /**
     * 分配字段名称列表
     */
    private String assignFieldName;

    /**
     * 分配字段名称列表
     */
    private List<String> assignFieldNames;

    /**
     * 触发ID
     */
    private String triggerId;

    @Override
    public void accept(ICanvasNodeVisitor visitor) {
        visitor.visit(this);
    }

}
