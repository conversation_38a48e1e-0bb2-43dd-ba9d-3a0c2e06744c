package com.mlc.workflow.core.editor.model.properties;

import com.mlc.base.common.enums.workflow.NodeTypeEnum;
import com.mlc.workflow.core.editor.model.BaseNodeProperties;
import com.mlc.workflow.core.editor.model.visitor.properties.INodeVisitor;
import lombok.Getter;
import lombok.Setter;

/**
 * 获取页面快照任务节点
 * 对应NodeTypeEnum.SNAPSHOT (28)
 */
@Getter
@Setter
public class SnapshotNodeProperties extends BaseNodeProperties {

    public SnapshotNodeProperties() {
        this.setFlowNodeType(NodeTypeEnum.SNAPSHOT.getValue());
        this.setName("获取页面快照任务");
    }

    @Override
    public void accept(INodeVisitor visitor) {
        visitor.visit(this);
    }
}
