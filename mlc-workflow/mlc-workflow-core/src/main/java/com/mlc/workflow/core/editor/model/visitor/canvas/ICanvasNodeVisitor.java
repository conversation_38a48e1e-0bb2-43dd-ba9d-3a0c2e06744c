package com.mlc.workflow.core.editor.model.visitor.canvas;

import com.mlc.workflow.core.editor.model.canvas.*;

/**
 * Canvas节点访问者接口（纯净版本，无业务上下文）
 * 
 * 这是为BaseNodeCanvas体系设计的访问者接口，遵循经典的访问者设计模式。
 * 与BaseNodeProperties的INodeVisitor对应，但用于Canvas层的节点处理。
 * 
 * 设计原则：
 * 1. 保持接口纯净，不包含任何业务上下文（如TraverseContext）
 * 2. 为每个具体的Canvas节点类型提供对应的visit方法
 * 3. 支持双重分派机制，实现类型安全的多态调用
 * 
 * 使用场景：
 * - 单节点数据处理和转换
 * - 节点信息提取和统计
 * - 节点验证和校验
 * - 不依赖遍历上下文的操作
 */
public interface ICanvasNodeVisitor {
    
    // ==================== 基础节点类型 ====================
    
    /**
     * 访问起始事件节点
     * @param node 起始事件节点
     */
    void visit(StartEventNodeCanvas node);
    
    /**
     * 访问分支网关节点
     * @param node 分支网关节点
     */
    void visit(GatewayNodeCanvas node);
    
    /**
     * 访问条件节点（分支叶子）
     * @param node 条件节点
     */
    void visit(ConditionNodeCanvas node);
    
    // ==================== 审批相关节点 ====================
    
    /**
     * 访问审批节点
     * @param node 审批节点
     */
    void visit(ApprovalNodeCanvas node);
    
    /**
     * 访问审批流程节点
     * @param node 审批流程节点
     */
    void visit(ApprovalProcessNodeCanvas node);
    
    /**
     * 访问填写节点
     * @param node 填写节点
     */
    void visit(WriteNodeCanvas node);
    
    /**
     * 访问抄送节点
     * @param node 抄送节点
     */
    void visit(CcNodeCanvas node);
    
    // ==================== 通知相关节点 ====================
    
    /**
     * 访问通知节点
     * @param node 通知节点
     */
    void visit(NotifyNodeCanvas node);
    
    /**
     * 访问邮件节点
     * @param node 邮件节点
     */
    void visit(EmailNodeCanvas node);
    
    /**
     * 访问消息节点
     * @param node 消息节点
     */
    void visit(MessageNodeCanvas node);
    
    /**
     * 访问推送节点
     * @param node 推送节点
     */
    void visit(PushNodeCanvas node);
    
    // ==================== 数据处理节点 ====================
    
    /**
     * 访问获取更多记录节点
     * @param node 获取更多记录节点
     */
    void visit(GetMoreRecordNodeCanvas node);
    
    /**
     * 访问搜索节点
     * @param node 搜索节点
     */
    void visit(SearchNodeCanvas node);
    
    /**
     * 访问公式节点
     * @param node 公式节点
     */
    void visit(FormulaNodeCanvas node);
    
    /**
     * 访问JSON解析节点
     * @param node JSON解析节点
     */
    void visit(JsonParseNodeCanvas node);
    
    // ==================== 系统集成节点 ====================
    
    /**
     * 访问子流程节点
     * @param node 子流程节点
     */
    void visit(SubProcessNodeCanvas node);
    
    /**
     * 访问系统节点
     * @param node 系统节点
     */
    void visit(SystemNodeCanvas node);
    
    /**
     * 访问Webhook节点
     * @param node Webhook节点
     */
    void visit(WebhookNodeCanvas node);
    
    /**
     * 访问API节点
     * @param node API节点
     */
    void visit(ApiNodeCanvas node);
    
    /**
     * 访问API包节点
     * @param node API包节点
     */
    void visit(ApiPackageNodeCanvas node);
    
    // ==================== 代码和脚本节点 ====================
    
    /**
     * 访问代码节点
     * @param node 代码节点
     */
    void visit(CodeNodeCanvas node);
    
    /**
     * 访问插件节点
     * @param node 插件节点
     */
    void visit(PluginNodeCanvas node);
    
    // ==================== 流程控制节点 ====================
    
    /**
     * 访问延迟节点
     * @param node 延迟节点
     */
    void visit(DelayNodeCanvas node);
    
    /**
     * 访问循环节点
     * @param node 循环节点
     */
    void visit(LoopNodeCanvas node);
    
    /**
     * 访问返回节点
     * @param node 返回节点
     */
    void visit(ReturnNodeCanvas node);
    
    // ==================== 其他功能节点 ====================
    
    /**
     * 访问文件节点
     * @param node 文件节点
     */
    void visit(FileNodeCanvas node);
    
    /**
     * 访问模板节点
     * @param node 模板节点
     */
    void visit(TemplateNodeCanvas node);
    
    /**
     * 访问链接节点
     * @param node 链接节点
     */
    void visit(LinkNodeCanvas node);
    
    /**
     * 访问快照节点
     * @param node 快照节点
     */
    void visit(SnapshotNodeCanvas node);
    
    /**
     * 访问认证节点
     * @param node 认证节点
     */
    void visit(AuthenticationNodeCanvas node);
    
    /**
     * 访问参数节点
     * @param node 参数节点
     */
    void visit(ParameterNodeCanvas node);
    
    /**
     * 访问PBC节点
     * @param node PBC节点
     */
    void visit(PbcNodeCanvas node);
    
    /**
     * 访问AIGC节点
     * @param node AIGC节点
     */
    void visit(AigcNodeCanvas node);
    
    /**
     * 访问动作节点
     * @param node 动作节点
     */
    void visit(ActionNodeCanvas node);
    
    /**
     * 访问单一消息查找节点
     * @param node 单一消息查找节点
     */
    void visit(FindSingleMessageNodeCanvas node);
    
    /**
     * 访问多消息查找节点
     * @param node 多消息查找节点
     */
    void visit(FindMoreMessageNodeCanvas node);
    
    // ==================== 通用流程节点 ====================
    
    /**
     * 访问通用流程节点（默认处理）
     * @param node 流程节点
     */
    default void visit(FlowNodeCanvas node) {
        // 默认实现，子类可以重写
    }
}

