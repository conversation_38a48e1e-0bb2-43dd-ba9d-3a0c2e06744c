package com.mlc.workflow.core.editor.model.properties.startevent;

import com.mlc.base.common.enums.workflow.AppTypeEnum;
import com.mlc.workflow.core.editor.model.properties.StartEventNodeProperties;
import com.mlc.workflow.core.editor.model.visitor.properties.INodeVisitor;
import java.util.ArrayList;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * 工作表事件触发节点属性 (appType=1)
 */
@Getter
@Setter
public class SheetEventTriggerProperties extends StartEventNodeProperties {

    public SheetEventTriggerProperties() {
        super();
        this.setAppType(AppTypeEnum.SHEET.getValue());
        this.setName("工作表事件触发");
    }

    /**
     * 触发器ID
     */
    private String triggerId = "2";

    /**
     * 操作条件
     */
    private List<String> operateCondition = new ArrayList<>();

    /**
     * 分配字段ID
     */
    private String assignFieldId = "";

    /**
     * 分配字段ID列表
     */
    private List<String> assignFieldIds = new ArrayList<>();

    @Override
    public void accept(INodeVisitor visitor) {
        visitor.visit(this);
    }
}
