package com.mlc.workflow.core.editor.model.canvas;

import com.mlc.base.common.enums.workflow.NodeTypeEnum;
import com.mlc.workflow.core.editor.model.BaseNodeCanvas;
import com.mlc.workflow.core.editor.model.visitor.canvas.ICanvasNodeVisitor;
import io.nop.api.core.annotations.data.DataBean;
import lombok.Getter;
import lombok.Setter;

/**
 * 循环任务节点
 * 对应NodeTypeEnum.LOOP (29)
 */
@Getter
@Setter
@DataBean
public class LoopNodeCanvas extends BaseNodeCanvas {

    public LoopNodeCanvas() {
        this.setTypeId(NodeTypeEnum.LOOP.getValue());
        this.setName("循环任务");
    }

    @Override
    public void accept(ICanvasNodeVisitor visitor) {
        visitor.visit(this);
    }
}
