package com.mlc.workflow.core.editor.model.build;

import com.mlc.base.common.enums.workflow.NodeTypeEnum;
import com.mlc.workflow.core.editor.model.BaseNodeCanvas;
import com.mlc.workflow.core.editor.model.canvas.GetMoreRecordNodeCanvas;
import com.mlc.workflow.core.editor.model.canvas.StartEventNodeCanvas;
import com.mlc.workflow.core.editor.model.canvas.SystemNodeCanvas;
import com.mlc.base.common.enums.workflow.ActionIdEnum;
import com.mlc.base.common.enums.workflow.StartEventAppTypeEnum;
import com.mlc.workflow.core.editor.model.canvas.startevent.SheetEventTriggerCanvas;
import com.mlc.workflow.core.editor.model.canvas.startevent.ScheduleTriggerCanvas;
import com.mlc.workflow.core.editor.model.canvas.startevent.DateFieldTriggerCanvas;
import com.mlc.workflow.core.editor.model.canvas.startevent.WebhookTriggerCanvas;
import com.mlc.workflow.core.editor.model.canvas.startevent.UserEventTriggerCanvas;
import com.mlc.workflow.core.editor.model.canvas.startevent.ExternalUserEventTriggerCanvas;
import com.mlc.workflow.core.editor.model.canvas.startevent.PBCTriggerCanvas;
import com.mlc.workflow.core.editor.structure.manager.EndOwnerManager;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class CanvasNodeBuilder {

    /**
     * 构建系统节点列表
     */
    private final List<BaseNodeCanvas> builtNodes = new ArrayList<>();

    public static CanvasNodeBuilder builder() {
        return new CanvasNodeBuilder();
    }

    /**
     * 构建系统节点列表
     */
    public CanvasNodeBuilder systemNodes() {

        SystemNodeCanvas parameterNode = new SystemNodeCanvas();
        parameterNode.setName("本流程参数");

        SystemNodeCanvas variableNode = new SystemNodeCanvas();
        variableNode.setName("全局变量");

        SystemNodeCanvas systemNode = new SystemNodeCanvas();
        systemNode.setName("系统");

        builtNodes.add(parameterNode);
        builtNodes.add(variableNode);
        builtNodes.add(systemNode);
        return this;
    }

    public CanvasNodeBuilder getMoreRecordNode() {
        GetMoreRecordNodeCanvas getMoreRecordNode = new GetMoreRecordNodeCanvas();
        getMoreRecordNode.setName("人工节点操作明细");
        getMoreRecordNode.setAppType(NodeTypeEnum.GET_MORE_RECORD.getValue());
        getMoreRecordNode.setActionId(ActionIdEnum.FROM_ARTIFICIAL.getValue());
        getMoreRecordNode.setExecute(false);

        builtNodes.add(getMoreRecordNode);
        return this;
    }

    /**
     * 构建开始事件节点
     * 
     * @return FlowNodeBuilder
     */
    public CanvasNodeBuilder startEventNode(Integer startEventAppType) {
        StartEventAppTypeEnum eventTypeEnum = StartEventAppTypeEnum.fromCode(startEventAppType);
        StartEventNodeCanvas startEventNode = this.createStartEventNode(eventTypeEnum);
        builtNodes.add(startEventNode);
        return this;
    }

    /**
     * 实例化并配置开始事件节点
     */
    private StartEventNodeCanvas createStartEventNode(StartEventAppTypeEnum eventType) {
        StartEventNodeCanvas node;
        switch (eventType) {
            case WORKSHEET:
                node = new SheetEventTriggerCanvas();
                break;
            case TIMER:
                node = new ScheduleTriggerCanvas();
                break;
            case DATE_FIELD:
                node = new DateFieldTriggerCanvas();
                break;
            case WEBHOOK:
                node = new WebhookTriggerCanvas();
                node.setIsException(false);
                break;
            case ORG_EVENT:
                node = new UserEventTriggerCanvas();
                node.setIsException(false);
                break;
            case EXTERNAL_USER_EVENT:
                node = new ExternalUserEventTriggerCanvas();
                node.setIsException(false);
                break;
            case PBC:
                node = new PBCTriggerCanvas();
                break;
            default:
                throw new IllegalStateException("未处理的事件类型: " + eventType);
        }

        String name = eventType.name();
        node.setNextId(EndOwnerManager.END_OWNER_ID);
        node.setSelectNodeId(node.getId());
        node.setIsException(true);
        node.setName(name);
        node.setSelectNodeName(name);
        return node;
    }

    public List<BaseNodeCanvas> build() {
        if (builtNodes.isEmpty()) {
            throw new IllegalStateException("请先构建节点");
        }
        return builtNodes;
    }

    public Map<String, BaseNodeCanvas> buildMap() {
        Map<String, BaseNodeCanvas> attributes = new HashMap<>();
        for (BaseNodeCanvas node : build()) {
            attributes.put(node.getId(), node);
        }
        return attributes;
    }
}
