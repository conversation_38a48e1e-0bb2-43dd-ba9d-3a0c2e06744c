package com.mlc.workflow.core.editor.model.canvas.startevent;

import com.mlc.workflow.core.editor.model.canvas.StartEventNodeCanvas;
import com.mlc.workflow.core.editor.model.visitor.canvas.ICanvasNodeVisitor;
import lombok.Getter;
import lombok.Setter;

/**
 * 外部用户事件触发节点属性 (appType=23)
 */
@Getter
@Setter
public class ExternalUserEventTriggerCanvas extends StartEventNodeCanvas {

    public ExternalUserEventTriggerCanvas() {
        super();
        this.setAppType(23);
        this.setName("外部用户事件触发");
    }

    /**
     * 触发ID
     */
    private String triggerId;

    /**
     * 应用类型名称
     */
    private String appTypeName;

    @Override
    public void accept(ICanvasNodeVisitor visitor) {
        visitor.visit(this);
    }
}
