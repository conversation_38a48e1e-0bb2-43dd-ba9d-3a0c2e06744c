package com.mlc.workflow.core.editor.model.canvas.startevent;

import com.mlc.workflow.core.editor.model.canvas.StartEventNodeCanvas;
import com.mlc.workflow.core.editor.model.visitor.canvas.ICanvasNodeVisitor;
import lombok.Getter;
import lombok.Setter;

/**
 * 按日期字段触发节点属性 (appType=6)
 */
@Getter
@Setter
public class DateFieldTriggerCanvas extends StartEventNodeCanvas {

    public DateFieldTriggerCanvas() {
        super();
        this.setAppType(6);
        this.setName("按日期字段触发");
    }

    /**
     * 应用类型名称
     */
    private String appTypeName;

    /**
     * 应用名称
     */
    private String appName;

    /**
     * 应用ID
     */
    private String appId;

    /**
     * 分配字段名称
     */
    private String assignFieldName;

    /**
     * 分配字段名称列表
     */
    private Integer assignFieldType;

    /**
     * 分配字段名称列表
     */
    private Integer executeTimeType;

    /**
     * 提前天数
     */
    private Integer day;

    /**
     * 提前时间
     */
    private String time;

    /**
     * 提前时间（小时和分钟）
     */
    private Integer number;

    /**
     * 提前时间单位（1-分钟，2-小时，3-天，4-周，5-月）
     */
    private Integer unit;

    @Override
    public void accept(ICanvasNodeVisitor visitor) {
        visitor.visit(this);
    }
}
