package com.mlc.workflow.core.editor.structure.utils;

import java.util.ArrayList;
import java.util.List;

/**
 * EndOwner 操作异常
 */
public class EndOwnerException extends RuntimeException {
    
    private final List<String> errors;
    private final List<String> warnings;
    
    public EndOwnerException(String message) {
        super(message);
        this.errors = new ArrayList<>();
        this.warnings = new ArrayList<>();
        this.errors.add(message);
    }
    
    public EndOwnerException(String message, Throwable cause) {
        super(message, cause);
        this.errors = new ArrayList<>();
        this.warnings = new ArrayList<>();
        this.errors.add(message);
    }
    
    public EndOwnerException(List<String> errors) {
        super(String.join("; ", errors));
        this.errors = new ArrayList<>(errors);
        this.warnings = new ArrayList<>();
    }
    
    public EndOwnerException(List<String> errors, List<String> warnings) {
        super(String.join("; ", errors));
        this.errors = new ArrayList<>(errors);
        this.warnings = new ArrayList<>(warnings);
    }
    
    public List<String> getErrors() {
        return new ArrayList<>(errors);
    }
    
    public List<String> getWarnings() {
        return new ArrayList<>(warnings);
    }
    
    public boolean hasWarnings() {
        return !warnings.isEmpty();
    }
}
