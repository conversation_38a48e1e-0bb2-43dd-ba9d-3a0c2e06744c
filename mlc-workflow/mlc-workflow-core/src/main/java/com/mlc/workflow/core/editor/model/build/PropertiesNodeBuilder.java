package com.mlc.workflow.core.editor.model.build;

import com.mlc.base.common.enums.workflow.StartEventAppTypeEnum;
import com.mlc.workflow.core.editor.model.BaseNodeProperties;
import com.mlc.workflow.core.editor.model.properties.GetMoreRecordNodeProperties;
import com.mlc.workflow.core.editor.model.properties.StartEventNodeProperties;
import com.mlc.workflow.core.editor.model.properties.SystemNodeProperties;
import com.mlc.workflow.core.editor.model.properties.startevent.DateFieldTriggerProperties;
import com.mlc.workflow.core.editor.model.properties.startevent.ExternalUserEventTriggerProperties;
import com.mlc.workflow.core.editor.model.properties.startevent.ScheduleTriggerProperties;
import com.mlc.workflow.core.editor.model.properties.startevent.SheetEventTriggerProperties;
import com.mlc.workflow.core.editor.model.properties.startevent.UserEventTriggerProperties;
import com.mlc.workflow.core.editor.model.properties.startevent.WebhookTriggerProperties;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class PropertiesNodeBuilder {

    /**
     * 构建系统节点列表
     */
    private final List<BaseNodeProperties> builtNodes = new ArrayList<>();

    public PropertiesNodeBuilder buildSystemNodes() {

        SystemNodeProperties parameterNode = new SystemNodeProperties();
        parameterNode.setName("本流程参数");

        SystemNodeProperties variableNode = new SystemNodeProperties();
        variableNode.setName("全局变量");

        SystemNodeProperties systemNode = new SystemNodeProperties();
        systemNode.setName("系统");

        builtNodes.add(parameterNode);
        builtNodes.add(variableNode);
        builtNodes.add(systemNode);
        return this;
    }

    public PropertiesNodeBuilder buildGetMoreRecordNode() {
        GetMoreRecordNodeProperties getMoreRecordNode = new GetMoreRecordNodeProperties();
        getMoreRecordNode.setName("人工节点操作明细");
//        getMoreRecordNode.setAppType(AppTypeEnum.VARIABLE.getValue());
//        getMoreRecordNode.setActionId(ActionIdEnum.FROM_ARTIFICIAL.getValue());
//        getMoreRecordNode.setExecute(false);

        builtNodes.add(getMoreRecordNode);
        return this;
    }

    /**
     * 构建开始事件节点
     * 
     * @return FlowNodeBuilder
     */
    public PropertiesNodeBuilder buildStartEventNode(Integer startEventAppType) {
        StartEventAppTypeEnum eventTypeEnum = StartEventAppTypeEnum.fromCode(startEventAppType);
        StartEventNodeProperties startEventNode = this.createStartEventNode(eventTypeEnum);
        builtNodes.add(startEventNode);
        return this;
    }

    /**
     * 实例化并配置开始事件节点
     */
    private StartEventNodeProperties createStartEventNode(StartEventAppTypeEnum eventType) {
        StartEventNodeProperties node = switch (eventType) {
            case WORKSHEET -> new SheetEventTriggerProperties();
            case TIMER -> new ScheduleTriggerProperties();
            case DATE_FIELD -> new DateFieldTriggerProperties();
            case WEBHOOK -> new WebhookTriggerProperties();
            case ORG_EVENT -> new UserEventTriggerProperties();
            case EXTERNAL_USER_EVENT -> new ExternalUserEventTriggerProperties();
            default -> throw new IllegalStateException("未处理的事件类型: " + eventType);
        };

        String eventName = eventType.name();
        node.setName(eventName);
        return node;
    }

    public List<BaseNodeProperties> build() {
        if (builtNodes.isEmpty()) {
            throw new IllegalStateException("请先构建节点");
        }
        return builtNodes;
    }

    public Map<String, BaseNodeProperties> buildMap() {
        Map<String, BaseNodeProperties> attributes = new HashMap<>();
        for (BaseNodeProperties node : build()) {
//            attributes.put(node.getNodeId(), node);
        }
        return attributes;
    }
}
