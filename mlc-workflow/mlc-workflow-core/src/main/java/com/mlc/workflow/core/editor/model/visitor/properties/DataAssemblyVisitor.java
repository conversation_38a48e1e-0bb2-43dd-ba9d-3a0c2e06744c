package com.mlc.workflow.core.editor.model.visitor.properties;

import com.mlc.workflow.core.editor.model.canvas.startevent.WebhookTriggerCanvas;
import com.mlc.workflow.core.editor.model.properties.*;
import lombok.Getter;

import java.util.*;

/**
 * 数据组装访问者
 * 
 * 这是一个具体的访问者实现，专门用于收集和组装工作流节点的数据。
 * 它遵循访问者模式的设计原则：
 * 1. 作为专家：只处理它关心的特定节点类型
 * 2. 作为数据收集器：在内部维护自己的数据存储
 * 3. 提供结果：通过公共方法暴露收集到的数据
 * 
 * 使用场景：
 * - 收集工作流中的关键节点信息
 * - 统计不同类型节点的数量
 * - 提取节点配置数据用于分析
 * - 生成工作流报告
 */
public class DataAssemblyVisitor extends AbstractNodeVisitor {
    
    /**
     * 节点数据收集器 - 存储收集到的节点信息
     */
    @Getter
    private final Map<String, Object> collectedData = new HashMap<>();
    
    /**
     * 节点类型统计器 - 统计各种节点类型的数量
     */
    @Getter
    private final Map<String, Integer> nodeTypeStats = new HashMap<>();
    
    /**
     * 审批节点列表 - 收集所有审批节点的详细信息
     */
    @Getter
    private final List<Map<String, Object>> approvalNodes = new ArrayList<>();
    
    /**
     * 搜索节点列表 - 收集所有搜索节点的详细信息
     */
    @Getter
    private final List<Map<String, Object>> searchNodes = new ArrayList<>();
    
    /**
     * 起始节点信息 - 存储工作流的起始节点配置
     */
    @Getter
    private Map<String, Object> startNodeInfo = null;
    
    /**
     * 填写节点列表 - 收集所有填写节点的详细信息
     */
    @Getter
    private final List<Map<String, Object>> writeNodes = new ArrayList<>();
    
    /**
     * 访问审批节点
     * 收集审批节点的关键信息，包括审批人、审批类型等
     */
    @Override
    public void visit(ApprovalNodeProperties node) {
        // 统计节点类型
        incrementNodeType("审批节点");
        
        // 收集审批节点详细信息
        Map<String, Object> approvalInfo = new HashMap<>();
        approvalInfo.put("name", node.getName());
        approvalInfo.put("desc", node.getDesc());
        approvalInfo.put("nodeType", "审批节点");
        approvalInfo.put("multipleLevelType", node.getMultipleLevelType());
        approvalInfo.put("multipleLevel", node.getMultipleLevel());
        approvalInfo.put("accounts", node.getAccounts());
        approvalInfo.put("countersignType", node.getCountersignType());
        approvalInfo.put("condition", node.getCondition());
        
        approvalNodes.add(approvalInfo);
        
        // 添加到总的数据收集器
        collectedData.put("approvalNode_" + System.currentTimeMillis(), approvalInfo);
        
        System.out.println("✓ 已处理审批节点: " + node.getName());
    }
    
    /**
     * 访问搜索节点
     * 收集搜索节点的配置信息
     */
    @Override
    public void visit(SearchNodeProperties node) {
        // 统计节点类型
        incrementNodeType("搜索节点");
        
        // 收集搜索节点信息
        Map<String, Object> searchInfo = new HashMap<>();
        searchInfo.put("name", node.getName());
        searchInfo.put("desc", node.getDesc());
        searchInfo.put("nodeType", "搜索节点");
        searchInfo.put("flowNodeType", node.getFlowNodeType());
        
        searchNodes.add(searchInfo);
        
        // 添加到总的数据收集器
        collectedData.put("searchNode_" + System.currentTimeMillis(), searchInfo);
        
        System.out.println("✓ 已处理搜索节点: " + node.getName());
    }

    /**
     * 访问起始事件节点
     * 收集工作流的起始配置信息
     */
    @Override
    public void visit(StartEventNodeProperties node) {
        // 统计节点类型
        incrementNodeType("起始节点");
        
        // 收集起始节点信息
        Map<String, Object> startInfo = new HashMap<>();
        startInfo.put("name", node.getName());
        startInfo.put("desc", node.getDesc());
        startInfo.put("nodeType", "起始节点");
        startInfo.put("appType", node.getAppType());

        this.startNodeInfo = startInfo;
        
        // 添加到总的数据收集器
        collectedData.put("startNode", startInfo);
        
        System.out.println("✓ 已处理起始节点: " + node.getName());
    }
    
    /**
     * 访问填写节点
     * 收集填写节点的表单配置信息
     */
    @Override
    public void visit(WriteNodeProperties node) {
        // 统计节点类型
        incrementNodeType("填写节点");
        
        // 收集填写节点信息
        Map<String, Object> writeInfo = new HashMap<>();
        writeInfo.put("name", node.getName());
        writeInfo.put("desc", node.getDesc());
        writeInfo.put("nodeType", "填写节点");
        writeInfo.put("accounts", node.getAccounts());
        writeInfo.put("formProperties", node.getFormProperties());
        writeInfo.put("multipleLevelType", node.getMultipleLevelType());
        writeInfo.put("multipleLevel", node.getMultipleLevel());
        writeInfo.put("countersignType", node.getCountersignType());
        writeInfo.put("condition", node.getCondition());
        writeInfo.put("operationTypeList", node.getOperationTypeList());
        
        writeNodes.add(writeInfo);
        
        // 添加到总的数据收集器
        collectedData.put("writeNode_" + System.currentTimeMillis(), writeInfo);
        
        System.out.println("✓ 已处理填写节点: " + node.getName());
    }
    
    /**
     * 增加节点类型统计
     */
    private void incrementNodeType(String nodeType) {
        nodeTypeStats.put(nodeType, nodeTypeStats.getOrDefault(nodeType, 0) + 1);
    }
    
    /**
     * 获取访问结果摘要
     * 返回本次访问收集到的数据摘要信息
     */
    public Map<String, Object> getResultSummary() {
        Map<String, Object> summary = new HashMap<>();
        summary.put("totalNodesProcessed", collectedData.size());
        summary.put("nodeTypeStats", nodeTypeStats);
        summary.put("approvalNodesCount", approvalNodes.size());
        summary.put("searchNodesCount", searchNodes.size());
        summary.put("writeNodesCount", writeNodes.size());
        summary.put("hasStartNode", startNodeInfo != null);
        
        return summary;
    }
    
    /**
     * 获取所有收集的数据
     * 返回完整的数据收集结果
     */
    public Map<String, Object> getAllResults() {
        Map<String, Object> results = new HashMap<>();
        results.put("collectedData", collectedData);
        results.put("nodeTypeStats", nodeTypeStats);
        results.put("approvalNodes", approvalNodes);
        results.put("searchNodes", searchNodes);
        results.put("writeNodes", writeNodes);
        results.put("startNodeInfo", startNodeInfo);
        results.put("summary", getResultSummary());
        
        return results;
    }
    
    /**
     * 清空所有收集的数据
     * 重置访问者状态，准备下次使用
     */
    public void reset() {
        collectedData.clear();
        nodeTypeStats.clear();
        approvalNodes.clear();
        searchNodes.clear();
        writeNodes.clear();
        startNodeInfo = null;
        
        System.out.println("✓ 数据收集器已重置");
    }
    
    /**
     * 打印收集结果的摘要信息
     */
    public void printSummary() {
        System.out.println("\n=== 数据收集摘要 ===");
        System.out.println("总处理节点数: " + collectedData.size());
        System.out.println("节点类型统计:");
        nodeTypeStats.forEach((type, count) -> 
            System.out.println("  - " + type + ": " + count + "个"));
        
        if (startNodeInfo != null) {
            System.out.println("起始节点: " + startNodeInfo.get("name"));
        }
        
        System.out.println("审批节点数: " + approvalNodes.size());
        System.out.println("搜索节点数: " + searchNodes.size());
        System.out.println("填写节点数: " + writeNodes.size());
        System.out.println("==================");
    }
}
