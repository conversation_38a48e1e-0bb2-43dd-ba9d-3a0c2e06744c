package com.mlc.workflow.core.editor.model.properties;

import com.mlc.base.common.enums.workflow.NodeTypeEnum;
import com.mlc.base.core.worksheet.controls.base.BasicControl;
import com.mlc.workflow.core.editor.model.BaseNodeProperties;
import com.mlc.workflow.core.editor.runtime.beans.ProcessNode;
import com.mlc.workflow.core.editor.model.canvas.FlowNodeCanvas;
import com.mlc.workflow.core.editor.model.visitor.properties.INodeVisitor;
import java.util.List;
import java.util.Map;
import lombok.Getter;
import lombok.Setter;

/**
 * 审批流程节点 (typeId=26)
 * 包含子流程的审批节点
 */
@Getter
@Setter
public class ApprovalProcessNodeProperties extends BaseNodeProperties {

    public ApprovalProcessNodeProperties() {
        this.setFlowNodeType(NodeTypeEnum.APPROVAL_PROCESS.getValue());
        this.setName("审批流程任务");
    }

    private String appId;

    private List<String> accounts;

    private ProcessNode processConfig;

    private List<BasicControl> fields;

    private List<FlowNodeCanvas> flowNodeMap;

    private Boolean addNotAllowView;

    private Map<String, Object> formProperties;

    @Override
    public void accept(INodeVisitor visitor) {
        visitor.visit(this);
    }
}
