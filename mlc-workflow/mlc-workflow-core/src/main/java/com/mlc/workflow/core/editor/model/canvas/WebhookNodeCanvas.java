package com.mlc.workflow.core.editor.model.canvas;

import com.mlc.base.common.enums.workflow.NodeTypeEnum;
import com.mlc.workflow.core.editor.model.BaseNodeCanvas;
import com.mlc.workflow.core.editor.model.visitor.canvas.ICanvasNodeVisitor;
import io.nop.api.core.annotations.data.DataBean;
import lombok.Getter;
import lombok.Setter;

/**
 * 发送 API 请求任务节点
 * 对应NodeTypeEnum.WEBHOOK (8)
 */
@Getter
@Setter
@DataBean
public class WebhookNodeCanvas extends BaseNodeCanvas {

    public WebhookNodeCanvas() {
        this.setTypeId(NodeTypeEnum.WEBHOOK.getValue());
        this.setName("发送 API 请求任务");
    }

    @Override
    public void accept(ICanvasNodeVisitor visitor) {
        visitor.visit(this);
    }
}
