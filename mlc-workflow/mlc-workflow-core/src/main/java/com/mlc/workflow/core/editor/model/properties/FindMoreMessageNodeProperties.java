package com.mlc.workflow.core.editor.model.properties;

import com.mlc.base.common.enums.workflow.NodeTypeEnum;
import com.mlc.workflow.core.editor.model.BaseNodeProperties;
import com.mlc.workflow.core.editor.model.visitor.properties.INodeVisitor;
import lombok.Getter;
import lombok.Setter;

/**
 * 获取多条信息任务节点
 * 对应NodeTypeEnum.FIND_MORE_MESSAGE (1001)
 */
@Getter
@Setter
public class FindMoreMessageNodeProperties extends BaseNodeProperties {

    public FindMoreMessageNodeProperties() {
        this.setFlowNodeType(NodeTypeEnum.FIND_MORE_MESSAGE.getValue());
        this.setName("获取多条信息任务");
    }

    @Override
    public void accept(INodeVisitor visitor) {
        visitor.visit(this);
    }
}
