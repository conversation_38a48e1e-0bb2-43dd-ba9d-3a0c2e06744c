package com.mlc.workflow.core.editor.model.properties.startevent;

import com.mlc.base.common.enums.workflow.AppTypeEnum;
import com.mlc.workflow.core.editor.model.properties.StartEventNodeProperties;
import com.mlc.workflow.core.editor.model.visitor.properties.INodeVisitor;
import java.util.ArrayList;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * 人员事件触发节点属性 (appType=20)
 */
@Getter
@Setter
public class UserEventTriggerProperties extends StartEventNodeProperties {

    public UserEventTriggerProperties() {
        super();
        this.setAppType(AppTypeEnum.USER.getValue());
        this.setName("人员事件触发");
    }


    /**
     * 触发器ID
     */
    private String triggerId = "1";

    /**
     * 操作条件
     */
    private List<String> operateCondition = new ArrayList<>();

    /**
     * 分配字段ID列表
     */
    private List<String> assignFieldIds = new ArrayList<>();

    @Override
    public void accept(INodeVisitor visitor) {
        visitor.visit(this);
    }

}
