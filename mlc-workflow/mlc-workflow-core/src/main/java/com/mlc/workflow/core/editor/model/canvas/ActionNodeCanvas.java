package com.mlc.workflow.core.editor.model.canvas;

import com.mlc.base.common.enums.workflow.NodeTypeEnum;
import com.mlc.workflow.core.editor.model.visitor.canvas.ICanvasNodeVisitor;
import io.nop.api.core.annotations.data.DataBean;
import java.util.ArrayList;
import java.util.List;
import lombok.Getter;
import lombok.Setter;

/**
 * 动作任务节点
 */
@Getter
@Setter
@DataBean
public class ActionNodeCanvas extends FlowNodeCanvas {

    private Integer appType;

    private String appName;

    private String appTypeName;

    private String actionId;

    private List<String> fields = new ArrayList<>();

    public ActionNodeCanvas() {
        this.setTypeId(NodeTypeEnum.ACTION.getValue());
        this.setName("动作任务");
    }

    @Override
    public void accept(ICanvasNodeVisitor visitor) {
        visitor.visit(this);
    }
}
