package com.mlc.workflow.core.editor.model.properties.startevent;

import com.mlc.base.common.enums.workflow.AppTypeEnum;
import com.mlc.workflow.core.editor.model.properties.StartEventNodeProperties;
import com.mlc.workflow.core.editor.model.visitor.properties.INodeVisitor;
import java.util.ArrayList;
import java.util.HashMap;
import lombok.Getter;
import lombok.Setter;

import java.util.List;
import java.util.Map;

/**
 * Webhook触发节点属性 (appType=7)
 */
@Getter
@Setter
public class WebhookTriggerProperties extends StartEventNodeProperties {

    public WebhookTriggerProperties() {
        super();
        this.setAppType(AppTypeEnum.WEBHOOK.getValue());
        this.setName("Webhook触发");
    }

    /**
     * 操作条件
     */
    private List<String> operateCondition = new ArrayList<>();

    /**
     * 分配字段ID列表
     */
    private List<String> assignFieldIds = new ArrayList<>();

    /**
     * Hook URL
     * 这里好像要随机生成一个URL，暂时先留空
     */
    private String hookUrl = "";

    /**
     * JSON 参数
     */
    private String jsonParam = "";

    /**
     * 参数列表
     */
    private List<Map<String, Object>> params = new ArrayList<>();

    /**
     * 返回JSON
     */
    private String returnJson = "";

    /**
     * 返回值列表
     */
    private List<Map<String, Object>> returns = new ArrayList<>();

    /**
     * 钩子主体
     */
    private Boolean hooksBody = false;

    /**
     * 钩子全部
     */
    private Boolean hooksAll = true;

    /**
     * 公式映射
     */
    private Map<String, Object> formulaMap = new HashMap<>();

    @Override
    public void accept(INodeVisitor visitor) {
        visitor.visit(this);
    }
}
