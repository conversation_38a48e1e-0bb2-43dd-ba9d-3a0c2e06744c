package com.mlc.workflow.core.editor.model.properties;

import com.mlc.base.common.enums.workflow.NodeTypeEnum;
import com.mlc.workflow.core.editor.model.BaseNodeProperties;
import com.mlc.workflow.core.editor.model.visitor.properties.INodeVisitor;
import lombok.Getter;
import lombok.Setter;

/**
 * 公式任务节点
 * 对应NodeTypeEnum.FORMULA (9)
 */
@Getter
@Setter
public class FormulaNodeProperties extends BaseNodeProperties {

    public FormulaNodeProperties() {
        this.setFlowNodeType(NodeTypeEnum.FORMULA.getValue());
        this.setName("公式任务");
    }

    @Override
    public void accept(INodeVisitor visitor) {
        visitor.visit(this);
    }
}
