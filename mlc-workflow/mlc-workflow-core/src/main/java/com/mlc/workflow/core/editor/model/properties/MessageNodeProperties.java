package com.mlc.workflow.core.editor.model.properties;

import com.mlc.base.common.enums.workflow.NodeTypeEnum;
import com.mlc.workflow.core.editor.model.BaseNodeProperties;
import com.mlc.workflow.core.editor.model.visitor.properties.INodeVisitor;
import lombok.Getter;
import lombok.Setter;

/**
 * 短信任务节点
 * 对应NodeTypeEnum.MESSAGE (10)
 */
@Getter
@Setter
public class MessageNodeProperties extends BaseNodeProperties {

    public MessageNodeProperties() {
        this.setFlowNodeType(NodeTypeEnum.MESSAGE.getValue());
        this.setName("短信任务");
    }

    @Override
    public void accept(INodeVisitor visitor) {
        visitor.visit(this);
    }
}
