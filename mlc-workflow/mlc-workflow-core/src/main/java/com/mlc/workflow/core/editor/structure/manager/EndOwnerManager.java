package com.mlc.workflow.core.editor.structure.manager;

import com.mlc.workflow.core.editor.runtime.beans.ProcessNode;
import com.mlc.workflow.core.editor.model.BaseNodeCanvas;
import com.mlc.workflow.core.editor.model.canvas.GatewayNodeCanvas;
import com.mlc.workflow.core.editor.model.canvas.capability.IRoutable;
import com.mlc.workflow.core.editor.structure.service.WorkflowQueryService;
import com.mlc.workflow.core.editor.structure.utils.EndOwnerException;
import java.util.ArrayList;
import java.util.List;
import lombok.extern.slf4j.Slf4j;

/**
 * EndOwner 管理器
 * 负责维护流程中 EndOwner 的唯一性和一致性，可能会因为结构扁平化等操作需要重新选择
 * EndOwner 是流程的结束点，nextId="99"，每个流程只能有一个
 * EndOwner 是一种机制，允许多个节点扇入到同一个结束点
 */
@Slf4j
public class EndOwnerManager {

    /**
     * EndOwner 标识
     */
    public static final String END_OWNER_ID = "99";

    // 分支结束符号
    public static final String BRANCH_END_ID = "";
    
    /**
     * 查找当前的 EndOwner
     * @param processNode 流程节点
     * @return EndOwner 节点，如果没有则返回 null
     */
    public BaseNodeCanvas findEndOwner(ProcessNode processNode) {
        if (processNode == null || processNode.getFlowNodeMap() == null) {
            return null;
        }
        
        for (BaseNodeCanvas node : processNode.getFlowNodeMap().values()) {
            if (node instanceof IRoutable routableNode) {
                if (END_OWNER_ID.equals(routableNode.getNextId())) {
                    return node;
                }
            }
        }
        
        return null;
    }
    
    /**
     * 验证 EndOwner 的唯一性
     * @param processNode 流程节点
     * @return 验证结果，true 表示唯一性满足（恰好有一个EndOwner），false 表示不满足
     */
    public boolean validateEndOwnerUnique(ProcessNode processNode) {
        if (processNode == null || processNode.getFlowNodeMap() == null) {
            throw new IllegalArgumentException("ProcessNode 不能为空");
        }

        int endOwnerCount = 0;
        for (BaseNodeCanvas node : processNode.getFlowNodeMap().values()) {
            if (node instanceof IRoutable routableNode) {
                if (END_OWNER_ID.equals(routableNode.getNextId())) {
                    endOwnerCount++;
                }
            }
        }

        // 根据设计方案：同一ProcessNode内，恰有1个节点的nextId=99
        return endOwnerCount == 1;
    }

    /**
     * 统计 EndOwner 数量
     * @param processNode 流程节点
     * @return EndOwner 数量
     */
    public int countEndOwners(ProcessNode processNode) {
        if (processNode == null || processNode.getFlowNodeMap() == null) {
            return 0;
        }

        int count = 0;
        for (BaseNodeCanvas node : processNode.getFlowNodeMap().values()) {
            if (node instanceof IRoutable routableNode) {
                if (END_OWNER_ID.equals(routableNode.getNextId())) {
                    count++;
                }
            }
        }
        return count;
    }

    /**
     * 查找所有 EndOwner 节点
     * @param processNode 流程节点
     * @return EndOwner 节点列表
     */
    public List<BaseNodeCanvas> findAllEndOwners(ProcessNode processNode) {
        List<BaseNodeCanvas> endOwners = new ArrayList<>();
        if (processNode == null || processNode.getFlowNodeMap() == null) {
            return endOwners;
        }

        for (BaseNodeCanvas node : processNode.getFlowNodeMap().values()) {
            if (node instanceof IRoutable routableNode) {
                if (END_OWNER_ID.equals(routableNode.getNextId())) {
                    endOwners.add(node);
                }
            }
        }
        return endOwners;
    }


    /**
     * 设置节点为 EndOwner
     * 严格按照设计方案：任何新增"指向结束"的请求，必须转化为"接到EndOwner之前"
     * @param processNode 流程节点
     * @param node 要设置为 EndOwner 的节点
     * @return 新的 EndOwner 节点
     */
    public BaseNodeCanvas setAsEndOwner(ProcessNode processNode, BaseNodeCanvas node) {
        if (!(node instanceof IRoutable routableNode)) {
            throw new EndOwnerException("节点不是可路由的，无法设置为 EndOwner");
        }

        // 检查当前是否已有 EndOwner
        BaseNodeCanvas currentEndOwner = findEndOwner(processNode);

        if (currentEndOwner != null) {
            if (currentEndOwner.getId().equals(node.getId())) {
                // 节点已经是 EndOwner，无需操作
                log.debug("节点 {} 已经是 EndOwner", node.getId());
                return currentEndOwner;
            } else {
                // 已有其他 EndOwner，根据设计方案，必须扇入到现有 EndOwner
                handleFanInToEndOwner(node, currentEndOwner);
                log.debug("节点 {} 扇入到现有 EndOwner {}", node.getId(), currentEndOwner.getId());
                return currentEndOwner;
            }
        } else {
            // 没有 EndOwner，直接设置为 EndOwner
            routableNode.setNextId(END_OWNER_ID);
            log.debug("设置节点 {} 为流程的第一个 EndOwner", node.getId());
            return node;
        }
    }
    
    /**
     * 处理扇入到 EndOwner
     * 根据设计方案：保证仍只有一个节点的nextId=99，其他节点扇入到EndOwner
     * @param newNode 新节点
     * @param currentEndOwner 当前 EndOwner
     * @throws EndOwnerException 操作失败时抛出
     */
    private void handleFanInToEndOwner(BaseNodeCanvas newNode, BaseNodeCanvas currentEndOwner) {
        if (!(newNode instanceof IRoutable routableNew)) {
            throw new EndOwnerException("新节点不是可路由的");
        }

        if (!(currentEndOwner instanceof IRoutable routableEndOwner)) {
            throw new EndOwnerException("当前EndOwner不是可路由的");
        }

        // 验证当前EndOwner确实指向99
        if (!END_OWNER_ID.equals(routableEndOwner.getNextId())) {
            throw new EndOwnerException("当前EndOwner的nextId不是99，数据不一致");
        }

        // 新节点扇入到现有 EndOwner（形成扇入结构）
        routableNew.setNextId(currentEndOwner.getId());

        log.debug("节点 {} 扇入到 EndOwner {}，保持EndOwner唯一性", newNode.getId(), currentEndOwner.getId());
    }
    
    /**
     * 连接节点到结束
     * 严格按照设计方案的ConnectToEnd原语：
     * - 若当前流程无EndOwner：设置tail.nextId=99，登记为EndOwner
     * - 若已有EndOwner：禁止再设99，设置tail.nextId=E.id，形成扇入
     * @param processNode 流程节点
     * @param baseNodeCanvas 要连接的节点
     * @return 新的 EndOwner 节点
     * @throws EndOwnerException 操作失败时抛出
     */
    public BaseNodeCanvas connectToEnd(ProcessNode processNode, BaseNodeCanvas baseNodeCanvas) {
        if (!(baseNodeCanvas instanceof IRoutable routableNode)) {
            throw new EndOwnerException("节点不是可路由的，无法连接到结束");
        }

        BaseNodeCanvas currentEndOwner = findEndOwner(processNode);

        if (currentEndOwner == null) {
            // 若当前流程无EndOwner：设置tail.nextId=99，登记为EndOwner
            routableNode.setNextId(END_OWNER_ID);
            log.debug("流程无EndOwner，设置节点 {} 为EndOwner", baseNodeCanvas.getId());
            return baseNodeCanvas;
        } else {
            // 若已有EndOwner：禁止再设99，设置tail.nextId=E.id，形成扇入
            handleFanInToEndOwner(baseNodeCanvas, currentEndOwner);
            log.debug("流程已有EndOwner {}，节点 {} 扇入", currentEndOwner.getId(), baseNodeCanvas.getId());
            return currentEndOwner;
        }
    }
    
    /**
     * 修复 EndOwner：当结构扁平化导致 EndOwner 不再是实际末尾时
     * 实现设计方案中的AbortEndOwnerIfFlatten原语
     * @param processNode 流程节点
     * @return 修复后的 EndOwner 节点
     * @throws EndOwnerException 修复失败时抛出
     */
    public BaseNodeCanvas repairEndOwnerAfterFlatten(ProcessNode processNode) {
        // 检查EndOwner唯一性
        List<BaseNodeCanvas> allEndOwners = findAllEndOwners(processNode);
        if (allEndOwners.isEmpty()) {
            throw new EndOwnerException("没有找到 EndOwner，流程结构异常");
        }

        if (allEndOwners.size() > 1) {
            throw new EndOwnerException("发现多个EndOwner，违反唯一性约束: " +
                          allEndOwners.stream().map(BaseNodeCanvas::getId).toList());
        }

        BaseNodeCanvas currentEndOwner = allEndOwners.get(0);

        // 检查是否有其他节点指向 EndOwner
        List<BaseNodeCanvas> prevNodes = WorkflowQueryService.findPrevNodes(processNode, currentEndOwner.getId());

        if (prevNodes.isEmpty()) {
            // EndOwner 没有前驱，检查是否是开始节点
            if (currentEndOwner.getId().equals(processNode.getStartEventId())) {
                log.debug("EndOwner {} 是开始节点，无需修复", currentEndOwner.getId());
                return currentEndOwner;
            } else {
                log.warn("EndOwner {} 没有前驱节点且不是开始节点，可能是孤立节点", currentEndOwner.getId());
                return currentEndOwner;
            }
        }

        // 如果有多个前驱指向 EndOwner，需要重新组织结构
        if (prevNodes.size() > 1) {
            return reorganizeEndOwnerStructure(processNode, currentEndOwner, prevNodes);
        } else {
            // 只有一个前驱，结构正常
            log.debug("EndOwner {} 结构正常，只有一个前驱 {}",
                     currentEndOwner.getId(), prevNodes.get(0).getId());
            return currentEndOwner;
        }
    }
    
    /**
     * 重新组织EndOwner结构
     * 当有多个前驱指向EndOwner时，选择一个作为新的EndOwner，其他扇入
     * @param processNode 流程节点
     * @param currentEndOwner 当前EndOwner
     * @param prevNodes 前驱节点列表
     * @return 新的 EndOwner 节点
     * @throws EndOwnerException 操作失败时抛出
     */
    private BaseNodeCanvas reorganizeEndOwnerStructure(ProcessNode processNode,
                                                      BaseNodeCanvas currentEndOwner,
                                                      List<BaseNodeCanvas> prevNodes) {
        // 选择新的EndOwner
        BaseNodeCanvas newEndOwner = selectNewEndOwner(prevNodes);
        if (!(newEndOwner instanceof IRoutable routableNewEnd)) {
            throw new EndOwnerException("选择的新EndOwner不是可路由的");
        }

        // 设置新的EndOwner
        routableNewEnd.setNextId(END_OWNER_ID);

        // 其他前驱指向新的 EndOwner（形成扇入）
        for (BaseNodeCanvas prevNode : prevNodes) {
            if (!prevNode.getId().equals(newEndOwner.getId()) &&
                prevNode instanceof IRoutable routablePrev) {

                // 检查前驱节点是否是网关的分支叶子
                if (isGatewayBranchLeaf(processNode, prevNode)) {
                    // 网关的分支叶子不应该指向其他节点，保持为空字符串
                    log.debug("跳过网关分支叶子 {} 的nextId修改", prevNode.getId());
                    continue;
                }

                routablePrev.setNextId(newEndOwner.getId());
            }
        }

        // 原 EndOwner 不再是结束节点
        if (currentEndOwner instanceof IRoutable routableOldEnd) {
            routableOldEnd.setNextId("");
        }

        log.debug("重新组织EndOwner结构: {} -> {}，{} 个前驱扇入",
                 currentEndOwner.getId(), newEndOwner.getId(), prevNodes.size() - 1);

        return newEndOwner;
    }

    /**
     * 检查节点是否是网关的分支叶子
     * @param processNode 流程节点
     * @param node 要检查的节点
     * @return 是否是网关的分支叶子
     */
    private boolean isGatewayBranchLeaf(ProcessNode processNode, BaseNodeCanvas node) {
        if (!(node instanceof IRoutable routableNode)) {
            return false;
        }

        String prveId = routableNode.getPrveId();
        if (prveId == null || prveId.trim().isEmpty()) {
            return false;
        }

        BaseNodeCanvas prevNode = processNode.getFlowNodeMap().get(prveId);
        if (!(prevNode instanceof GatewayNodeCanvas gateway)) {
            return false;
        }

        // 检查该节点是否在网关的分支列表中
        List<String> flowIds = gateway.getFlowIds();
        return flowIds != null && flowIds.contains(node.getId());
    }

    /**
     * 选择新的 EndOwner
     * @param candidates 候选节点列表
     * @return 选择的新 EndOwner
     */
    private BaseNodeCanvas selectNewEndOwner(List<BaseNodeCanvas> candidates) {
        if (candidates == null || candidates.isEmpty()) {
            throw new IllegalArgumentException("候选节点列表不能为空");
        }

        // 策略：选择最后一个候选节点作为新的EndOwner
        // 这样可以保持流程的逻辑顺序
        return candidates.get(candidates.size() - 1);
    }
    
    /**
     * 删除 EndOwner 时的处理
     * 确保删除后仍然保持EndOwner唯一性约束
     * @param processNode 流程节点
     * @param pointingNodes 指向被删除EndOwner的所有节点
     * @return 新的 EndOwner 节点，如果没有前驱节点则返回null
     */
    public BaseNodeCanvas handleEndOwnerDeletion(ProcessNode processNode, List<BaseNodeCanvas> pointingNodes) {
        if (pointingNodes.isEmpty()) {
            log.warn("删除EndOwner后没有前驱节点");
            return null;
        }

        try {
            log.info("处理EndOwner删除，有 {} 个前驱节点", pointingNodes.size());

            // 根据设计方案：选择一个稳定策略作为新的EndOwner
            // 这里选择最后一个前驱作为新的EndOwner
            BaseNodeCanvas newEndOwner = pointingNodes.get(pointingNodes.size() - 1);

            if (!(newEndOwner instanceof IRoutable routableNewEnd)) {
                throw new EndOwnerException("选择的新EndOwner不是可路由的");
            }

            // 直接设置新的EndOwner
            routableNewEnd.setNextId(END_OWNER_ID);
            log.info("选择新的EndOwner: {}", newEndOwner.getId());

            // 其他前驱指向新的EndOwner（形成扇入）
            for (int i = 0; i < pointingNodes.size() - 1; i++) {
                BaseNodeCanvas prevNode = pointingNodes.get(i);
                if (prevNode instanceof IRoutable routablePrev) {
                    routablePrev.setNextId(newEndOwner.getId());
                    log.debug("前驱节点 {} 扇入到新EndOwner {}", prevNode.getId(), newEndOwner.getId());
                }
            }

            log.debug("EndOwner删除处理完成，新EndOwner: {}，{} 个前驱扇入",
                      newEndOwner.getId(), pointingNodes.size() - 1);
            
            return newEndOwner;
        } catch (Exception e) {
            log.error("处理EndOwner删除时发生错误", e);
            throw new EndOwnerException("EndOwner删除处理失败: " + e.getMessage(), e);
        }
    }

}
