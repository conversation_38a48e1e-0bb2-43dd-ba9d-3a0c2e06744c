package com.mlc.workflow.core.editor.model.properties;

import com.mlc.base.common.enums.workflow.NodeTypeEnum;
import com.mlc.workflow.core.editor.model.BaseNodeProperties;
import com.mlc.workflow.core.editor.model.visitor.properties.INodeVisitor;
import lombok.Getter;
import lombok.Setter;

/**
 * 获取单条信息任务节点
 * 对应NodeTypeEnum.FIND_SINGLE_MESSAGE (1000)
 */
@Getter
@Setter
public class FindSingleMessageNodeProperties extends BaseNodeProperties {

    public FindSingleMessageNodeProperties() {
        this.setFlowNodeType(NodeTypeEnum.FIND_SINGLE_MESSAGE.getValue());
        this.setName("获取单条信息任务");
    }

    @Override
    public void accept(INodeVisitor visitor) {
        visitor.visit(this);
    }
}
