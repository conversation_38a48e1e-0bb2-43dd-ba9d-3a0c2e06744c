package com.mlc.workflow.core.editor.model.properties;

import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.mlc.base.common.enums.workflow.NodeTypeEnum;
import com.mlc.workflow.core.editor.model.BaseNodeProperties;
import com.mlc.workflow.core.editor.model.canvas.startevent.PBCTriggerCanvas;
import com.mlc.workflow.core.editor.model.properties.startevent.DateFieldTriggerProperties;
import com.mlc.workflow.core.editor.model.properties.startevent.ExternalUserEventTriggerProperties;
import com.mlc.workflow.core.editor.model.properties.startevent.ScheduleTriggerProperties;
import com.mlc.workflow.core.editor.model.properties.startevent.SheetEventTriggerProperties;
import com.mlc.workflow.core.editor.model.properties.startevent.UserEventTriggerProperties;
import com.mlc.workflow.core.editor.model.properties.startevent.WebhookTriggerProperties;
import com.mlc.workflow.core.editor.model.visitor.properties.INodeVisitor;
import io.nop.api.core.annotations.data.DataBean;
import java.util.List;
import java.util.Map;
import lombok.Getter;
import lombok.Setter;

/**
 * 起始事件节点 (typeId=0)
 * 工作流的起始节点，触发工作流的执行
 * @see com.mlc.base.common.enums.workflow.StartEventAppTypeEnum  对应类型enum
 */
@Getter
@Setter
@DataBean
@JsonTypeInfo(
    use = JsonTypeInfo.Id.NAME,
    include = JsonTypeInfo.As.EXISTING_PROPERTY,
    property = "appType",
    visible = true, defaultImpl = SheetEventTriggerProperties.class)
@JsonSubTypes({
    @JsonSubTypes.Type(value = SheetEventTriggerProperties.class, name = "1"),
    @JsonSubTypes.Type(value = ScheduleTriggerProperties.class, name = "5"),
    @JsonSubTypes.Type(value = DateFieldTriggerProperties.class, name = "6"),
    @JsonSubTypes.Type(value = WebhookTriggerProperties.class, name = "7"),
    @JsonSubTypes.Type(value = UserEventTriggerProperties.class, name = "20"),
    @JsonSubTypes.Type(value = ExternalUserEventTriggerProperties.class, name = "23")
})
public abstract class StartEventNodeProperties extends BaseNodeProperties {

    public StartEventNodeProperties() {
        this.setFlowNodeType(NodeTypeEnum.START.getValue());
        this.setName("发起任务");
    }

    /**
     * 应用类型
     */
    private Integer appType;

}
