package com.mlc.workflow.core.editor.model.utils;

import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.mlc.workflow.core.editor.model.BaseNodeProperties;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class PropertiesNodeParseUtil {

    public static final PropertiesNodeParseUtil INSTANCE = new PropertiesNodeParseUtil();

    private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper();

    /**
     * 将对象转换为JSON字符串
     *
     * @param obj 需要转换的对象
     * @return 转换后的JSON字符串
     */
    public String toJsonString(Object obj) {
        try {
            OBJECT_MAPPER.setSerializationInclusion(Include.NON_NULL);
            return OBJECT_MAPPER.writeValueAsString(obj);
        } catch (Exception e) {
            throw new RuntimeException("对象转换为JSON失败: " + e.getMessage(), e);
        }
    }


    /**
     * 将JSON字符串转换为指定泛型类型的对象
     *
     * @param json JSON字符串
     * @param typeReference 目标对象的类型引用
     * @param <T> 目标对象类型
     * @return 转换后的对象
     */
    public <T> T fromJsonString(String json, TypeReference<T> typeReference) {
        try {
            return OBJECT_MAPPER.readValue(json, typeReference);
        } catch (Exception e) {
            throw new RuntimeException("JSON转换为对象失败: " + e.getMessage(), e);
        }
    }

    /**
     * 将 BaseNodeProperties 对象转换为指定类型的对象
     *
     * @param baseNodeProperties BaseNodeProperties 对象
     * @param clazz 目标对象的类类型
     * @param <T> 目标对象类型
     * @return 转换后的对象
     */
    public <T> T fromJsonString(BaseNodeProperties baseNodeProperties, TypeReference<T> typeReference) {
        try {
            String json = toJsonString(baseNodeProperties);
            return OBJECT_MAPPER.readValue(json, typeReference);
        } catch (Exception e) {
            throw new RuntimeException("BaseNodeProperties转换为对象失败: " + e.getMessage(), e);
        }
    }
}
