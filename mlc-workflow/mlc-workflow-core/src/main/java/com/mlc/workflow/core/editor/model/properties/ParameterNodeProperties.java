package com.mlc.workflow.core.editor.model.properties;

import com.mlc.base.common.enums.workflow.NodeTypeEnum;
import com.mlc.workflow.core.editor.model.BaseNodeProperties;
import com.mlc.workflow.core.editor.model.visitor.properties.INodeVisitor;
import lombok.Getter;
import lombok.Setter;

/**
 * 连接参数任务节点
 * 对应NodeTypeEnum.PARAMETER (23)
 */
@Getter
@Setter
public class ParameterNodeProperties extends BaseNodeProperties {

    public ParameterNodeProperties() {
        this.setFlowNodeType(NodeTypeEnum.PARAMETER.getValue());
        this.setName("连接参数任务");
    }

    @Override
    public void accept(INodeVisitor visitor) {
        visitor.visit(this);
    }
}
