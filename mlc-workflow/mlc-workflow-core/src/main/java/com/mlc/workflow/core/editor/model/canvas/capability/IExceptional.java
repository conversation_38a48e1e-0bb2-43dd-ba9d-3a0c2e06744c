package com.mlc.workflow.core.editor.model.canvas.capability;

import com.fasterxml.jackson.annotation.JsonIgnore;

/**
 * 异常处理能力接口
 */
public interface IExceptional {
    
    /**
     * 是否为异常节点
     * @return 是否为异常节点
     */
    Boolean getIsException();
    
    /**
     * 设置是否为异常节点
     * @param isException 是否为异常节点
     */
    void setIsException(Boolean isException);
    
    /**
     * 判断是否为异常节点
     * @return 是否为异常节点
     */
    @JsonIgnore
    default boolean isExceptional() {
        Boolean exception = getIsException();
        return exception != null && exception;
    }
}
