package com.mlc.workflow.core.editor.model.canvas.capability;

import com.mlc.workflow.core.editor.runtime.beans.Account;

import java.util.List;

/**
 * 具有账户信息能力接口
 */
public interface IHasAccounts {
    
    /**
     * 获取账户列表
     * @return 账户列表
     */
    List<Account> getAccounts();
    
    /**
     * 设置账户列表
     * @param accounts 账户列表
     */
    void setAccounts(List<Account> accounts);
    
    /**
     * 添加账户
     * @param account 账户信息
     */
    default void addAccount(Account account) {
        getAccounts().add(account);
    }
    
    /**
     * 验证账户信息是否有效
     * @return 是否有效
     */
    default boolean hasValidAccounts() {
        List<Account> accounts = getAccounts();
        return accounts != null && !accounts.isEmpty() && 
               accounts.stream().allMatch(account -> 
                   account != null && account.getEntityId() != null);
    }
}
