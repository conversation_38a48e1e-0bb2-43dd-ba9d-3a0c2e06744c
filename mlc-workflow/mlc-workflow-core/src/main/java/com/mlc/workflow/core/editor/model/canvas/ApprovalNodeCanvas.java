package com.mlc.workflow.core.editor.model.canvas;

import com.mlc.base.common.enums.workflow.NodeTypeEnum;
import com.mlc.workflow.core.editor.runtime.beans.Account;
import com.mlc.workflow.core.editor.model.canvas.capability.IHasAccounts;
import com.mlc.workflow.core.editor.model.visitor.canvas.ICanvasNodeVisitor;
import io.nop.api.core.annotations.data.DataBean;
import lombok.EqualsAndHashCode;

import java.util.List;
import lombok.Getter;
import lombok.Setter;

/**
 * 审批节点 (typeId=4)
 * 用于审批任务
 */
@Getter
@Setter
@EqualsAndHashCode(callSuper = true)
@DataBean
public class ApprovalNodeCanvas extends FlowNodeCanvas implements IHasAccounts {
    
    /**
     * 账户列表
     */
    private List<Account> accounts;
    
    /**
     * 会签类型
     * 0: 或签（任意一人同意即可）
     * 1: 会签（所有人都需同意）
     */
    private Integer countersignType;
    
    /**
     * 是否会签
     */
    private Boolean countersign;
    
    /**
     * 多级类型
     */
    private Integer multipleLevelType;
    
    /**
     * 是否可回调
     */
    private Boolean isCallBack;

    public ApprovalNodeCanvas() {
        this.setName("审批节点");
        this.setTypeId(NodeTypeEnum.APPROVAL.getValue());
    }

    /**
     * 验证审批节点
     * @return 是否有效
     */
    @Override
    public boolean isValid() {
        return super.isValid() && hasValidAccounts();
    }

    @Override
    public void accept(ICanvasNodeVisitor visitor) {
        visitor.visit(this);
    }
}
