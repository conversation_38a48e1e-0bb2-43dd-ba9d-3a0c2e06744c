package com.mlc.workflow.core.editor.structure.manager;

import com.mlc.base.common.utils.JacksonDeepCopyUtil;
import com.mlc.workflow.core.editor.runtime.beans.ProcessNode;
import com.mlc.workflow.core.editor.model.BaseNodeCanvas;
import com.mlc.workflow.core.editor.model.canvas.capability.IHasSubProcess;
import com.mlc.base.common.utils.ValidationCollector;
import com.mlc.workflow.core.editor.structure.utils.WorkflowValidatorUtil;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.UUID;
import lombok.extern.slf4j.Slf4j;

/**
 * 子流程管理器
 * 处理子流程的递归操作和一致性维护
 */
@Slf4j
public class SubProcessManager {
    
    private final WorkflowValidatorUtil validator;
    
    public SubProcessManager(WorkflowValidatorUtil validator) {
        this.validator = validator;
    }
    
    /**
     * 递归验证所有子流程
     * @param processNode 主流程节点
     * @param collector 验证结果收集器
     */
    public void validateAllSubProcesses(ProcessNode processNode, ValidationCollector collector) {
        if (processNode == null) {
            ValidationCollector.addError(collector, "流程节点不能为空");
            return;
        }
        
        // 验证主流程
        validator.validate(processNode, collector);
        
        // 递归验证所有子流程
        validateSubProcessesRecursive(processNode, collector, new HashSet<>());
    }
    
    /**
     * 递归验证子流程
     * @param processNode 当前流程节点
     * @param collector 验证结果收集器
     * @param visitedProcesses 已访问的流程集合（防止循环引用）
     */
    private void validateSubProcessesRecursive(ProcessNode processNode, ValidationCollector collector, Set<String> visitedProcesses) {
        if (processNode == null || visitedProcesses.contains(processNode.getId())) {
            return;
        }
        
        visitedProcesses.add(processNode.getId());
        
        Map<String, BaseNodeCanvas> flowNodeMap = processNode.getFlowNodeMap();
        if (flowNodeMap == null) {
            return;
        }
        
        for (BaseNodeCanvas node : flowNodeMap.values()) {
            if (node instanceof IHasSubProcess subProcessNode) {
                ProcessNode subProcess = subProcessNode.getProcessNode();
                if (subProcess != null) {
                    // 验证子流程
                    ValidationCollector subCollector = new ValidationCollector();
                    validator.validate(subProcess, subCollector);
                    if (!ValidationCollector.isValid(subCollector)) {
                        ValidationCollector.addError(collector,
                            "子流程 " + node.getId() + " 验证失败: " + 
                            String.join(", ", subCollector.getErrors()));
                    }
                    ValidationCollector.addWarnings(collector, subCollector.getWarnings());
                    
                    // 递归验证子流程的子流程
                    validateSubProcessesRecursive(subProcess, collector, visitedProcesses);
                }
            }
        }
    }
    
    /**
     * 查找所有子流程节点
     * @param processNode 流程节点
     * @return 子流程节点列表
     */
    public List<BaseNodeCanvas> findAllSubProcessNodes(ProcessNode processNode) {
        List<BaseNodeCanvas> subProcessNodes = new ArrayList<>();
        
        if (processNode == null) {
            return subProcessNodes;
        }
        
        findSubProcessNodesRecursive(processNode, subProcessNodes, new HashSet<>());
        
        return subProcessNodes;
    }
    
    /**
     * 递归查找子流程节点
     * @param processNode 当前流程节点
     * @param subProcessNodes 子流程节点列表
     * @param visitedProcesses 已访问的流程集合
     */
    private void findSubProcessNodesRecursive(ProcessNode processNode, 
                                            List<BaseNodeCanvas> subProcessNodes,
                                            Set<String> visitedProcesses) {
        if (processNode == null || visitedProcesses.contains(processNode.getId())) {
            return;
        }
        
        visitedProcesses.add(processNode.getId());
        
        Map<String, BaseNodeCanvas> flowNodeMap = processNode.getFlowNodeMap();
        if (flowNodeMap == null) {
            return;
        }
        
        for (BaseNodeCanvas node : flowNodeMap.values()) {
            if (node instanceof IHasSubProcess subProcessNode) {
                subProcessNodes.add(node);
                
                ProcessNode subProcess = subProcessNode.getProcessNode();
                if (subProcess != null) {
                    // 递归查找子流程的子流程
                    findSubProcessNodesRecursive(subProcess, subProcessNodes, visitedProcesses);
                }
            }
        }
    }
    
    /**
     * 克隆子流程
     * @param originalSubProcess 原始子流程
     * @return 克隆的子流程
     */
    public ProcessNode cloneSubProcess(ProcessNode originalSubProcess) {
        if (originalSubProcess == null) {
            return null;
        }
        
        ProcessNode clonedProcess = JacksonDeepCopyUtil.deepCopy(originalSubProcess);
        
        // 更新流程ID和节点ID以确保唯一性
        clonedProcess.setId(generateNewProcessId());
        
        Map<String, String> idMapping = new HashMap<>();
        Map<String, BaseNodeCanvas> newFlowNodeMap = new HashMap<>();
        
        // 更新所有节点ID
        for (Map.Entry<String, BaseNodeCanvas> entry : clonedProcess.getFlowNodeMap().entrySet()) {
            String oldId = entry.getKey();
            BaseNodeCanvas node = entry.getValue();
            String newId = generateNewNodeId();
            
            node.setId(newId);
            idMapping.put(oldId, newId);
            newFlowNodeMap.put(newId, node);
        }
        
        clonedProcess.setFlowNodeMap(newFlowNodeMap);
        
        // 更新所有引用关系
        updateNodeReferences(newFlowNodeMap, idMapping);
        
        // 更新开始事件ID
        if (originalSubProcess.getStartEventId() != null) {
            String newStartEventId = idMapping.get(originalSubProcess.getStartEventId());
            clonedProcess.setStartEventId(newStartEventId);
        }
        
        log.debug("克隆子流程: {} -> {}", originalSubProcess.getId(), clonedProcess.getId());
        
        return clonedProcess;
    }
    
    
    /**
     * 更新节点引用关系
     * @param clonedNodeMap 克隆的节点映射
     * @param idMapping ID映射关系
     */
    private void updateNodeReferences(Map<String, BaseNodeCanvas> clonedNodeMap, Map<String, String> idMapping) {
        for (BaseNodeCanvas node : clonedNodeMap.values()) {
            // 更新路由引用
            if (node instanceof com.mlc.workflow.core.editor.model.canvas.capability.IRoutable routableNode) {
                String oldNextId = routableNode.getNextId();
                if (oldNextId != null && idMapping.containsKey(oldNextId)) {
                    routableNode.setNextId(idMapping.get(oldNextId));
                }
                
                String oldPrveId = routableNode.getPrveId();
                if (oldPrveId != null && idMapping.containsKey(oldPrveId)) {
                    routableNode.setPrveId(idMapping.get(oldPrveId));
                }
            }
            
            // 更新分支引用
            if (node instanceof com.mlc.workflow.core.editor.model.canvas.capability.IHasBranches branchNode) {
                List<String> oldFlowIds = branchNode.getFlowIds();
                if (oldFlowIds != null) {
                    List<String> newFlowIds = new ArrayList<>();
                    for (String oldFlowId : oldFlowIds) {
                        String newFlowId = idMapping.get(oldFlowId);
                        if (newFlowId != null) {
                            newFlowIds.add(newFlowId);
                        }
                    }
                    branchNode.setFlowIds(newFlowIds);
                }
            }
            
            // 更新子流程引用（递归克隆）
            if (node instanceof IHasSubProcess subProcessNode) {
                ProcessNode originalSubProcess = subProcessNode.getProcessNode();
                if (originalSubProcess != null) {
                    ProcessNode clonedSubProcess = cloneSubProcess(originalSubProcess);
                    subProcessNode.setProcessNode(clonedSubProcess);
                }
            }
        }
    }
    
    /**
     * 删除子流程及其所有内容
     * @param subProcessNode 子流程节点
     */
    public void deleteSubProcess(BaseNodeCanvas subProcessNode) {
        if (!(subProcessNode instanceof IHasSubProcess hasSubProcess)) {
            return;
        }
        
        ProcessNode subProcess = hasSubProcess.getProcessNode();
        if (subProcess == null) {
            return;
        }
        
        log.debug("删除子流程: {}", subProcess.getId());
        
        // 递归删除所有嵌套的子流程
        Map<String, BaseNodeCanvas> flowNodeMap = subProcess.getFlowNodeMap();
        if (flowNodeMap != null) {
            for (BaseNodeCanvas node : flowNodeMap.values()) {
                if (node instanceof IHasSubProcess) {
                    deleteSubProcess(node);
                }
            }
        }
        
        // 清空子流程引用
        hasSubProcess.setProcessNode(null);
    }
    
    /**
     * 生成新的流程ID
     * @return 新的流程ID
     */
    private String generateNewProcessId() {
        return "process-" + UUID.randomUUID().toString().replace("-", "").substring(0, 8);
    }
    
    /**
     * 生成新的节点ID
     * @return 新的节点ID
     */
    private String generateNewNodeId() {
        return "node-" + UUID.randomUUID().toString().replace("-", "").substring(0, 8);
    }
    
    /**
     * 检查子流程循环引用
     * @param processNode 流程节点
     * @return 是否存在循环引用
     */
    public boolean hasCircularReference(ProcessNode processNode) {
        Set<String> visitedProcesses = new HashSet<>();
        return hasCircularReferenceRecursive(processNode, visitedProcesses);
    }
    
    /**
     * 递归检查循环引用
     * @param processNode 当前流程节点
     * @param visitedProcesses 已访问的流程集合
     * @return 是否存在循环引用
     */
    private boolean hasCircularReferenceRecursive(ProcessNode processNode, Set<String> visitedProcesses) {
        if (processNode == null) {
            return false;
        }
        
        String processId = processNode.getId();
        if (visitedProcesses.contains(processId)) {
            return true; // 发现循环引用
        }
        
        visitedProcesses.add(processId);
        
        Map<String, BaseNodeCanvas> flowNodeMap = processNode.getFlowNodeMap();
        if (flowNodeMap != null) {
            for (BaseNodeCanvas node : flowNodeMap.values()) {
                if (node instanceof IHasSubProcess subProcessNode) {
                    ProcessNode subProcess = subProcessNode.getProcessNode();
                    if (hasCircularReferenceRecursive(subProcess, new HashSet<>(visitedProcesses))) {
                        return true;
                    }
                }
            }
        }
        
        return false;
    }
}
