package com.mlc.workflow.core.editor.model.canvas;

import com.mlc.base.common.enums.workflow.NodeTypeEnum;
import com.mlc.workflow.core.editor.model.BaseNodeCanvas;
import com.mlc.workflow.core.editor.model.visitor.canvas.ICanvasNodeVisitor;
import io.nop.api.core.annotations.data.DataBean;
import lombok.Getter;
import lombok.Setter;

/**
 * 强制中止任务节点
 * 对应NodeTypeEnum.RETURN (30)
 */
@Getter
@Setter
@DataBean
public class ReturnNodeCanvas extends BaseNodeCanvas {

    public ReturnNodeCanvas() {
        this.setTypeId(NodeTypeEnum.RETURN.getValue());
        this.setName("强制中止任务");
    }

    @Override
    public void accept(ICanvasNodeVisitor visitor) {
        visitor.visit(this);
    }
}
