package com.mlc.workflow.core.editor.structure.operation;

import com.mlc.base.common.utils.JacksonDeepCopyUtil;
import com.mlc.workflow.core.editor.model.BaseNodeCanvas;
import com.mlc.workflow.core.editor.model.canvas.ConditionNodeCanvas;
import com.mlc.workflow.core.editor.model.canvas.GatewayNodeCanvas;
import com.mlc.workflow.core.editor.runtime.beans.ProcessNode;
import com.mlc.workflow.core.editor.runtime.beans.ConditionGroup;
import com.mlc.workflow.core.editor.model.canvas.capability.IRoutable;
import com.mlc.workflow.core.editor.structure.autowire.AutoWireStrategy;
import com.mlc.workflow.core.editor.structure.autowire.AutoWireStrategy.DetachContext;
import com.mlc.workflow.core.editor.structure.manager.EndOwnerManager;
import com.mlc.workflow.core.editor.structure.utils.GatewaySemanticsStrategyUtil;
import com.mlc.workflow.core.editor.structure.utils.ValidParamsUtil;
import com.mlc.workflow.core.editor.structure.service.WorkflowQueryService;
import com.mlc.workflow.core.editor.structure.service.WorkflowQueryService.LinearChain;
import com.mlc.workflow.core.editor.structure.executor.NodeBatchExecutor;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.LinkedHashSet;
import java.util.Set;
import java.util.UUID;
import lombok.extern.slf4j.Slf4j;

/**
 * 分支操作命令
 * 实现分支的新增、删除、排序、复制等操作
 */
@Slf4j
public class BranchOperations {

    private final AutoWireStrategy autoWireStrategy;
    private final GatewayOperations gatewayOperations;
    private final NodeBatchExecutor nodeBatchExecutor;

    public BranchOperations(NodeBatchExecutor nodeBatchExecutor,
        AutoWireStrategy autoWireStrategy, GatewayOperations gatewayOperations) {
        this.autoWireStrategy = autoWireStrategy;
        this.gatewayOperations = gatewayOperations;
        this.nodeBatchExecutor = nodeBatchExecutor;
    }

    /**
     * 新增分支
     * @param gatewayId 网关ID
     * @param position 插入位置（-1表示末尾）
     * @return 创建的分支叶子节点
     */
    public ConditionNodeCanvas addBranch(String gatewayId, int position) {
        ValidParamsUtil.validateBranchOperationComplete(nodeBatchExecutor, gatewayId, null);

        ProcessNode workingCopy = nodeBatchExecutor.getWorkingCopy();
        GatewayNodeCanvas gateway = ValidParamsUtil.requireGatewayExists(workingCopy, gatewayId);

        // 创建新分支叶子
        ConditionNodeCanvas newBranch = new ConditionNodeCanvas();
        newBranch.setName("分支");
        newBranch.setPrveId(gatewayId);
        newBranch.setNextId(EndOwnerManager.BRANCH_END_ID); // 空分支，等待后续添加内容

        nodeBatchExecutor.createNode(newBranch);

        // 插入到指定位置
        List<String> flowIds = gateway.getFlowIds();
        int normalizedPosition = ValidParamsUtil.validateAndNormalizePosition(position, flowIds.size());
        if (normalizedPosition >= flowIds.size()) {
            flowIds.add(newBranch.getId());
        } else {
            flowIds.add(normalizedPosition, newBranch.getId());
        }

        // 更新网关节点
        nodeBatchExecutor.updateNode(gatewayId, gateway);

        // 如果是唯一分支网关，为新分支生成默认条件（非else）
        if (gateway.getGatewayType() != null && gateway.getGatewayType() == GatewaySemanticsStrategyUtil.GATEWAY_TYPE_EXCLUSIVE) {
            // 新分支总是生成非else条件，避免冲突
            generateNonElseConditionForBranch(newBranch, flowIds.size() - 1);
            nodeBatchExecutor.updateNode(newBranch.getId(), newBranch);
            log.debug("为新分支 {} 生成非else条件", newBranch.getId());
        }

        log.debug("为网关 {} 新增分支 {}，位置: {}", gatewayId, newBranch.getId(), position);

        return newBranch;
    }

    /**
     * 删除分支
     * @param gatewayId 网关ID
     * @param branchLeafId 分支叶子ID
     */
    public void deleteBranch(String gatewayId, String branchLeafId) {
        ValidParamsUtil.validateBranchOperationComplete(nodeBatchExecutor, gatewayId, branchLeafId);

        ProcessNode workingCopy = nodeBatchExecutor.getWorkingCopy();
        GatewayNodeCanvas gateway = ValidParamsUtil.requireGatewayExists(workingCopy, gatewayId);
        ValidParamsUtil.requireBranchBelongsToGateway(gateway, branchLeafId);

        List<String> flowIds = gateway.getFlowIds();
        if (flowIds.size() <= 1) {
            log.info("网关 {} 仅剩一条分支，触发扁平化删除", gatewayId);
            gatewayOperations.deleteGateway(gatewayId);
            return;
        }

        BaseNodeCanvas branchLeafNode = ValidParamsUtil.requireNodeExists(workingCopy, branchLeafId, "分支叶子");
        ConditionNodeCanvas branchLeaf = ValidParamsUtil.requireNodeType(branchLeafNode, ConditionNodeCanvas.class, branchLeafId);

        LinearChain branchChain = WorkflowQueryService.collectLinearChain(workingCopy, branchLeaf.getNextId());
        boolean containsEndOwner = EndOwnerManager.END_OWNER_ID.equals(branchLeaf.getNextId())
            || branchChain.containsEndOwner();

        Set<BaseNodeCanvas> dirtyNodes = new LinkedHashSet<>();

        if (!branchChain.isEmpty()) {
            BaseNodeCanvas branchHead = branchChain.head();
            BaseNodeCanvas branchTail = branchChain.tail();
            DetachContext detachContext = autoWireStrategy.detach(workingCopy, branchHead, branchTail);

            for (BaseNodeCanvas prev : detachContext.getPrevNodes()) {
                if (!prev.getId().equals(branchLeafId)) {
                    dirtyNodes.add(prev);
                }
            }

            for (int i = branchChain.nodes().size() - 1; i >= 0; i--) {
                BaseNodeCanvas node = branchChain.nodes().get(i);
                nodeBatchExecutor.deleteNode(node.getId());
                log.debug("删除分支链节点: {}", node.getId());
            }
        }

        nodeBatchExecutor.deleteNode(branchLeafId);
        flowIds.remove(branchLeafId);
        boolean requiresFlatten = flowIds.size() == 1;
        dirtyNodes.add(gateway);

        if (containsEndOwner) {
            BaseNodeCanvas repairedEndOwner = autoWireStrategy.abortEndOwnerIfFlatten(workingCopy);
            if (repairedEndOwner != null) {
                nodeBatchExecutor.updateNode(repairedEndOwner.getId(), repairedEndOwner);
                log.debug("EndOwner修复完成，当前EndOwner: {}", repairedEndOwner.getId());
            }
        }

        if (gateway.getGatewayType() != null &&
            gateway.getGatewayType() == GatewaySemanticsStrategyUtil.GATEWAY_TYPE_EXCLUSIVE) {
            ensureElseUniqueness(workingCopy, gateway);
        }

        for (BaseNodeCanvas dirty : dirtyNodes) {
            nodeBatchExecutor.updateNode(dirty.getId(), dirty);
        }

        ensureBranchNextIdConsistency(workingCopy, gatewayId);
        if (requiresFlatten) {
            log.info("网关 {} 删除分支后只剩一条，继续触发扁平化", gatewayId);
            gatewayOperations.deleteGateway(gatewayId);
            return;
        }
        log.debug("删除网关 {} 的分支 {} 完成，剩余分支数 {}", gatewayId, branchLeafId, flowIds.size());
    }

    /**
     * 确保分支nextId的一致性
     * 修复可能被错误设置的分支nextId
     */
    private void ensureBranchNextIdConsistency(ProcessNode processNode, String gatewayId) {
        GatewayNodeCanvas gateway = WorkflowQueryService.findGateway(processNode, gatewayId);
        if (gateway == null) {
            return;
        }

        List<String> flowIds = gateway.getFlowIds();
        if (flowIds == null) {
            return;
        }

        for (String branchId : flowIds) {
            BaseNodeCanvas branch = processNode.getFlowNodeMap().get(branchId);
            if (branch instanceof IRoutable routableBranch) {
                String branchNextId = routableBranch.getNextId();
                if (gatewayId.equals(branchNextId)) {
                    // 分支叶子不应该指向网关，修复为空字符串
                    routableBranch.setNextId(EndOwnerManager.BRANCH_END_ID);
                    nodeBatchExecutor.updateNode(branchId, branch);
                    log.debug("修复分支 {} 的nextId，从网关 {} 改为空字符串", branchId, gatewayId);
                }
            }
        }
    }

    /**
     * 调整分支顺序
     * @param gatewayId 网关ID
     * @param newOrder 新的分支顺序
     */
    public void reorderBranches(String gatewayId, List<String> newOrder) {
        ValidParamsUtil.requireAllNonNull(nodeBatchExecutor, "nodeBatchExecutor", gatewayId, "gatewayId", newOrder, "newOrder");

        ProcessNode workingCopy = nodeBatchExecutor.getWorkingCopy();
        GatewayNodeCanvas gateway = ValidParamsUtil.requireGatewayExists(workingCopy, gatewayId);

        List<String> currentFlowIds = gateway.getFlowIds();
        ValidParamsUtil.validateOrderValidity(currentFlowIds, newOrder);

        // 更新分支顺序
        gateway.setFlowIds(new ArrayList<>(newOrder));

        nodeBatchExecutor.updateNode(gatewayId, gateway);

        // 如果是唯一分支，同步更新优先序和else位置
        if (gateway.getGatewayType() != null && gateway.getGatewayType() == GatewaySemanticsStrategyUtil.GATEWAY_TYPE_EXCLUSIVE) {
            updateExclusiveBranchPriority(workingCopy, gateway);
        }

        log.debug("调整网关 {} 的分支顺序", gatewayId);
    }

    /**
     * 复制分支
     * @param gatewayId 网关ID
     * @param branchLeafId 要复制的分支叶子ID
     * @param position 插入位置（-1表示末尾）
     * @return 复制的分支叶子节点
     */
    public ConditionNodeCanvas duplicateBranch(String gatewayId, String branchLeafId, int position) {
        ValidParamsUtil.validateBranchOperationComplete(nodeBatchExecutor, gatewayId, branchLeafId);

        ProcessNode workingCopy = nodeBatchExecutor.getWorkingCopy();
        GatewayNodeCanvas gateway = ValidParamsUtil.requireGatewayExists(workingCopy, gatewayId);

        // 获取原分支链
        List<BaseNodeCanvas> originalChain = WorkflowQueryService.findBranchChain(workingCopy, branchLeafId);
        if (originalChain.isEmpty()) {
            throw new IllegalArgumentException("找不到分支链");
        }

        // 深拷贝分支链
        Map<String, String> idMapping = new HashMap<>();
        List<BaseNodeCanvas> copiedChain = deepCopyBranchChain(originalChain, idMapping);

        for (BaseNodeCanvas copiedNode : copiedChain) {
            nodeBatchExecutor.createNode(copiedNode);
        }

        Set<BaseNodeCanvas> dirtyNodes = new HashSet<>(copiedChain);

        // 获取复制的分支叶子
        ConditionNodeCanvas copiedBranchLeaf = (ConditionNodeCanvas) copiedChain.get(0);
        copiedBranchLeaf.setPrveId(gatewayId);
        dirtyNodes.add(copiedBranchLeaf);

        // 处理EndOwner问题
        dirtyNodes.addAll(handleEndOwnerInCopiedBranch(workingCopy, copiedChain, originalChain));

        // 插入到网关的flowIds
        List<String> flowIds = gateway.getFlowIds();
        int normalizedPosition = ValidParamsUtil.validateAndNormalizePosition(position, flowIds.size());
        if (normalizedPosition >= flowIds.size()) {
            flowIds.add(copiedBranchLeaf.getId());
        } else {
            flowIds.add(normalizedPosition, copiedBranchLeaf.getId());
        }

        // 更新网关节点
        nodeBatchExecutor.updateNode(gatewayId, gateway);

        for (BaseNodeCanvas dirtyNode : dirtyNodes) {
            nodeBatchExecutor.updateNode(dirtyNode.getId(), dirtyNode);
        }

        // 如果是唯一分支，处理条件冲突
        if (gateway.getGatewayType() != null && gateway.getGatewayType() == GatewaySemanticsStrategyUtil.GATEWAY_TYPE_EXCLUSIVE) {
            handleConditionConflicts(workingCopy, gateway, copiedBranchLeaf);
            nodeBatchExecutor.updateNode(copiedBranchLeaf.getId(), copiedBranchLeaf);
        }

        log.debug("复制网关 {} 的分支 {} 为 {}", gatewayId, branchLeafId, copiedBranchLeaf.getId());

        return copiedBranchLeaf;
    }

    /**
     * 深拷贝分支链
     */
    private List<BaseNodeCanvas> deepCopyBranchChain(List<BaseNodeCanvas> originalChain, Map<String, String> idMapping) {
        List<BaseNodeCanvas> copiedChain = new ArrayList<>();

        for (BaseNodeCanvas originalNode : originalChain) {
            BaseNodeCanvas copiedNode = JacksonDeepCopyUtil.deepCopy(originalNode);
            String newId = UUID.randomUUID().toString().replace("-", "");
            idMapping.put(originalNode.getId(), newId);
            copiedNode.setId(newId);
            copiedChain.add(copiedNode);
        }

        // 更新复制节点之间的连接关系
        for (int i = 0; i < copiedChain.size(); i++) {
            BaseNodeCanvas copiedNode = copiedChain.get(i);
            BaseNodeCanvas originalNode = originalChain.get(i);

            if (copiedNode instanceof IRoutable copiedRoutable &&
                originalNode instanceof IRoutable originalRoutable) {
                String originalNextId = originalRoutable.getNextId();
                if (originalNextId != null && idMapping.containsKey(originalNextId)) {
                    copiedRoutable.setNextId(idMapping.get(originalNextId));
                } else {
                    copiedRoutable.setNextId(originalNextId);
                }

                String originalPrevId = originalRoutable.getPrveId();
                if (originalPrevId != null && idMapping.containsKey(originalPrevId)) {
                    copiedRoutable.setPrveId(idMapping.get(originalPrevId));
                } else {
                    copiedRoutable.setPrveId(originalPrevId);
                }
            }
        }

        return copiedChain;
    }


    /**
     * 处理复制分支中的EndOwner问题
     * 根据设计方案：若原链尾是EndOwner，禁止让拷贝链尾也设99，而是令新尾nextId=原尾.id（扇入）
     */
    private Set<BaseNodeCanvas> handleEndOwnerInCopiedBranch(ProcessNode processNode,
        List<BaseNodeCanvas> copiedChain, List<BaseNodeCanvas> originalChain) {
        log.debug("处理复制分支中的EndOwner问题，复制链长度: {}", copiedChain.size());
        Set<BaseNodeCanvas> dirtyNodes = new HashSet<>();

        for (int i = 0; i < copiedChain.size(); i++) {
            BaseNodeCanvas copiedNode = copiedChain.get(i);
            BaseNodeCanvas originalNode = originalChain.get(i);

            if (originalNode instanceof IRoutable originalRoutable &&
                EndOwnerManager.END_OWNER_ID.equals(originalRoutable.getNextId()) &&
                copiedNode instanceof IRoutable) {
                autoWireStrategy.connectToEnd(processNode, copiedNode);
                dirtyNodes.add(copiedNode);
                log.debug("复制的节点 {} 扇入到EndOwner", copiedNode.getId());
            }
        }

        return dirtyNodes;
    }

    /**
     * 确保else条件的唯一性
     */
    private void ensureElseUniqueness(ProcessNode processNode, GatewayNodeCanvas gateway) {
        List<String> flowIds = gateway.getFlowIds();
        if (flowIds == null || flowIds.isEmpty()) {
            return;
        }

        for (int i = 0; i < flowIds.size(); i++) {
            BaseNodeCanvas branchNode = processNode.getFlowNodeMap().get(flowIds.get(i));
            if (!(branchNode instanceof ConditionNodeCanvas conditionNode)) {
                continue;
            }

            if (i == flowIds.size() - 1) {
                ensureElseCondition(conditionNode);
            } else if (hasElseCondition(conditionNode)) {
                generateNonElseConditionForBranch(conditionNode, i);
            }
        }

        log.debug("确保网关 {} 的else条件唯一性完成", gateway.getId());
    }

    /**
     * 更新唯一分支的优先序
     */
    private void updateExclusiveBranchPriority(ProcessNode processNode, GatewayNodeCanvas gateway) {
        // 这里应该根据新顺序更新分支的优先级
        log.debug("更新网关 {} 的分支优先序", gateway.getId());
    }

    /**
     * 处理条件冲突
     */
    private void handleConditionConflicts(ProcessNode processNode, GatewayNodeCanvas gateway, ConditionNodeCanvas newBranch) {
        log.debug("处理网关 {} 新分支 {} 的条件冲突", gateway.getId(), newBranch.getId());

        // 为新分支生成非else条件（避免多个else分支）
        List<String> flowIds = gateway.getFlowIds();
        int branchIndex = flowIds.indexOf(newBranch.getId());
        if (branchIndex >= 0) {
            // 总是生成非else条件，避免冲突
            generateNonElseConditionForBranch(newBranch, branchIndex);
            log.debug("为新分支 {} 生成非else条件", newBranch.getId());
        } else {
            log.warn("无法找到新分支 {} 在网关 {} 中的位置", newBranch.getId(), gateway.getId());
        }
    }

    /**
     * 为分支生成非else条件
     */
    private void generateNonElseConditionForBranch(ConditionNodeCanvas branch, int index) {
        if (branch.getOperateCondition() == null || branch.getOperateCondition().isEmpty()) {
            List<List<ConditionGroup>> defaultConditions = new ArrayList<>();
            List<ConditionGroup> conditionGroup = new ArrayList<>();

            // 创建一个简单的条件组（非else）
            ConditionGroup condition = new ConditionGroup();
            condition.setNodeId("system");  // 设置必需的 nodeId
            condition.setNodeName("系统");
            condition.setFiledId("condition_" + (index + 1));
            condition.setFiledValue("始终成立");
            condition.setConditionId("default_condition_" + (index + 1));
            condition.setValue("true");

            conditionGroup.add(condition);
            defaultConditions.add(conditionGroup);
            branch.setOperateCondition(defaultConditions);

            log.debug("为分支 {} 生成非else条件: {}", branch.getId(), condition.getFiledId());
        } else if (hasElseCondition(branch)) {
            // 已存在else条件但不应出现在非末尾分支，替换为默认条件
            branch.setOperateCondition(null);
            generateNonElseConditionForBranch(branch, index);
        }
    }

    private void ensureElseCondition(ConditionNodeCanvas branch) {
        if (hasElseCondition(branch)) {
        }
    }

    private boolean hasElseCondition(ConditionNodeCanvas branch) {
        return false;
    }
}
