package com.mlc.workflow.core.editor.model.canvas;

import com.mlc.base.common.enums.workflow.NodeTypeEnum;
import com.mlc.workflow.core.editor.model.BaseNodeCanvas;
import com.mlc.workflow.core.editor.model.visitor.canvas.ICanvasNodeVisitor;
import io.nop.api.core.annotations.data.DataBean;
import lombok.Getter;
import lombok.Setter;

/**
 * 代码块任务节点
 * 对应NodeTypeEnum.CODE (14)
 */
@Getter
@Setter
@DataBean
public class CodeNodeCanvas extends BaseNodeCanvas {

    public CodeNodeCanvas() {
        this.setTypeId(NodeTypeEnum.CODE.getValue());
        this.setName("代码块任务");
    }

    @Override
    public void accept(ICanvasNodeVisitor visitor) {
        visitor.visit(this);
    }
}
