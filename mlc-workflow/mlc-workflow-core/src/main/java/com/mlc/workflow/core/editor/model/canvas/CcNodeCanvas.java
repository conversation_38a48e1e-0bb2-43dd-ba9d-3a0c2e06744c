package com.mlc.workflow.core.editor.model.canvas;

import com.mlc.base.common.enums.workflow.NodeTypeEnum;
import com.mlc.workflow.core.editor.runtime.beans.Account;
import com.mlc.workflow.core.editor.model.canvas.capability.IHasAccounts;
import com.mlc.workflow.core.editor.model.visitor.canvas.ICanvasNodeVisitor;
import io.nop.api.core.annotations.data.DataBean;
import lombok.EqualsAndHashCode;

import java.util.List;
import lombok.Getter;
import lombok.Setter;

/**
 * 抄送节点 (typeId=5)
 * 用于抄送通知
 */
@Getter
@Setter
@EqualsAndHashCode(callSuper = true)
@DataBean
public class CcNodeCanvas extends FlowNodeCanvas implements IHasAccounts {
    
    /**
     * 账户列表
     */
    private List<Account> accounts;

    public CcNodeCanvas() {
        this.setName("抄送节点");
        this.setTypeId(NodeTypeEnum.CC.getValue());
    }

    /**
     * 验证抄送节点
     * @return 是否有效
     */
    @Override
    public boolean isValid() {
        return super.isValid() && hasValidAccounts();
    }

    @Override
    public void accept(ICanvasNodeVisitor visitor) {
        visitor.visit(this);
    }
}
