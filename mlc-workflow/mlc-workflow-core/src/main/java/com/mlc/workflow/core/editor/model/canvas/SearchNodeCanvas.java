package com.mlc.workflow.core.editor.model.canvas;

import com.mlc.base.common.enums.workflow.NodeTypeEnum;
import com.mlc.workflow.core.editor.model.BaseNodeCanvas;
import com.mlc.workflow.core.editor.model.visitor.canvas.ICanvasNodeVisitor;
import io.nop.api.core.annotations.data.DataBean;
import lombok.Getter;
import lombok.Setter;

/**
 * 获取单条数据任务节点
 * 对应NodeTypeEnum.SEARCH (7)
 */
@Getter
@Setter
@DataBean
public class SearchNodeCanvas extends BaseNodeCanvas {

    public SearchNodeCanvas() {
        this.setTypeId(NodeTypeEnum.SEARCH.getValue());
        this.setName("获取单条数据任务");
    }

    @Override
    public void accept(ICanvasNodeVisitor visitor) {
        visitor.visit(this);
    }
}
