package com.mlc.workflow.core.editor.model.properties;

import com.mlc.base.common.enums.workflow.NodeTypeEnum;
import com.mlc.workflow.core.editor.model.BaseNodeProperties;
import com.mlc.workflow.core.editor.model.visitor.properties.INodeVisitor;
import lombok.Getter;
import lombok.Setter;

/**
 * 延时任务节点
 * 对应NodeTypeEnum.DELAY (12)
 */
@Getter
@Setter
public class DelayNodeProperties extends BaseNodeProperties {

    public DelayNodeProperties() {
        this.setFlowNodeType(NodeTypeEnum.DELAY.getValue());
        this.setName("延时任务");
    }

    @Override
    public void accept(INodeVisitor visitor) {
        visitor.visit(this);
    }
}
