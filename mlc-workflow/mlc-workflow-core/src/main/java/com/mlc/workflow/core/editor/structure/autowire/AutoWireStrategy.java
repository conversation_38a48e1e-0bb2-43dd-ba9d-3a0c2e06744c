package com.mlc.workflow.core.editor.structure.autowire;

import com.mlc.workflow.core.editor.runtime.beans.ProcessNode;
import com.mlc.workflow.core.editor.model.BaseNodeCanvas;
import com.mlc.workflow.core.editor.model.canvas.capability.IRoutable;
import com.mlc.workflow.core.editor.structure.utils.ValidParamsUtil;
import com.mlc.workflow.core.editor.structure.service.WorkflowQueryService;
import com.mlc.workflow.core.editor.structure.manager.EndOwnerManager;
import java.util.List;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

/**
 * 通用自动连线策略
 * 实现设计方案中的核心连线原语：Detach、SpliceBetween、Replace、ConnectToEnd等
 */
@Slf4j
public class AutoWireStrategy {
    
    private final EndOwnerManager endOwnerManager;
    
    public AutoWireStrategy(EndOwnerManager endOwnerManager) {
        this.endOwnerManager = endOwnerManager;
    }
    
    /**
     * 断开操作：将指定的链段从流程中断开
     * @param processNode 流程节点
     * @param head 链段头节点
     * @param tail 链段尾节点
     * @return 断开操作的上下文信息
     */
    public DetachContext detach(ProcessNode processNode, BaseNodeCanvas head, BaseNodeCanvas tail) {
        // 使用统一的参数校验
        ValidParamsUtil.validateAutoWireParams(processNode, head, tail);

        DetachContext context = new DetachContext();

        // 找到所有指向head的前驱节点
        List<BaseNodeCanvas> prevNodes = WorkflowQueryService.findPrevNodes(processNode, head.getId());
        context.setPrevNodes(prevNodes);

        // 保存tail的下一个节点
        if (tail instanceof IRoutable routableTail) {
            context.setOriginalNext(routableTail.getNextId());
        }

        // 断开前驱节点的连接
        for (BaseNodeCanvas prevNode : prevNodes) {
            if (prevNode instanceof IRoutable routablePrev) {
                routablePrev.setNextId(null); // 暂置为null
            }
        }

        if (head instanceof IRoutable routableHead) {
            routableHead.setPrveId(null);
        }

        // 断开tail的连接
        if (tail instanceof IRoutable routableTail) {
            routableTail.setNextId(null);
        }

        log.debug("断开链段 {} -> {}, 前驱节点数: {}", head.getId(), tail.getId(), prevNodes.size());

        return context;
    }
    
    /**
     * 拼接操作：将链段拼接到指定位置
     * @param processNode 流程节点
     * @param prevNodes 前驱节点列表
     * @param head 链段头节点
     * @param tail 链段尾节点
     * @param nextNodeId 下一个节点ID
     */
    public void spliceBetween(ProcessNode processNode, List<BaseNodeCanvas> prevNodes,
                            BaseNodeCanvas head, BaseNodeCanvas tail, String nextNodeId) {
        // 使用统一的参数校验
        ValidParamsUtil.validateAutoWireParams(processNode, head, tail);

        // 连接前驱节点到head
        if (prevNodes != null) {
            for (BaseNodeCanvas prevNode : prevNodes) {
                if (prevNode instanceof IRoutable routablePrev) {
                    routablePrev.setNextId(head.getId());
                }
            }
        }

        // 设置head的前驱（如果只有一个前驱）
        if (head instanceof IRoutable routableHead && prevNodes != null && prevNodes.size() == 1) {
            routableHead.setPrveId(prevNodes.get(0).getId());
        }

        // 连接tail到下一个节点
        if (tail instanceof IRoutable routableTail) {
            // 根据设计方案，如果nextNodeId是99，应该通过ConnectToEnd处理
            if (EndOwnerManager.END_OWNER_ID.equals(nextNodeId)) {
                connectToEnd(processNode, tail);
            } else {
                routableTail.setNextId(nextNodeId);
                
                // 更新下一个节点的前驱
                if (nextNodeId != null && !nextNodeId.trim().isEmpty()) {
                    BaseNodeCanvas nextNode = processNode.getFlowNodeMap().get(nextNodeId);
                    if (nextNode instanceof IRoutable routableNext) {
                        routableNext.setPrveId(tail.getId());
                    }
                }
            }
        }

        log.debug("拼接链段 {} -> {} 到位置，前驱节点数: {}, 下一个节点: {}",
                  head.getId(), tail.getId(), prevNodes != null ? prevNodes.size() : 0,processNode.getFlowNodeMap().get(nextNodeId));
    }
    
    /**
     * 替换操作：用新链段替换旧链段
     * @param processNode 流程节点
     * @param oldHead 旧链段头节点
     * @param oldTail 旧链段尾节点
     * @param newHead 新链段头节点
     * @param newTail 新链段尾节点
     */
    public void replace(ProcessNode processNode, BaseNodeCanvas oldHead, BaseNodeCanvas oldTail,
                       BaseNodeCanvas newHead, BaseNodeCanvas newTail) {
        if (processNode == null || oldHead == null || oldTail == null || 
            newHead == null || newTail == null) {
            throw new IllegalArgumentException("参数不能为空");
        }
        
        // 断开旧链段
        DetachContext detachContext = detach(processNode, oldHead, oldTail);
        
        // 拼接新链段
        spliceBetween(processNode, detachContext.getPrevNodes(), newHead, newTail, 
                     detachContext.getOriginalNext());
        
        log.debug("替换链段 {} -> {} 为 {} -> {}", 
                oldHead.getId(), oldTail.getId(), newHead.getId(), newTail.getId());
    }
    
    /**
     * 连接到结束：将节点连接到流程结束
     * 实现设计方案中的ConnectToEnd原语，严格遵循EndOwner不变量
     * @param processNode 流程节点
     * @param tail 尾节点
     */
    public void connectToEnd(ProcessNode processNode, BaseNodeCanvas tail) {
        if (processNode == null || tail == null) {
            throw new IllegalArgumentException("参数不能为空");
        }

        try {
            BaseNodeCanvas endOwner = endOwnerManager.connectToEnd(processNode, tail);
            log.debug("成功连接节点 {} 到流程结束，EndOwner: {}", tail.getId(), endOwner.getId());
        } catch (Exception e) {
            String errorMsg = "连接到结束失败: " + e.getMessage();
            log.error(errorMsg, e);
            throw new RuntimeException(errorMsg, e);
        }
    }

    /**
     * 删除节点：使用Replace(node, ∅)原语实现节点删除
     * 根据设计方案：找到prevs(nodeId)与next=node.nextId，执行Replace(node, ∅)等价：prevs[*].nextId = next
     * @param processNode 流程节点
     * @param nodeToDelete 要删除的节点
     * @return 删除操作的上下文信息
     */
    public DeleteNodeContext deleteNode(ProcessNode processNode, BaseNodeCanvas nodeToDelete) {
        if (processNode == null || nodeToDelete == null) {
            throw new IllegalArgumentException("参数不能为空");
        }

        DeleteNodeContext context = new DeleteNodeContext();
        
        // 找到所有指向被删除节点的前驱节点
        List<BaseNodeCanvas> prevNodes = WorkflowQueryService.findPrevNodes(processNode, nodeToDelete.getId());
        context.setPrevNodes(prevNodes);
        
        String nextId = null;
        boolean isEndOwner = false;
        
        if (nodeToDelete instanceof IRoutable routableNode) {
            nextId = routableNode.getNextId();
            isEndOwner = EndOwnerManager.END_OWNER_ID.equals(nextId);
        }
        
        context.setNextId(nextId);
        context.setEndOwner(isEndOwner);

        if (isEndOwner) {
            // 删除EndOwner节点的处理 - 委托给EndOwnerManager
            BaseNodeCanvas newEndOwner = endOwnerManager.handleEndOwnerDeletion(processNode, prevNodes);
            context.setNewEndOwner(newEndOwner);
        } else {
            // 执行Replace(node, ∅)：将所有前驱直接连接到后继
            for (BaseNodeCanvas prevNode : prevNodes) {
                if (prevNode instanceof IRoutable routablePrev) {
                    routablePrev.setNextId(nextId);
                }
            }

            // 更新后继节点的前驱（选择第一个前驱作为新的前驱）
            if (nextId != null && !nextId.trim().isEmpty() && !prevNodes.isEmpty()) {
                BaseNodeCanvas nextNode = processNode.getFlowNodeMap().get(nextId);
                if (nextNode instanceof IRoutable routableNext) {
                    routableNext.setPrveId(prevNodes.get(0).getId());
                }
            }
        }

        log.debug("删除节点 {}，前驱节点数: {}, 是否为EndOwner: {}", 
                nodeToDelete.getId(), prevNodes.size(), isEndOwner);

        return context;
    }

    /**
     * 修复EndOwner：当结构扁平化导致EndOwner不再是实际末尾时
     * 实现设计方案中的AbortEndOwnerIfFlatten原语
     * @param processNode 流程节点
     */
    public BaseNodeCanvas abortEndOwnerIfFlatten(ProcessNode processNode) {
        if (processNode == null) {
            throw new IllegalArgumentException("ProcessNode不能为空");
        }

        try {
            BaseNodeCanvas endOwner = endOwnerManager.repairEndOwnerAfterFlatten(processNode);
            log.debug("EndOwner修复成功，当前EndOwner: {}", endOwner != null ? endOwner.getId() : "无");
            return endOwner;
        } catch (Exception e) {
            String errorMsg = "修复EndOwner失败: " + e.getMessage();
            log.error(errorMsg, e);
            throw new RuntimeException(errorMsg, e);
        }
    }
    
    /**
     * 断开操作的上下文信息
     */
    @Setter
    @Getter
    public static class DetachContext {
        private List<BaseNodeCanvas> prevNodes;
        private String originalNext;
    }

    /**
     * 删除节点操作的上下文信息
     */
    @Setter
    @Getter
    public static class DeleteNodeContext {
        private List<BaseNodeCanvas> prevNodes;
        private String nextId;
        private boolean isEndOwner;
        private BaseNodeCanvas newEndOwner;
    }
}
