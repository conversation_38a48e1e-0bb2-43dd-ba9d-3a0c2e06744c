package com.mlc.workflow.core.editor.model.canvas;

import com.mlc.base.common.enums.workflow.NodeTypeEnum;
import com.mlc.workflow.core.editor.model.BaseNodeCanvas;
import com.mlc.workflow.core.editor.model.visitor.canvas.ICanvasNodeVisitor;
import io.nop.api.core.annotations.data.DataBean;
import lombok.Getter;
import lombok.Setter;

/**
 * 短信任务节点
 * 对应NodeTypeEnum.MESSAGE (10)
 */
@Getter
@Setter
@DataBean
public class MessageNodeCanvas extends BaseNodeCanvas {

    public MessageNodeCanvas() {
        this.setTypeId(NodeTypeEnum.MESSAGE.getValue());
        this.setName("短信任务");
    }
    
    @Override
    public void accept(ICanvasNodeVisitor visitor) {
        visitor.visit(this);
    }
}
