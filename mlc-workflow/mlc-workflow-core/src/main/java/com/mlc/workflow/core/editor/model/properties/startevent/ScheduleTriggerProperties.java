package com.mlc.workflow.core.editor.model.properties.startevent;

import com.mlc.base.common.enums.workflow.AppTypeEnum;
import com.mlc.workflow.core.editor.model.properties.StartEventNodeProperties;
import com.mlc.workflow.core.editor.model.visitor.properties.INodeVisitor;
import java.util.ArrayList;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * 定时触发节点属性 (appType=5)
 */
@Getter
@Setter
public class ScheduleTriggerProperties extends StartEventNodeProperties {

    public ScheduleTriggerProperties() {
        super();
        this.setAppType(AppTypeEnum.LOOP.getValue());
        this.setName("定时触发");
    }

    /**
     * 分配字段ID列表
     */
    private List<String> assignFieldIds = new ArrayList<>();

    /**
     * 执行时间
     */
    private String executeTime = "";

    /**
     * 重复类型
     */
    private Integer repeatType = 1;

    /**
     * 执行结束时间
     */
    private String executeEndTime = "";

    /**
     * 频率
     */
    private Integer frequency = 1;

    /**
     * 间隔
     */
    private Integer interval = 1;

    /**
     * 星期几
     */
    private List<Integer> weekDays = new ArrayList<>();

    /**
     * 执行时间列表
     */
    private List<String> executeTimes = new ArrayList<>();

    @Override
    public void accept(INodeVisitor visitor) {
        visitor.visit(this);
    }
}
