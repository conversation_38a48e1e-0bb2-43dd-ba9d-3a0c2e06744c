package com.mlc.workflow.core.editor.model.canvas;

import com.mlc.base.common.enums.workflow.NodeTypeEnum;
import com.mlc.workflow.core.editor.model.BaseNodeCanvas;
import com.mlc.workflow.core.editor.model.visitor.canvas.ICanvasNodeVisitor;
import io.nop.api.core.annotations.data.DataBean;
import lombok.Getter;
import lombok.Setter;

/**
 * AIGC 任务节点
 * 对应NodeTypeEnum.AIGC (31)
 */
@Getter
@Setter
@DataBean
public class AigcNodeCanvas extends BaseNodeCanvas {

    public AigcNodeCanvas() {
        this.setTypeId(NodeTypeEnum.AIGC.getValue());
        this.setName("AIGC 任务");
    }

    @Override
    public void accept(ICanvasNodeVisitor visitor) {
        visitor.visit(this);
    }
}
