package com.mlc.workflow.core.editor.model.canvas.startevent;

import com.mlc.workflow.core.editor.model.canvas.StartEventNodeCanvas;
import com.mlc.workflow.core.editor.model.visitor.canvas.ICanvasNodeVisitor;
import lombok.Getter;
import lombok.Setter;

/**
 * Webhook触发节点属性 (appType=7)
 */
@Getter
@Setter
public class WebhookTriggerCanvas extends StartEventNodeCanvas {

    public WebhookTriggerCanvas() {
        super();
        this.setAppType(7);
        this.setName("Webhook触发");
    }

    @Override
    public void accept(ICanvasNodeVisitor visitor) {
        visitor.visit(this);
    }
}
