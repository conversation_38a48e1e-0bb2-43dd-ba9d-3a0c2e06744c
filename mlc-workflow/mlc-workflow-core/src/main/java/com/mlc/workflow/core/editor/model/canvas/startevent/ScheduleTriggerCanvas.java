package com.mlc.workflow.core.editor.model.canvas.startevent;

import com.mlc.workflow.core.editor.model.canvas.StartEventNodeCanvas;
import com.mlc.workflow.core.editor.model.visitor.canvas.ICanvasNodeVisitor;
import java.util.List;
import lombok.Getter;
import lombok.Setter;

/**
 * 定时触发节点属性 (appType=5)
 */
@Getter
@Setter
public class ScheduleTriggerCanvas extends StartEventNodeCanvas {

    public ScheduleTriggerCanvas() {
        super();
        this.setAppType(5);
        this.setName("定时触发");
    }

    /**
     * 执行时间
     */
    private String executeTime;

    /**
     * 重复类型
     */
    private Integer repeatType;

    /**
     * 重复频率
     */
    private Integer frequency;

    /**
     * 间隔时间
     */
    private Integer interval;

    /**
     * 重复的周期
     */
    private List<Integer> weekDays;

    @Override
    public void accept(ICanvasNodeVisitor visitor) {
        visitor.visit(this);
    }
}
