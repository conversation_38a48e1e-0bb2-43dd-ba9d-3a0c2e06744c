package com.mlc.workflow.core.editor.model.properties.startevent;

import com.mlc.base.common.enums.workflow.AppTypeEnum;
import com.mlc.workflow.core.editor.model.properties.StartEventNodeProperties;
import com.mlc.workflow.core.editor.model.visitor.properties.INodeVisitor;
import java.util.ArrayList;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * 外部用户事件触发节点属性 (appType=23)
 */
@Getter
@Setter
public class ExternalUserEventTriggerProperties extends StartEventNodeProperties {

    public ExternalUserEventTriggerProperties() {
        super();
        this.setAppType(AppTypeEnum.EXTERNAL_USER.getValue());
        this.setName("外部用户事件触发");
    }


    /**
     * 触发器ID
     */
    private String triggerId = "1";

    /**
     * 操作条件
     */
    private List<String> operateCondition = new ArrayList<>();

    /**
     * 分配字段ID列表
     */
    private List<String> assignFieldIds = new ArrayList<>();

    @Override
    public void accept(INodeVisitor visitor) {
        visitor.visit(this);
    }
}
