package com.mlc.workflow.core.editor.model.properties.startevent;

import com.mlc.base.common.enums.workflow.AppTypeEnum;
import com.mlc.workflow.core.editor.model.properties.StartEventNodeProperties;
import com.mlc.workflow.core.editor.model.visitor.properties.INodeVisitor;
import java.util.ArrayList;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * 按日期字段触发节点属性 (appType=6)
 */
@Getter
@Setter
public class DateFieldTriggerProperties extends StartEventNodeProperties {

    public DateFieldTriggerProperties() {
        super();
        this.setAppType(AppTypeEnum.DATE.getValue());
        this.setName("按日期字段触发");
    }

    /**
     * 操作条件
     */
    private List<String> operateCondition = new ArrayList<>();

    /**
     * 分配字段ID
     */
    private String assignFieldId = "";

    /**
     * 分配字段ID列表
     */
    private List<String> assignFieldIds = new ArrayList<>();

    /**
     * 触发器ID
     */
    private String triggerId = "2";

    /**
     * 执行时间类型
     */
    private Integer executeTimeType = 0;

    /**
     * 数字
     */
    private Integer number = 0;

    /**
     * 单位
     */
    private Integer unit = 3;

    /**
     * 频率
     */
    private Integer frequency = 0;

    /**
     * 时间
     */
    private String time = "08:00";

    /**
     * 结束时间
     */
    private String endTime = "08:00";

    @Override
    public void accept(INodeVisitor visitor) {
        visitor.visit(this);
    }
}
