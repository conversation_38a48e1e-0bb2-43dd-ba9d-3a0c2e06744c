package com.mlc.workflow.core.editor.model.canvas.capability;

import com.mlc.workflow.core.editor.runtime.beans.ProcessNode;

/**
 * 具有子流程能力接口
 */
public interface IHasSubProcess {
    
    /**
     * 获取子流程节点
     * @return 子流程节点
     */
    ProcessNode getProcessNode();
    
    /**
     * 设置子流程节点
     * @param processNode 子流程节点
     */
    void setProcessNode(ProcessNode processNode);
    
    /**
     * 验证子流程是否有效
     * @return 是否有效
     */
    default boolean hasValidSubProcess() {
        ProcessNode processNode = getProcessNode();
        return processNode != null && 
               processNode.getId() != null && 
               processNode.getStartEventId() != null &&
               processNode.getFlowNodeMap() != null && 
               !processNode.getFlowNodeMap().isEmpty();
    }
}
