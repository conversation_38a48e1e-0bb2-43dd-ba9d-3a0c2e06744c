package com.mlc.workflow.core.editor.model.canvas;

import com.mlc.base.common.enums.workflow.NodeTypeEnum;
import com.mlc.workflow.core.editor.model.BaseNodeCanvas;
import com.mlc.workflow.core.editor.model.visitor.canvas.ICanvasNodeVisitor;
import io.nop.api.core.annotations.data.DataBean;
import lombok.Getter;
import lombok.Setter;

/**
 * 延时任务节点
 * 对应NodeTypeEnum.DELAY (12)
 */
@Getter
@Setter
@DataBean
public class DelayNodeCanvas extends BaseNodeCanvas {

    public DelayNodeCanvas() {
        this.setTypeId(NodeTypeEnum.DELAY.getValue());
        this.setName("延时任务");
    }

    @Override
    public void accept(ICanvasNodeVisitor visitor) {
        visitor.visit(this);
    }
}
