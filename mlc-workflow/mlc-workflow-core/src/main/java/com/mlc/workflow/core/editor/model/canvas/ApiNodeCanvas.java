package com.mlc.workflow.core.editor.model.canvas;

import com.mlc.base.common.enums.workflow.NodeTypeEnum;
import com.mlc.workflow.core.editor.model.BaseNodeCanvas;
import com.mlc.workflow.core.editor.model.visitor.canvas.ICanvasNodeVisitor;
import io.nop.api.core.annotations.data.DataBean;
import lombok.Getter;
import lombok.Setter;

/**
 * 调用已集成 API 任务节点
 * 对应NodeTypeEnum.API (25)
 */
@Getter
@Setter
@DataBean
public class ApiNodeCanvas extends BaseNodeCanvas {

    public ApiNodeCanvas() {
        this.setTypeId(NodeTypeEnum.API.getValue());
        this.setName("调用已集成 API 任务");
    }

    @Override
    public void accept(ICanvasNodeVisitor visitor) {
        visitor.visit(this);
    }
}
