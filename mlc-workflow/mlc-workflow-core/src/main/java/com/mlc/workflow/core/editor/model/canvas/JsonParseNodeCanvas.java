package com.mlc.workflow.core.editor.model.canvas;

import com.mlc.base.common.enums.workflow.NodeTypeEnum;
import com.mlc.workflow.core.editor.model.BaseNodeCanvas;
import com.mlc.workflow.core.editor.model.visitor.canvas.ICanvasNodeVisitor;
import io.nop.api.core.annotations.data.DataBean;
import lombok.Getter;
import lombok.Setter;

/**
 * JSON 解析任务节点
 * 对应NodeTypeEnum.JSON_PARSE (21)
 */
@Getter
@Setter
@DataBean
public class JsonParseNodeCanvas extends BaseNodeCanvas {

    public JsonParseNodeCanvas() {
        this.setTypeId(NodeTypeEnum.JSON_PARSE.getValue());
        this.setName("JSON 解析任务");
    }

    @Override
    public void accept(ICanvasNodeVisitor visitor) {
        visitor.visit(this);
    }
}
