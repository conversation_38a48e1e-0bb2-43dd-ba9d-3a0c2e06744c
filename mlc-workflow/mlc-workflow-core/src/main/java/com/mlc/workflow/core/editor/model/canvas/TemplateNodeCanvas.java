package com.mlc.workflow.core.editor.model.canvas;

import com.mlc.base.common.enums.workflow.NodeTypeEnum;
import com.mlc.workflow.core.editor.model.BaseNodeCanvas;
import com.mlc.workflow.core.editor.model.visitor.canvas.ICanvasNodeVisitor;
import io.nop.api.core.annotations.data.DataBean;
import lombok.Getter;
import lombok.Setter;

/**
 * 服务号消息任务节点
 * 对应NodeTypeEnum.TEMPLATE (19)
 */
@Getter
@Setter
@DataBean
public class TemplateNodeCanvas extends BaseNodeCanvas {

    public TemplateNodeCanvas() {
        this.setTypeId(NodeTypeEnum.TEMPLATE.getValue());
        this.setName("服务号消息任务");
    }

    @Override
    public void accept(ICanvasNodeVisitor visitor) {
        visitor.visit(this);
    }
}
