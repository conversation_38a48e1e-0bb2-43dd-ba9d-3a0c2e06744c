package com.mlc.workflow.core.editor.model.visitor.properties.example;

import com.mlc.workflow.core.editor.model.BaseNodeProperties;
import com.mlc.workflow.core.editor.model.properties.*;
import com.mlc.workflow.core.editor.model.properties.startevent.SheetEventTriggerProperties;
import com.mlc.workflow.core.editor.model.visitor.properties.AbstractNodeVisitor;
import com.mlc.workflow.core.editor.model.visitor.properties.DataAssemblyVisitor;

import java.util.*;
import lombok.Getter;

/**
 * 多节点遍历访问者模式使用示例
 * 
 * 本示例展示了访问者模式的强大之处：
 * 1. 批量处理多个不同类型的节点
 * 2. 递归遍历复杂的节点树结构
 * 3. 使用不同的访问者实现不同的功能
 * 4. 展示访问者模式在实际工作流场景中的应用
 * 
 * 核心价值：
 * - 一次遍历，多种处理：同一个节点树可以被不同的访问者处理，实现不同的功能
 * - 数据与算法分离：节点结构保持稳定，算法逻辑封装在访问者中
 * - 易于扩展：添加新的访问者无需修改现有的节点类
 */
public class MultiNodeTraversalExample {
    
    public static void main(String[] args) {
        System.out.println("=== 多节点遍历访问者模式使用示例 ===\n");
        
        // 示例1: 构建一个完整的工作流节点树
        List<BaseNodeProperties> workflowNodes = buildSampleWorkflow();
        
        // 示例2: 使用数据收集访问者处理整个工作流
        demonstrateDataAssemblyVisitor(workflowNodes);
        
        // 示例3: 使用验证访问者检查工作流
        demonstrateValidationVisitor(workflowNodes);

        // 示例5: 演示如何递归遍历复杂的节点树
        demonstrateRecursiveTraversal();
        
        System.out.println("\n=== 多节点遍历示例完成 ===");
    }
    
    /**
     * 构建一个示例工作流，包含多种不同类型的节点
     */
    private static List<BaseNodeProperties> buildSampleWorkflow() {
        System.out.println("🏗️ 构建示例工作流");
        System.out.println("------------------------");
        
        List<BaseNodeProperties> nodes = new ArrayList<>();
        
        // 1. 起始节点
        StartEventNodeProperties startNode = new SheetEventTriggerProperties();
        startNode.setName("请假申请启动");
        startNode.setDesc("员工发起请假申请");
        startNode.setAppType(1);
        nodes.add(startNode);
        
        // 2. 填写节点
        WriteNodeProperties writeNode = new WriteNodeProperties();
        writeNode.setName("填写请假信息");
        writeNode.setDesc("员工填写请假详细信息");
        writeNode.setCondition("role == 'employee'");
        writeNode.setOperationTypeList(Arrays.asList("create", "update"));
        Map<String, Object> formProps = new HashMap<>();
        formProps.put("fields", Arrays.asList("startDate", "endDate", "reason", "type"));
        writeNode.setFormProperties(formProps);
        nodes.add(writeNode);
        
        // 3. 数据查询节点
        SearchNodeProperties searchNode = new SearchNodeProperties();
        searchNode.setName("查询员工信息");
        searchNode.setDesc("查询申请人的基本信息和历史记录");
        nodes.add(searchNode);
        
        // 4. 直属经理审批
        ApprovalNodeProperties managerApproval = new ApprovalNodeProperties();
        managerApproval.setName("直属经理审批");
        managerApproval.setDesc("直属经理审批请假申请");
        managerApproval.setAccounts(Arrays.asList("manager001"));
        managerApproval.setMultipleLevelType(1);
        managerApproval.setCountersignType(0);
        managerApproval.setCondition("days <= 3");
        nodes.add(managerApproval);
        
        // 5. HR审批（长期请假）
        ApprovalNodeProperties hrApproval = new ApprovalNodeProperties();
        hrApproval.setName("HR部门审批");
        hrApproval.setDesc("人力资源部门审批长期请假");
        hrApproval.setAccounts(Arrays.asList("hr001", "hr002"));
        hrApproval.setMultipleLevelType(2);
        hrApproval.setCountersignType(1);
        hrApproval.setCondition("days > 3");
        nodes.add(hrApproval);
        
        // 6. 第二个搜索节点（更新状态）
        SearchNodeProperties updateSearchNode = new SearchNodeProperties();
        updateSearchNode.setName("更新员工状态");
        updateSearchNode.setDesc("更新员工的请假状态");
        nodes.add(updateSearchNode);
        
        System.out.println("✓ 已构建包含 " + nodes.size() + " 个节点的示例工作流");
        nodes.forEach(node -> System.out.println("  - " + node.getName() + " (" + node.getClass().getSimpleName() + ")"));
        System.out.println();
        
        return nodes;
    }
    
    /**
     * 示例2: 使用数据收集访问者处理整个工作流
     */
    private static void demonstrateDataAssemblyVisitor(List<BaseNodeProperties> nodes) {
        System.out.println("📊 示例2: 数据收集访问者批量处理");
        System.out.println("----------------------------------------");
        
        // 创建数据收集访问者
        DataAssemblyVisitor dataVisitor = new DataAssemblyVisitor();
        
        // 批量处理所有节点
        System.out.println("开始批量处理节点...");
        for (BaseNodeProperties node : nodes) {
            node.accept(dataVisitor);
        }
        
        // 展示收集结果
        System.out.println("\n收集结果摘要:");
        dataVisitor.printSummary();
        
        // 展示详细统计
        Map<String, Object> summary = dataVisitor.getResultSummary();
        System.out.println("\n详细统计信息:");
        System.out.println("  总处理节点: " + summary.get("totalNodesProcessed"));
        System.out.println("  审批节点数: " + summary.get("approvalNodesCount"));
        System.out.println("  搜索节点数: " + summary.get("searchNodesCount"));
        System.out.println("  填写节点数: " + summary.get("writeNodesCount"));
        System.out.println("  包含起始节点: " + summary.get("hasStartNode"));
        
        System.out.println();
    }
    
    /**
     * 示例3: 使用验证访问者检查工作流
     */
    private static void demonstrateValidationVisitor(List<BaseNodeProperties> nodes) {
        System.out.println("✅ 示例3: 工作流验证访问者");
        System.out.println("--------------------------------");
        
        // 创建验证访问者
        WorkflowValidationVisitor validator = new WorkflowValidationVisitor();
        
        // 验证所有节点
        System.out.println("开始验证工作流节点...");
        for (BaseNodeProperties node : nodes) {
            node.accept(validator);
        }
        
        // 展示验证结果
        validator.printValidationResults();
        System.out.println();
    }
    

    /**
     * 示例5: 演示递归遍历复杂的节点树结构
     */
    private static void demonstrateRecursiveTraversal() {
        System.out.println("🌳 示例5: 递归遍历节点树");
        System.out.println("---------------------------");
        
        // 构建一个模拟的树状结构（在实际应用中，这可能是一个真正的树形工作流）
        WorkflowTreeNode root = buildWorkflowTree();
        
        // 创建树遍历访问者
        TreeTraversalVisitor treeVisitor = new TreeTraversalVisitor();
        
        // 递归遍历整个树
        System.out.println("开始递归遍历工作流树...");
        traverseWorkflowTree(root, treeVisitor, 0);
        
        // 展示遍历结果
        treeVisitor.printTraversalResults();
        System.out.println();
    }
    
    /**
     * 构建一个示例的工作流树结构
     */
    private static WorkflowTreeNode buildWorkflowTree() {
        // 根节点
        StartEventNodeProperties rootNode = new SheetEventTriggerProperties();
        rootNode.setName("工作流根节点");
        WorkflowTreeNode root = new WorkflowTreeNode(rootNode);
        
        // 第一层子节点
        WriteNodeProperties writeChild = new WriteNodeProperties();
        writeChild.setName("信息填写");
        WorkflowTreeNode writeNode = new WorkflowTreeNode(writeChild);
        root.addChild(writeNode);
        
        ApprovalNodeProperties approvalChild = new ApprovalNodeProperties();
        approvalChild.setName("审批节点");
        WorkflowTreeNode approvalNode = new WorkflowTreeNode(approvalChild);
        root.addChild(approvalNode);
        
        // 第二层子节点
        SearchNodeProperties searchChild = new SearchNodeProperties();
        searchChild.setName("数据查询");
        WorkflowTreeNode searchNode = new WorkflowTreeNode(searchChild);
        approvalNode.addChild(searchNode);
        
        return root;
    }
    
    /**
     * 递归遍历工作流树
     */
    private static void traverseWorkflowTree(WorkflowTreeNode node, TreeTraversalVisitor visitor, int depth) {
        // 缩进显示层级
        String indent = "  ".repeat(depth);
        System.out.println(indent + "访问节点: " + node.getNodeData().getName());
        
        // 让当前节点接受访问者
        node.getNodeData().accept(visitor);
        
        // 递归访问所有子节点
        for (WorkflowTreeNode child : node.getChildren()) {
            traverseWorkflowTree(child, visitor, depth + 1);
        }
    }
    
    /**
     * 工作流验证访问者
     * 专门用于验证工作流节点的配置是否正确
     */
    private static class WorkflowValidationVisitor extends AbstractNodeVisitor {
        private final List<String> validationErrors = new ArrayList<>();
        private final List<String> validationWarnings = new ArrayList<>();
        private int validNodesCount = 0;
        
        @Override
        public void visit(StartEventNodeProperties node) {
            if (node.getName() == null || node.getName().trim().isEmpty()) {
                validationErrors.add("起始节点缺少名称");
            }
            if (node.getAppType() == null) {
                validationWarnings.add("起始节点未设置应用类型");
            }
            validNodesCount++;
        }
        
        @Override
        public void visit(ApprovalNodeProperties node) {
            if (node.getAccounts() == null || node.getAccounts().isEmpty()) {
                validationErrors.add("审批节点 '" + node.getName() + "' 缺少审批人");
            }
            if (node.getCondition() == null || node.getCondition().trim().isEmpty()) {
                validationWarnings.add("审批节点 '" + node.getName() + "' 未设置审批条件");
            }
            validNodesCount++;
        }
        
        @Override
        public void visit(WriteNodeProperties node) {
            if (node.getFormProperties() == null || node.getFormProperties().isEmpty()) {
                validationWarnings.add("填写节点 '" + node.getName() + "' 未配置表单属性");
            }
            validNodesCount++;
        }
        
        @Override
        public void visit(SearchNodeProperties node) {
            // 搜索节点的基本验证
            validNodesCount++;
        }
        
        public void printValidationResults() {
            System.out.println("验证结果:");
            System.out.println("  验证节点总数: " + validNodesCount);
            System.out.println("  错误数量: " + validationErrors.size());
            System.out.println("  警告数量: " + validationWarnings.size());
            
            if (!validationErrors.isEmpty()) {
                System.out.println("\n错误列表:");
                validationErrors.forEach(error -> System.out.println("  ❌ " + error));
            }
            
            if (!validationWarnings.isEmpty()) {
                System.out.println("\n警告列表:");
                validationWarnings.forEach(warning -> System.out.println("  ⚠️ " + warning));
            }
            
            if (validationErrors.isEmpty()) {
                System.out.println("✅ 工作流验证通过!");
            }
        }
    }
    
    /**
     * 树遍历访问者
     * 专门用于记录树遍历的过程和结果
     */
    private static class TreeTraversalVisitor extends AbstractNodeVisitor {
        private final List<String> traversalPath = new ArrayList<>();
        
        @Override
        public void visit(StartEventNodeProperties node) {
            traversalPath.add("起始:" + node.getName());
        }
        
        @Override
        public void visit(ApprovalNodeProperties node) {
            traversalPath.add("审批:" + node.getName());
        }
        
        @Override
        public void visit(WriteNodeProperties node) {
            traversalPath.add("填写:" + node.getName());
        }
        
        @Override
        public void visit(SearchNodeProperties node) {
            traversalPath.add("搜索:" + node.getName());
        }
        
        public void printTraversalResults() {
            System.out.println("遍历路径记录:");
            for (int i = 0; i < traversalPath.size(); i++) {
                System.out.println("  " + (i + 1) + ". " + traversalPath.get(i));
            }
            System.out.println("总共遍历了 " + traversalPath.size() + " 个节点");
        }
    }
    
    /**
     * 工作流树节点
     * 用于模拟树状的工作流结构
     */
    @Getter
    private static class WorkflowTreeNode {
        private final BaseNodeProperties nodeData;
        private final List<WorkflowTreeNode> children = new ArrayList<>();
        
        public WorkflowTreeNode(BaseNodeProperties nodeData) {
            this.nodeData = nodeData;
        }
        
        public void addChild(WorkflowTreeNode child) {
            children.add(child);
        }

    }
}
