package com.mlc.workflow.core.editor.model.canvas.capability;

import java.util.List;

/**
 * 具有分支能力接口
 */
public interface IHasBranches {
    
    /**
     * 获取分支流ID列表
     * @return 分支流ID列表
     */
    List<String> getFlowIds();
    
    /**
     * 设置分支流ID列表
     * @param flowIds 分支流ID列表
     */
    void setFlowIds(List<String> flowIds);
    
    /**
     * 添加分支流ID
     * @param flowId 分支流ID
     */
    default void addFlowId(String flowId) {
        if (flowId != null && !flowId.trim().isEmpty()) {
            getFlowIds().add(flowId);
        }
    }
    
    /**
     * 验证分支是否有效
     * @return 是否有效
     */
    default boolean hasValidBranches() {
        List<String> flowIds = getFlowIds();
        return flowIds != null && !flowIds.isEmpty() && 
               flowIds.stream().allMatch(id -> id != null && !id.trim().isEmpty());
    }
}
