package com.mlc.workflow.core.editor.model.utils;

import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.base.Strings;
import com.mlc.workflow.core.editor.model.BaseNodeCanvas;
import com.mlc.workflow.core.editor.runtime.beans.ProcessNode;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 流程节点解析器
 * 负责将JSON数据解析为具体的节点对象
 */
@Slf4j
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class ProcessParserUtil {

    public static final ProcessParserUtil INSTANCE = new ProcessParserUtil();

    private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper();
    
    /**
     * 解析流程节点
     * @param jsonString JSON字符串
     * @return 流程节点
     */
    public ProcessNode parseProcessNode(String jsonString) {
        if (Strings.isNullOrEmpty(jsonString)) {
            throw new IllegalArgumentException("jsonString不能为空");
        }
        try {
            return OBJECT_MAPPER.readValue(jsonString, ProcessNode.class);
        } catch (Exception e) {
            log.error("解析流程节点失败", e);
            throw new RuntimeException("解析流程节点失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 解析单个节点
     * @param nodeJson 节点JSON
     * @return 基础节点
     */
    public BaseNodeCanvas parseNode(JsonNode nodeJson) {
        try {
            return OBJECT_MAPPER.treeToValue(nodeJson, BaseNodeCanvas.class);
        } catch (Exception e) {
            log.error("解析单个节点失败", e);
            throw new RuntimeException("解析节点失败: " + e.getMessage(), e);
        }
    }

    /**
     * 将流程节点转换为JSON字符串
     * @param processNode 流程节点
     * @return JSON字符串
     */
    public String toJsonString(ProcessNode processNode) {
        if (processNode == null) {
            throw new IllegalArgumentException("processNode不能为空");
        }

        OBJECT_MAPPER.setSerializationInclusion(Include.NON_NULL);
        try {
            return OBJECT_MAPPER.writeValueAsString(processNode);
        } catch (Exception e) {
            log.error("将流程节点转换为JSON失败", e);
            throw new RuntimeException("将流程节点转换为JSON失败: " + e.getMessage(), e);
        }
    }
}
