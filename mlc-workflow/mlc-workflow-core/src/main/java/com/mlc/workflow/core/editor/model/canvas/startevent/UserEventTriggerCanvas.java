package com.mlc.workflow.core.editor.model.canvas.startevent;

import com.mlc.workflow.core.editor.model.canvas.StartEventNodeCanvas;
import com.mlc.workflow.core.editor.model.visitor.canvas.ICanvasNodeVisitor;
import lombok.Getter;
import lombok.Setter;

/**
 * 人员事件触发节点属性 (appType=20)
 */
@Getter
@Setter
public class UserEventTriggerCanvas extends StartEventNodeCanvas {

    public UserEventTriggerCanvas() {
        super();
        this.setAppType(20);
        this.setName("人员事件触发");
    }

    /**
     * 应用类型
     */
    private String appTypeName;

    /**
     * 触发ID
     */
    private String triggerId;

    @Override
    public void accept(ICanvasNodeVisitor visitor) {
        visitor.visit(this);
    }
}
