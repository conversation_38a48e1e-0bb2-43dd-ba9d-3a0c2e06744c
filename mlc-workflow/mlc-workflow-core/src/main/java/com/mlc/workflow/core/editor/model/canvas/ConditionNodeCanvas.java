package com.mlc.workflow.core.editor.model.canvas;

import com.mlc.base.common.enums.workflow.NodeTypeEnum;
import com.mlc.workflow.core.editor.runtime.beans.ConditionGroup;
import com.mlc.workflow.core.editor.model.visitor.canvas.ICanvasNodeVisitor;
import io.nop.api.core.annotations.data.DataBean;
import lombok.EqualsAndHashCode;

import java.util.List;
import lombok.Getter;
import lombok.Setter;

/**
 * 条件节点 (typeId=2)
 * 用于流程的条件判断，通常作为分支项
 */
@Getter
@Setter
@EqualsAndHashCode(callSuper = true)
@DataBean
public class ConditionNodeCanvas extends FlowNodeCanvas{
    
    /**
     * 操作条件列表
     * 二维数组，外层是OR关系，内层是AND关系
     */
    private List<List<ConditionGroup>> operateCondition;

    public ConditionNodeCanvas() {
        this.setName("条件节点");
        this.setTypeId(NodeTypeEnum.BRANCH_ITEM.getValue());
    }

    /**
     * 验证条件是否有效
     * @return 是否有效
     */
     public boolean hasValidConditions() {
        List<List<ConditionGroup>> conditions = getOperateCondition();
        return conditions != null && !conditions.isEmpty() &&
            conditions.stream().allMatch(conditionGroup ->
                                             conditionGroup != null && !conditionGroup.isEmpty() &&
                                                 conditionGroup.stream().allMatch(condition ->
                                                    condition != null && condition.getNodeId() != null &&
                                                          condition.getFiledId() != null));
    }

    /**
     * 验证条件节点
     * @return 是否有效
     */
    @Override
    public boolean isValid() {
        return super.isValid() && hasValidConditions();
    }

    @Override
    public void accept(ICanvasNodeVisitor visitor) {
        visitor.visit(this);
    }
}
