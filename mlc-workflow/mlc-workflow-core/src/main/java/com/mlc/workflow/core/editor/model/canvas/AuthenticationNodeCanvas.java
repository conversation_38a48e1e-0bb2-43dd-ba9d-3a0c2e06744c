package com.mlc.workflow.core.editor.model.canvas;

import com.mlc.base.common.enums.workflow.NodeTypeEnum;
import com.mlc.workflow.core.editor.model.BaseNodeCanvas;
import com.mlc.workflow.core.editor.model.visitor.canvas.ICanvasNodeVisitor;
import io.nop.api.core.annotations.data.DataBean;
import lombok.Getter;
import lombok.Setter;

/**
 * API 连接与认证任务节点
 * 对应NodeTypeEnum.AUTHENTICATION (22)
 */
@Getter
@Setter
@DataBean
public class AuthenticationNodeCanvas extends BaseNodeCanvas {

    public AuthenticationNodeCanvas() {
        this.setTypeId(NodeTypeEnum.AUTHENTICATION.getValue());
        this.setName("API 连接与认证任务");
    }

    @Override
    public void accept(ICanvasNodeVisitor visitor) {
        visitor.visit(this);
    }
}
