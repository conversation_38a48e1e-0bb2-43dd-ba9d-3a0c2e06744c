package com.mlc.workflow.core.editor.structure.service;

import com.mlc.workflow.core.editor.model.BaseNodeCanvas;
import com.mlc.workflow.core.editor.runtime.beans.ProcessNode;

/**
 * 节点链路访问的回调接口
 */
@FunctionalInterface
public interface INodeCallback {
    
    /**
     * 访问节点的回调方法
     * @param node 节点实例
     * @param context 遍历上下文
     * @param <T> 节点类型
     */
    <T extends BaseNodeCanvas> void visit(T node, TraverseContext context);
    
    /**
     * 开始访问流程
     * @param processNode 流程节点
     * @param context 遍历上下文
     */
    default void startVisit(ProcessNode processNode, TraverseContext context) {
    }
    
    /**
     * 结束访问流程
     * @param processNode 流程节点
     * @param context 遍历上下文
     */
    default void endVisit(ProcessNode processNode, TraverseContext context) {
    }
}
