package com.mlc.workflow.core.editor.model.canvas;

import com.mlc.base.common.enums.workflow.NodeTypeEnum;
import com.mlc.workflow.core.editor.model.BaseNodeCanvas;
import com.mlc.workflow.core.editor.model.visitor.canvas.ICanvasNodeVisitor;
import io.nop.api.core.annotations.data.DataBean;
import lombok.Getter;
import lombok.Setter;

/**
 * 插件任务节点
 * 对应NodeTypeEnum.PLUGIN (32)
 */
@Getter
@Setter
@DataBean
public class PluginNodeCanvas extends BaseNodeCanvas {

    public PluginNodeCanvas() {
        this.setTypeId(NodeTypeEnum.PLUGIN.getValue());
        this.setName("插件任务");
    }

    @Override
    public void accept(ICanvasNodeVisitor visitor) {
        visitor.visit(this);
    }
}
