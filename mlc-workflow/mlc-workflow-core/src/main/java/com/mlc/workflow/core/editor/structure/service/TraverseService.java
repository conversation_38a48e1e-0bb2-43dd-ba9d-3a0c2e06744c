package com.mlc.workflow.core.editor.structure.service;

import com.mlc.workflow.core.editor.model.BaseNodeCanvas;
import com.mlc.workflow.core.editor.runtime.beans.ProcessNode;
import com.mlc.workflow.core.editor.model.canvas.capability.IHasBranches;
import com.mlc.workflow.core.editor.model.canvas.capability.IHasSubProcess;
import com.mlc.workflow.core.editor.model.canvas.capability.IRoutable;
import com.mlc.workflow.core.editor.structure.manager.EndOwnerManager;
import java.util.List;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 遍历服务工具类
 * 支持网关分支展开和子流程递归
 */
@Slf4j
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class TraverseService {

    /**
     * 遍历流程节点
     * @param processNode 流程节点
     * @param visitor 访问者
     */
    public static void visit(ProcessNode processNode, INodeCallback visitor) {
        if (processNode == null || visitor == null) {
            return;
        }
        
        TraverseContext context = new TraverseContext(processNode);
        try {
            visitor.startVisit(processNode, context);
            traverseFromStart(processNode, visitor, context);
        } catch (Exception e) {
            log.error("遍历流程时发生错误: {}", e.getMessage(), e);
            throw new RuntimeException("流程遍历失败", e);
        } finally {
            visitor.endVisit(processNode, context);
        }
    }
    
    /**
     * 从开始事件开始遍历
     * @param processNode 流程节点
     * @param visitor 访问者
     * @param context 遍历上下文
     */
    private static void traverseFromStart(ProcessNode processNode, INodeCallback visitor, TraverseContext context) {
        String startEventId = processNode.getStartEventId();
        if (startEventId == null || startEventId.trim().isEmpty()) {
            log.warn("流程 {} 没有开始事件ID", processNode.getId());
            return;
        }
        
        BaseNodeCanvas startNode = processNode.getFlowNodeMap().get(startEventId);
        if (startNode == null) {
            log.warn("找不到开始事件节点: {}", startEventId);
            return;
        }
        
        traverseNode(startNode, visitor, context);
    }
    
    /**
     * 遍历单个节点
     * @param node 节点
     * @param nodeCallback 访问者
     * @param context 遍历上下文
     */
    private static void traverseNode(BaseNodeCanvas node, INodeCallback nodeCallback, TraverseContext context) {
        if (node == null) {
            return;
        }
        
        String nodeId = node.getId();
        
        // 检查是否已访问（防止环路）
        if (context.isVisited(nodeId)) {
            log.warn("检测到环路，节点 {} 已被访问，当前路径: {}", nodeId, context.getCurrentPathString());
            return;
        }
        
        // 检查最大深度
        if (context.isMaxDepthExceeded()) {
            log.warn("超过最大遍历深度 {}，停止遍历", context.getMaxDepth());
            return;
        }
        
        // 标记为已访问
        context.markVisited(nodeId);
        
        try {
            // 访问当前节点
            nodeCallback.visit(node, context);
            
            // 继续遍历后续节点
            traverseNext(node, nodeCallback, context);
            
        } finally {
            // 回溯时取消访问标记
            context.unmarkVisited(nodeId);
        }
    }

    /**
     * 遍历后续节点
     * @param node 当前节点
     * @param visitor 访问者
     * @param context 遍历上下文
     */
    private static void traverseNext(BaseNodeCanvas node, INodeCallback visitor, TraverseContext context) {
        // 处理网关节点的分支
        if (node instanceof IHasBranches branchNode) {
            traverseBranches(branchNode, visitor, context);
            return;
        }
        
        // 处理子流程节点
        if (node instanceof IHasSubProcess subProcessNode) {
            traverseSubProcess(subProcessNode, visitor, context);
        }
        
        // 处理普通的下一个节点
        if (node instanceof IRoutable routableNode) {
            traverseNextNode(routableNode, visitor, context);
        }
    }
    
    /**
     * 遍历网关分支
     * @param branchNode 分支节点
     * @param visitor 访问者
     * @param context 遍历上下文
     */
    private static void traverseBranches(IHasBranches branchNode, INodeCallback visitor, TraverseContext context) {
        List<String> flowIds = branchNode.getFlowIds();
        if (flowIds == null || flowIds.isEmpty()) {
            return;
        }
        
        ProcessNode processNode = context.getCurrentProcessNode();
        for (String flowId : flowIds) {
            BaseNodeCanvas branchLeaf = processNode.getFlowNodeMap().get(flowId);
            if (branchLeaf != null) {
                traverseNode(branchLeaf, visitor, context);
            }
        }
        
        // 遍历网关的合流后节点
        if (branchNode instanceof IRoutable routableNode) {
            String nextId = routableNode.getNextId();
            if (nextId != null && !nextId.trim().isEmpty() && !EndOwnerManager.END_OWNER_ID.equals(nextId)) {
                BaseNodeCanvas nextNode = processNode.getFlowNodeMap().get(nextId);
                if (nextNode != null) {
                    traverseNode(nextNode, visitor, context);
                }
            }
        }
    }
    
    /**
     * 遍历子流程
     * @param subProcessNode 子流程节点
     * @param visitor 访问者
     * @param context 遍历上下文
     */
    private static void traverseSubProcess(IHasSubProcess subProcessNode, INodeCallback visitor, TraverseContext context) {
        ProcessNode subProcess = subProcessNode.getProcessNode();
        if (subProcess != null) {
            context.enterSubProcess(subProcess);
            try {
                traverseFromStart(subProcess, visitor, context);
            } finally {
                context.exitSubProcess();
            }
        }
    }
    
    /**
     * 遍历下一个节点
     * @param routableNode 可路由节点
     * @param visitor 访问者
     * @param context 遍历上下文
     */
    private static void traverseNextNode(IRoutable routableNode, INodeCallback visitor, TraverseContext context) {
        String nextId = routableNode.getNextId();
        if (nextId == null || nextId.trim().isEmpty() || EndOwnerManager.END_OWNER_ID.equals(nextId)) {
            // 到达流程结束
            return;
        }
        
        ProcessNode processNode = context.getCurrentProcessNode();
        BaseNodeCanvas nextNode = processNode.getFlowNodeMap().get(nextId);
        if (nextNode != null) {
            traverseNode(nextNode, visitor, context);
        } else {
            log.warn("找不到下一个节点: {}", nextId);
        }
    }
}
