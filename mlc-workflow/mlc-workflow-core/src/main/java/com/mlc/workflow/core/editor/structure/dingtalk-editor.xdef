<?xml version="1.0" encoding="UTF-8"?>
<!--
  工作流设计器元模型 (V2)
  该模型用于抽象定义类钉钉审批流的设计器核心结构。
  V2 版本采纳了更高级的抽象，将所有元素统一为 <node>，并通过 type 属性区分，
  同时显式化了节点间的连接关系。
-->
<design-model displayName="string"
              startNode="!string"
              endOwnerNode="string"
              x:schema="/nop/schema/xdef.xdef" xmlns:x="/nop/schema/xdsl.xdef"
              xmlns:xdef="/nop/schema/xdef.xdef"
              xdef:name="WorkflowDesignModel"
              xdef:bean-package="com.mlc.workflow.core.editor.modeld"
>
    <description xdef:value="string"/>

    <!--
      定义了设计器画布上的所有节点。
      节点之间的连接关系通过 <outputs> 元素显式定义，构成一个完整的图。
    -->
    <nodes xdef:body-type="list" xdef:key-attr="name" xdef:bean-child-name="node">

        <!--
          统一的节点模型。所有流程元素，无论是开始、审批还是网关，都是一个节点。
          通过 type 属性来区分其具体职责和功能。
        -->
        <node name="!string" displayName="string"
              type="!enum:START,END,APPROVER,NOTIFIER,GATEWAY,SUBPROCESS"
              xdef:name="NodeModel">

            <description xdef:value="string"/>

            <!--
              节点的输出连接定义。一个节点可以有多个输出，连接到不同的后续节点。
              这使得流程图的描述非常灵活，可以轻松表达分支和并行。
            -->
            <outputs xdef:body-type="list" xdef:key-attr="to" xdef:bean-child-name="output">
                <output to="!string" xdef:name="NodeOutputModel">
                    <!--
                      连接线上的条件表达式。仅在源节点是条件网关 (GATEWAY/EXCLUSIVE) 时有意义。
                    -->
                    <when xdef:value="xpl-predicate"/>
                </output>
            </outputs>

            <!--
              使用 xdef:switch 根据节点类型来定义其特有的属性。
              这实现了您所期望的“以另一种抽象的形式表达”普通节点。
            -->
            <xdef:switch on="@type">

                <xdef:case value="APPROVER">
                    <!--
                      审批节点的特有属性
                      @strategy 审批策略 (ALL:会签/全员通过, ANY:或签/任一通过)
                      @endOwnerCandidate 是否为“最终审批人”的候选节点。
                    -->
                    <approver-config strategy="!enum:ALL,ANY=ALL" endOwnerCandidate="!boolean=false"
                                     xdef:name="ApproverConfigModel"/>

                    <!-- 审批参与者 -->
                    <participants role="!enum:APPROVER=APPROVER" xdef:ref="ParticipantsModel"/>
                </xdef:case>

                <xdef:case value="NOTIFIER">
                    <!-- 抄送参与者 -->
                    <participants role="!enum:NOTIFIER=NOTIFIER" xdef:ref="ParticipantsModel"/>
                </xdef:case>

                <xdef:case value="GATEWAY">
                    <!--
                      网关节点的特有属性
                      @gatewayType 网关类型 (EXCLUSIVE:唯一分支, PARALLEL:并行分支, INCLUSIVE:包容性分支)
                      @joinType 合流语义 (AND: 等待所有上游分支完成, OR: 任一上游分支到达即可)。
                                 当一个网关被多个节点指向时，它也扮演了Join的角色，此属性定义其合流行为。
                    -->
                    <gateway-config gatewayType="!enum:EXCLUSIVE,PARALLEL,INCLUSIVE=EXCLUSIVE"
                                    joinType="!enum:AND,OR=AND"
                                    xdef:name="GatewayConfigModel"/>
                </xdef:case>

                <xdef:case value="SUBPROCESS">
                    <subprocess-config wfName="!string" wfVersion="long"
                                       xdef:name="SubProcessConfigModel"/>
                </xdef:case>

            </xdef:switch>
        </node>
    </nodes>

    <!--
      抽象出的参与者模型定义，可被审批和抄送节点复用。
    -->
    <xdef:define xdef:name="ParticipantsModel">
        <candidates xdef:body-type="list" xdef:key-attr="id" xdef:bean-child-name="candidate">
            <candidate id="!string" type="!enum:USER,ROLE,DEPT"/>
        </candidates>
    </xdef:define>

    <!--
      元模型中的动作抽象。这部分保持不变，用于定义附加在流程上的运行时行为。
    -->
    <actions xdef:body-type="list" xdef:key-attr="name" xdef:bean-child-name="action">
         <action name="!string" displayName="string" type="!enum:APPROVE,REJECT,COMMENT">
             <when xdef:value="xpl-predicate"/>
             <source xdef:value="xpl"/>
         </action>
    </actions>

</design-model>
