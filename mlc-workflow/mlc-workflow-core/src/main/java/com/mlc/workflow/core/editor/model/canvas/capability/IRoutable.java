package com.mlc.workflow.core.editor.model.canvas.capability;

import com.mlc.workflow.core.editor.structure.manager.EndOwnerManager;

/**
 * 可路由能力接口
 */
public interface IRoutable {

    /**
     * 获取节点ID
     * @return 节点ID
     */
    String getId();

    /**
     * 获取下一个节点ID
     * @return 下一个节点ID
     */
    String getNextId();

    /**
     * 设置下一个节点ID
     * @param nextId 下一个节点ID
     */
    void setNextId(String nextId);

    /**
     * 获取上一个节点ID
     * @return 上一个节点ID
     */
    String getPrveId();

    /**
     * 设置上一个节点ID
     * @param prveId 上一个节点ID
     */
    void setPrveId(String prveId);
    
    /**
     * 验证路由是否有效
     * @return 是否有效
     */
    default boolean hasValidRoute() {
        String nextId = getNextId();
        return nextId != null && (!nextId.trim().isEmpty() || EndOwnerManager.END_OWNER_ID.equals(nextId));
    }
}
