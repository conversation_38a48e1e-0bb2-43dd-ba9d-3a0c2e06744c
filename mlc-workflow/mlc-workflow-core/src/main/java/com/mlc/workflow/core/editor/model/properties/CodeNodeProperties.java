package com.mlc.workflow.core.editor.model.properties;

import com.mlc.base.common.enums.workflow.NodeTypeEnum;
import com.mlc.workflow.core.editor.model.BaseNodeProperties;
import com.mlc.workflow.core.editor.model.visitor.properties.INodeVisitor;
import lombok.Getter;
import lombok.Setter;

/**
 * 代码块任务节点
 * 对应NodeTypeEnum.CODE (14)
 */
@Getter
@Setter
public class CodeNodeProperties extends BaseNodeProperties {

    public CodeNodeProperties() {
        this.setFlowNodeType(NodeTypeEnum.CODE.getValue());
        this.setName("代码块任务");
    }

    @Override
    public void accept(INodeVisitor visitor) {
        visitor.visit(this);
    }
}
