package com.mlc.workflow.core.editor.model.properties;

import com.mlc.base.common.enums.workflow.NodeTypeEnum;
import com.mlc.workflow.core.editor.model.BaseNodeProperties;
import com.mlc.workflow.core.editor.runtime.beans.Account;
import com.mlc.workflow.core.editor.model.visitor.properties.INodeVisitor;
import java.util.List;
import java.util.Map;
import lombok.Getter;
import lombok.Setter;

/**
 * 填写节点 (typeId=3)
 * 用于表单填写任务
 */
@Getter
@Setter
public class WriteNodeProperties extends BaseNodeProperties {

    public WriteNodeProperties() {
        this.setFlowNodeType(NodeTypeEnum.WRITE.getValue());
        this.setName("填写任务");
    }

    /**
     * 经办人
     */
    private List<Account> accounts;

    /**
     * 表单属性
     */
    private Map<String, Object> formProperties;

    /**
     * 添加不允许查看
     */
    private Boolean addNotAllowView;

    /**
     * 多级类型
     */
    private Integer multipleLevelType;

    /**
     * 多级
     */
    private Integer multipleLevel;

    /**
     * 会签类型
     */
    private Integer countersignType;

    /**
     * 条件
     */
    private String condition;

    /**
     * 操作类型列表
     */
    private List<String> operationTypeList;
    
    @Override
    public void accept(INodeVisitor visitor) {
        visitor.visit(this);
    }
}
