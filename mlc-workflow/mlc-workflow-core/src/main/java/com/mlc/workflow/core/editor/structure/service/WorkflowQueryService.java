package com.mlc.workflow.core.editor.structure.service;

import com.mlc.workflow.core.editor.model.BaseNodeCanvas;
import com.mlc.workflow.core.editor.model.canvas.GatewayNodeCanvas;
import com.mlc.workflow.core.editor.runtime.beans.ProcessNode;
import com.mlc.workflow.core.editor.model.canvas.capability.IHasBranches;
import com.mlc.workflow.core.editor.model.canvas.capability.IRoutable;
import com.mlc.workflow.core.editor.structure.manager.EndOwnerManager;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 工作流查询服务
 */
@Slf4j
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class WorkflowQueryService {

    /**
     * 查找指定节点的所有前驱节点
     * @param processNode 流程节点
     * @param targetNodeId 目标节点ID
     * @return 前驱节点列表
     */
    public static List<BaseNodeCanvas> findPrevNodes(ProcessNode processNode, String targetNodeId) {
        if (processNode == null || targetNodeId == null || targetNodeId.trim().isEmpty()) {
            return Collections.emptyList();
        }

        List<BaseNodeCanvas> prevNodes = new ArrayList<>();
        Map<String, BaseNodeCanvas> flowNodeMap = processNode.getFlowNodeMap();
        
        for (BaseNodeCanvas node : flowNodeMap.values()) {
            if (node instanceof IRoutable routableNode) {
                String nextId = routableNode.getNextId();
                if (targetNodeId.equals(nextId)) {
                    prevNodes.add(node);
                }
            }
            
            // 检查网关的分支是否指向目标节点
            if (node instanceof IHasBranches branchNode) {
                List<String> flowIds = branchNode.getFlowIds();
                if (flowIds != null && flowIds.contains(targetNodeId)) {
                    prevNodes.add(node);
                }
            }
        }
        
        return prevNodes;
    }

    /**
     * 收集一条以{@code startNodeId}开头的线性链段
     * 线性链段按照设计方案中的“上下文链”定义，只沿着{@link IRoutable#getNextId()}指针前进，
     * 在遇到空、结束标识或非可路由节点时停止。
     */
    public static LinearChain collectLinearChain(ProcessNode processNode, String startNodeId) {
        if (processNode == null || startNodeId == null || startNodeId.trim().isEmpty()) {
            return LinearChain.empty();
        }

        if (EndOwnerManager.END_OWNER_ID.equals(startNodeId)) {
            return LinearChain.empty();
        }

        Map<String, BaseNodeCanvas> nodeMap = processNode.getFlowNodeMap();
        List<BaseNodeCanvas> nodes = new ArrayList<>();
        Set<String> visited = new HashSet<>();

        String currentId = startNodeId;
        while (!currentId.trim().isEmpty() && visited.add(currentId)) {
            BaseNodeCanvas currentNode = nodeMap.get(currentId);
            if (currentNode == null) {
                break;
            }

            nodes.add(currentNode);

            if (!(currentNode instanceof IRoutable routableNode)) {
                break;
            }

            String nextId = routableNode.getNextId();
            if (nextId == null || nextId.trim().isEmpty()
                || EndOwnerManager.END_OWNER_ID.equals(nextId)) {
                break;
            }

            currentId = nextId;
        }

        return new LinearChain(nodes);
    }
    
    /**
     * 获取网关的分支头节点列表
     * @param processNode 流程节点
     * @param gateway 网关节点
     * @return 分支头节点列表
     */
    public static List<BaseNodeCanvas> branchHeads(ProcessNode processNode, GatewayNodeCanvas gateway) {
        if (processNode == null || gateway == null) {
            return Collections.emptyList();
        }
        
        List<BaseNodeCanvas> branchHeads = new ArrayList<>();
        List<String> flowIds = gateway.getFlowIds();
        
        if (flowIds == null || flowIds.isEmpty()) {
            return branchHeads;
        }
        
        Map<String, BaseNodeCanvas> flowNodeMap = processNode.getFlowNodeMap();
        
        for (String flowId : flowIds) {
            BaseNodeCanvas branchLeaf = flowNodeMap.get(flowId);
            if (branchLeaf instanceof IRoutable routableLeaf) {
                String nextId = routableLeaf.getNextId();
                if (nextId != null && !nextId.trim().isEmpty() && !EndOwnerManager.END_OWNER_ID.equals(nextId)) {
                    BaseNodeCanvas headNode = flowNodeMap.get(nextId);
                    if (headNode != null) {
                        branchHeads.add(headNode);
                    }
                } else {
                    // 分支为空，分支叶子本身就是头节点
                    branchHeads.add(null);
                }
            }
        }
        
        return branchHeads;
    }
    
    /**
     * 获取网关的分支尾节点列表
     * @param processNode 流程节点
     * @param gateway 网关节点
     * @return 分支尾节点列表
     */
    public static List<BaseNodeCanvas> branchTails(ProcessNode processNode, GatewayNodeCanvas gateway) {
        if (processNode == null || gateway == null) {
            return Collections.emptyList();
        }
        
        List<BaseNodeCanvas> branchTails = new ArrayList<>();
        List<String> flowIds = gateway.getFlowIds();
        
        if (flowIds == null || flowIds.isEmpty()) {
            return branchTails;
        }
        
        for (String flowId : flowIds) {
            List<BaseNodeCanvas> tailsInBranch = findBranchTails(processNode, flowId, gateway.getNextId());
            branchTails.addAll(tailsInBranch);
        }
        
        return branchTails;
    }
    
    /**
     * 查找分支中的尾节点
     * @param processNode 流程节点
     * @param branchLeafId 分支叶子ID
     * @param gatewayNextId 网关的下一个节点ID
     * @return 分支尾节点列表
     */
    private static List<BaseNodeCanvas> findBranchTails(ProcessNode processNode, String branchLeafId, String gatewayNextId) {
        List<BaseNodeCanvas> tails = new ArrayList<>();
        Set<String> visited = new HashSet<>();
        
        findBranchTailsRecursive(processNode, branchLeafId, gatewayNextId, tails, visited);
        
        return tails;
    }
    
    /**
     * 递归查找分支尾节点
     * @param processNode 流程节点
     * @param currentNodeId 当前节点ID
     * @param gatewayNextId 网关的下一个节点ID
     * @param tails 尾节点列表
     * @param visited 已访问节点集合
     */
    private static void findBranchTailsRecursive(ProcessNode processNode, String currentNodeId, 
                                        String gatewayNextId, List<BaseNodeCanvas> tails, Set<String> visited) {
        if (currentNodeId == null || visited.contains(currentNodeId)) {
            return;
        }
        
        visited.add(currentNodeId);
        BaseNodeCanvas currentNode = processNode.getFlowNodeMap().get(currentNodeId);
        
        if (currentNode == null) {
            return;
        }
        
        if (currentNode instanceof IRoutable routableNode) {
            String nextId = routableNode.getNextId();
            
            // 如果nextId为空字符串，说明是分支尾节点
            if (nextId == null || nextId.trim().isEmpty()) {
                tails.add(currentNode);
                return;
            }
            
            // 如果nextId指向网关的合流节点，说明是分支尾节点
            if (nextId.equals(gatewayNextId)) {
                tails.add(currentNode);
                return;
            }
            
            // 如果nextId为"99"，说明直接连接到结束
            if (EndOwnerManager.END_OWNER_ID.equals(nextId)) {
                tails.add(currentNode);
                return;
            }
            
            // 继续递归查找
            findBranchTailsRecursive(processNode, nextId, gatewayNextId, tails, visited);
        }
        
        // 处理网关节点的分支
        if (currentNode instanceof IHasBranches branchNode) {
            List<String> flowIds = branchNode.getFlowIds();
            if (flowIds != null) {
                for (String flowId : flowIds) {
                    findBranchTailsRecursive(processNode, flowId, gatewayNextId, tails, visited);
                }
            }
        }
    }
    
    /**
     * 查找指定节点的所有后继节点
     * @param processNode 流程节点
     * @param sourceNodeId 源节点ID
     * @return 后继节点列表
     */
    public static List<BaseNodeCanvas> findNextNodes(ProcessNode processNode, String sourceNodeId) {
        if (processNode == null || sourceNodeId == null) {
            return Collections.emptyList();
        }
        
        BaseNodeCanvas sourceNode = processNode.getFlowNodeMap().get(sourceNodeId);
        if (sourceNode == null) {
            return Collections.emptyList();
        }
        
        List<BaseNodeCanvas> nextNodes = new ArrayList<>();
        
        if (sourceNode instanceof IRoutable routableNode) {
            String nextId = routableNode.getNextId();
            if (nextId != null && !nextId.trim().isEmpty() && !EndOwnerManager.END_OWNER_ID.equals(nextId)) {
                BaseNodeCanvas nextNode = processNode.getFlowNodeMap().get(nextId);
                if (nextNode != null) {
                    nextNodes.add(nextNode);
                }
            }
        }
        
        if (sourceNode instanceof IHasBranches branchNode) {
            List<String> flowIds = branchNode.getFlowIds();
            if (flowIds != null) {
                for (String flowId : flowIds) {
                    BaseNodeCanvas branchLeaf = processNode.getFlowNodeMap().get(flowId);
                    if (branchLeaf != null) {
                        nextNodes.add(branchLeaf);
                    }
                }
            }
        }
        
        return nextNodes;
    }
    
    /**
     * 查找网关节点
     * @param processNode 流程节点
     * @param nodeId 节点ID
     * @return 网关节点，如果不是网关则返回null
     */
    public static GatewayNodeCanvas findGateway(ProcessNode processNode, String nodeId) {
        if (processNode == null || nodeId == null) {
            return null;
        }
        
        BaseNodeCanvas node = processNode.getFlowNodeMap().get(nodeId);
        return node instanceof GatewayNodeCanvas ? (GatewayNodeCanvas) node : null;
    }
    
    /**
     * 查找分支链
     * @param processNode 流程节点
     * @param branchLeafId 分支叶子ID
     * @return 分支链中的所有节点（从分支叶子开始到分支尾部）
     */
    public static List<BaseNodeCanvas> findBranchChain(ProcessNode processNode, String branchLeafId) {
        if (processNode == null || branchLeafId == null) {
            return Collections.emptyList();
        }

        List<BaseNodeCanvas> branchChain = new ArrayList<>();
        Set<String> visited = new HashSet<>();

        findBranchChainRecursive(processNode, branchLeafId, branchChain, visited);

        return branchChain;
    }

    /**
     * 查找网关的分支头节点
     * @param processNode 流程节点
     * @param gateway 网关节点
     * @return 各分支的头节点列表
     */
    public static List<BaseNodeCanvas> findBranchHeads(ProcessNode processNode, GatewayNodeCanvas gateway) {
        if (processNode == null || gateway == null) {
            return Collections.emptyList();
        }

        List<BaseNodeCanvas> branchHeads = new ArrayList<>();
        List<String> flowIds = gateway.getFlowIds();

        if (flowIds != null) {
            for (String flowId : flowIds) {
                BaseNodeCanvas branchLeaf = processNode.getFlowNodeMap().get(flowId);
                if (branchLeaf instanceof IRoutable routableLeaf) {
                    String nextId = routableLeaf.getNextId();
                    if (nextId != null && !nextId.trim().isEmpty() && !EndOwnerManager.END_OWNER_ID.equals(nextId)) {
                        BaseNodeCanvas branchHead = processNode.getFlowNodeMap().get(nextId);
                        if (branchHead != null) {
                            branchHeads.add(branchHead);
                        }
                    }
                    // 如果分支叶子的nextId为空，说明分支为空，头节点就是null
                }
            }
        }

        return branchHeads;
    }

    /**
     * 查找网关的分支尾节点
     * @param processNode 流程节点
     * @param gateway 网关节点
     * @return 各分支的尾节点列表
     */
    public static List<BaseNodeCanvas> findBranchTails(ProcessNode processNode, GatewayNodeCanvas gateway) {
        if (processNode == null || gateway == null) {
            return Collections.emptyList();
        }

        List<BaseNodeCanvas> branchTails = new ArrayList<>();
        List<String> flowIds = gateway.getFlowIds();

        if (flowIds != null) {
            for (String flowId : flowIds) {
                BaseNodeCanvas branchTail = findBranchTail(processNode, flowId);
                if (branchTail != null) {
                    branchTails.add(branchTail);
                }
            }
        }

        return branchTails;
    }

    /**
     * 查找单个分支的尾节点
     * @param processNode 流程节点
     * @param branchLeafId 分支叶子ID
     * @return 分支尾节点
     */
    private static BaseNodeCanvas findBranchTail(ProcessNode processNode, String branchLeafId) {
        Set<String> visited = new HashSet<>();
        return findBranchTailRecursive(processNode, branchLeafId, visited);
    }

    /**
     * 递归查找分支尾节点
     */
    private static BaseNodeCanvas findBranchTailRecursive(ProcessNode processNode, String currentNodeId, Set<String> visited) {
        if (currentNodeId == null || visited.contains(currentNodeId)) {
            return null;
        }

        visited.add(currentNodeId);
        BaseNodeCanvas currentNode = processNode.getFlowNodeMap().get(currentNodeId);

        if (currentNode == null) {
            return null;
        }

        if (currentNode instanceof IRoutable routableNode) {
            String nextId = routableNode.getNextId();
            if (nextId == null || nextId.trim().isEmpty()) {
                // 找到尾节点（nextId为空）
                return currentNode;
            } else if (EndOwnerManager.END_OWNER_ID.equals(nextId)) {
                // 指向结束的节点也是尾节点
                return currentNode;
            } else {
                // 继续递归查找
                return findBranchTailRecursive(processNode, nextId, visited);
            }
        }

        return currentNode;
    }
    
    /**
     * 递归查找分支链
     * @param processNode 流程节点
     * @param currentNodeId 当前节点ID
     * @param branchChain 分支链
     * @param visited 已访问节点集合
     */
    private static void findBranchChainRecursive(ProcessNode processNode, String currentNodeId, 
                                        List<BaseNodeCanvas> branchChain, Set<String> visited) {
        if (currentNodeId == null || visited.contains(currentNodeId)) {
            return;
        }
        
        visited.add(currentNodeId);
        BaseNodeCanvas currentNode = processNode.getFlowNodeMap().get(currentNodeId);
        
        if (currentNode == null) {
            return;
        }
        
        branchChain.add(currentNode);
        
        if (currentNode instanceof IRoutable routableNode) {
            String nextId = routableNode.getNextId();
            if (nextId != null && !nextId.trim().isEmpty() && !EndOwnerManager.END_OWNER_ID.equals(nextId)) {
                findBranchChainRecursive(processNode, nextId, branchChain, visited);
            }
        }
    }

    /**
     * 线性链段快照
     */
    public static final class LinearChain {
        private final List<BaseNodeCanvas> nodes;

        private LinearChain(List<BaseNodeCanvas> nodes) {
            this.nodes = nodes;
        }

        public static LinearChain empty() {
            return new LinearChain(Collections.emptyList());
        }

        public boolean isEmpty() {
            return nodes.isEmpty();
        }

        public List<BaseNodeCanvas> nodes() {
            return nodes;
        }

        public BaseNodeCanvas head() {
            return isEmpty() ? null : nodes.get(0);
        }

        public BaseNodeCanvas tail() {
            return isEmpty() ? null : nodes.get(nodes.size() - 1);
        }

        public boolean containsEndOwner() {
            for (BaseNodeCanvas node : nodes) {
                if (node instanceof IRoutable routable &&
                    EndOwnerManager.END_OWNER_ID.equals(routable.getNextId())) {
                    return true;
                }
            }
            return false;
        }
    }
}
