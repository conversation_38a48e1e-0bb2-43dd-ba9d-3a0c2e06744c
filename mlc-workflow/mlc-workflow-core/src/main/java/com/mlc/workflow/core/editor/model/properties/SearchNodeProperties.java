package com.mlc.workflow.core.editor.model.properties;

import com.mlc.base.common.enums.workflow.NodeTypeEnum;
import com.mlc.workflow.core.editor.model.BaseNodeProperties;
import com.mlc.workflow.core.editor.model.visitor.properties.INodeVisitor;
import lombok.Getter;
import lombok.Setter;

/**
 * 获取单条数据任务节点
 * 对应NodeTypeEnum.SEARCH (7)
 */
@Getter
@Setter
public class SearchNodeProperties extends BaseNodeProperties {

    public SearchNodeProperties() {
        this.setFlowNodeType(NodeTypeEnum.SEARCH.getValue());
        this.setName("获取单条数据任务");
    }
    
    @Override
    public void accept(INodeVisitor visitor) {
        visitor.visit(this);
    }
}
