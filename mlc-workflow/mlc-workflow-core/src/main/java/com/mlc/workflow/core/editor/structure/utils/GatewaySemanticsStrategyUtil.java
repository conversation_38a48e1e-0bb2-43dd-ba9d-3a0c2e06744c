package com.mlc.workflow.core.editor.structure.utils;

import com.mlc.workflow.core.editor.runtime.beans.ConditionGroup;
import com.mlc.workflow.core.editor.runtime.beans.ProcessNode;
import com.mlc.workflow.core.editor.model.canvas.ConditionNodeCanvas;
import com.mlc.workflow.core.editor.model.canvas.GatewayNodeCanvas;
import com.mlc.workflow.core.editor.model.BaseNodeCanvas;
import com.mlc.workflow.core.editor.structure.manager.EndOwnerManager;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 网关语义策略工具类
 * 封装并行与唯一分支的合流/触发语义，处理网关类型变更时的副作用
 */
@Slf4j
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class GatewaySemanticsStrategyUtil {
    
    /**
     * 网关类型常量
     */
    public static final int GATEWAY_TYPE_PARALLEL = 1; // 并行分支
    public static final int GATEWAY_TYPE_EXCLUSIVE = 2; // 唯一分支
    
    /**
     * 切换网关类型
     * @param processNode 流程节点
     * @param gateway 网关节点
     * @param toType 目标类型
     */
    public static void switchGatewayType(ProcessNode processNode, GatewayNodeCanvas gateway, Integer toType) {
        if (processNode == null || gateway == null || toType == null) {
            throw new IllegalArgumentException("参数不能为空");
        }
        
        Integer fromType = gateway.getGatewayType();

        // 如果fromType为null，设置默认值为并行分支
        if (fromType == null) {
            fromType = GATEWAY_TYPE_PARALLEL;
            gateway.setGatewayType(fromType);
            log.debug("网关 {} 类型为null，设置默认值为并行分支: {}", gateway.getId(), fromType);
        }

        if (Objects.equals(fromType, toType)) {
            log.debug("网关 {} 类型未变化: {}", gateway.getId(), toType);
            return;
        }

        log.debug("切换网关 {} 类型: {} -> {}", gateway.getId(), fromType, toType);

        if (fromType.intValue() == GATEWAY_TYPE_PARALLEL && toType.intValue() == GATEWAY_TYPE_EXCLUSIVE) {
            // 并行 -> 唯一
            switchParallelToExclusive(processNode, gateway);
        } else if (fromType.intValue() == GATEWAY_TYPE_EXCLUSIVE && toType.intValue() == GATEWAY_TYPE_PARALLEL) {
            // 唯一 -> 并行
            switchExclusiveToParallel(processNode, gateway);
        }
        
        // 更新网关类型
        gateway.setGatewayType(toType);
        
        // 确保分支尾部的一致性
        ensureBranchTailConsistency(processNode, gateway);
    }
    
    /**
     * 并行分支切换为唯一分支
     * @param processNode 流程节点
     * @param gateway 网关节点
     */
    private static void switchParallelToExclusive(ProcessNode processNode, GatewayNodeCanvas gateway) {
        List<String> flowIds = gateway.getFlowIds();
        if (flowIds == null || flowIds.isEmpty()) {
            log.warn("网关 {} 没有分支，跳过条件生成", gateway.getId());
            return;
        }

        log.info("开始为网关 {} 的 {} 个分支生成条件", gateway.getId(), flowIds.size());

        // 为每条分支叶子补齐默认条件
        for (int i = 0; i < flowIds.size(); i++) {
            String flowId = flowIds.get(i);
            BaseNodeCanvas branchLeaf = processNode.getFlowNodeMap().get(flowId);

            log.info("处理分支 {} (索引 {}): {}", flowId, i, branchLeaf != null ? branchLeaf.getClass().getSimpleName() : "null");

            if (branchLeaf instanceof ConditionNodeCanvas conditionNode) {
                ensureDefaultCondition(conditionNode, i, flowIds.size());
            } else {
                log.warn("分支 {} 不是 ConditionNode 类型: {}", flowId, branchLeaf != null ? branchLeaf.getClass().getSimpleName() : "null");
            }
        }

        log.info("网关 {} 从并行切换为唯一，已补齐条件", gateway.getId());
    }
    
    /**
     * 唯一分支切换为并行分支
     * @param processNode 流程节点
     * @param gateway 网关节点
     */
    private static void switchExclusiveToParallel(ProcessNode processNode, GatewayNodeCanvas gateway) {
        List<String> flowIds = gateway.getFlowIds();
        if (flowIds == null || flowIds.isEmpty()) {
            return;
        }
        
        // 清空或忽略条件（可以迁移到分支元数据但不参与路由）
        for (String flowId : flowIds) {
            BaseNodeCanvas branchLeaf = processNode.getFlowNodeMap().get(flowId);

            if (branchLeaf instanceof ConditionNodeCanvas conditionNode) {
                // 保留条件但标记为不参与路由（可以在元数据中保存）
                // 这里简单处理，可以根据需要保存到其他地方
                log.debug("分支叶子 {} 的条件将不再参与路由判断", flowId);
            }
        }
        
        log.debug("网关 {} 从唯一切换为并行，条件已清理", gateway.getId());
    }
    
    /**
     * 为条件节点确保默认条件
     * @param conditionNode 条件节点
     * @param index 分支索引
     * @param totalBranches 总分支数
     */
    private static void ensureDefaultCondition(ConditionNodeCanvas conditionNode, int index, int totalBranches) {
        List<List<ConditionGroup>> operateCondition = conditionNode.getOperateCondition();

        log.debug("检查分支叶子 {} 的条件，当前条件: {}", conditionNode.getId(), operateCondition);

        // 检查是否需要生成条件（空列表或所有组都为空）
        boolean needsCondition = operateCondition == null || operateCondition.isEmpty() ||
                                operateCondition.stream().allMatch(group -> group == null || group.isEmpty() ||
                                    group.stream().allMatch(condition -> condition == null ||
                                        (condition.getFiledId() == null && condition.getConditionId() == null)));

        log.debug("分支叶子 {} 是否需要生成条件: {}", conditionNode.getId(), needsCondition);

        if (needsCondition) {
            // 创建默认条件
            operateCondition = new ArrayList<>();
            conditionNode.setOperateCondition(operateCondition);

            if (index == totalBranches - 1) {
                // 最后一条分支作为"else"分支
                createElseCondition(operateCondition);
                log.debug("为分支叶子 {} 创建else条件", conditionNode.getId());
            } else {
                // 其他分支创建"始终成立"条件
                createAlwaysTrueCondition(operateCondition);
                log.debug("为分支叶子 {} 创建默认条件", conditionNode.getId());
            }

            // 验证条件是否有效
            boolean isValid = conditionNode.hasValidConditions();
            log.info("分支叶子 {} 条件生成后验证结果: {}, 条件内容: {}",
                    conditionNode.getId(), isValid, conditionNode.getOperateCondition());
        } else {
            log.debug("分支叶子 {} 已有条件，跳过生成", conditionNode.getId());
        }
    }
    
    /**
     * 创建"始终成立"条件
     * @param operateCondition 操作条件列表
     */
    private static void createAlwaysTrueCondition(List<List<ConditionGroup>> operateCondition) {
        List<ConditionGroup> conditionGroup = new ArrayList<>();
        
        ConditionGroup condition = new ConditionGroup();
        condition.setNodeId("system");
        condition.setNodeName("系统");
        condition.setFiledId("always_true");
        condition.setFiledValue("始终成立");
        condition.setConditionId("1");
        condition.setValue("true");
        
        conditionGroup.add(condition);
        operateCondition.add(conditionGroup);
    }
    
    /**
     * 创建"else"条件
     * @param operateCondition 操作条件列表
     */
    private static void createElseCondition(List<List<ConditionGroup>> operateCondition) {
        List<ConditionGroup> conditionGroup = new ArrayList<>();
        
        ConditionGroup condition = new ConditionGroup();
        condition.setNodeId("system");
        condition.setNodeName("系统");
        condition.setFiledId("else");
        condition.setFiledValue("其他情况");
        condition.setConditionId("else");
        condition.setValue("else");
        
        conditionGroup.add(condition);
        operateCondition.add(conditionGroup);
    }
    
    /**
     * 确保分支尾部的一致性
     * @param processNode 流程节点
     * @param gateway 网关节点
     */
    private static void ensureBranchTailConsistency(ProcessNode processNode, GatewayNodeCanvas gateway) {
        List<String> flowIds = gateway.getFlowIds();
        if (flowIds == null || flowIds.isEmpty()) {
            return;
        }
        
        String gatewayNextId = gateway.getNextId();
        
        for (String flowId : flowIds) {
            ensureBranchTailConsistencyRecursive(processNode, flowId, gatewayNextId, new HashSet<>());
        }
    }
    
    /**
     * 递归确保分支尾部一致性
     * @param processNode 流程节点
     * @param currentNodeId 当前节点ID
     * @param gatewayNextId 网关的下一个节点ID
     * @param visited 已访问节点集合
     */
    private static void ensureBranchTailConsistencyRecursive(ProcessNode processNode, String currentNodeId, 
                                                    String gatewayNextId, Set<String> visited) {
        if (currentNodeId == null || visited.contains(currentNodeId)) {
            return;
        }
        
        visited.add(currentNodeId);
        BaseNodeCanvas currentNode = processNode.getFlowNodeMap().get(currentNodeId);
        
        if (currentNode == null) {
            return;
        }
        
        if (currentNode instanceof ConditionNodeCanvas conditionNode) {
            String nextId = conditionNode.getNextId();
            
            // 如果分支叶子的nextId为空或指向网关的合流节点，确保一致性
            if (nextId == null || nextId.trim().isEmpty()) {
                // 分支为空，设置为空字符串（等待合流）
                conditionNode.setNextId("");
            } else if (!nextId.equals(gatewayNextId) && !EndOwnerManager.END_OWNER_ID.equals(nextId)) {
                // 继续递归检查
                ensureBranchTailConsistencyRecursive(processNode, nextId, gatewayNextId, visited);
            }
        }
    }
    
    /**
     * 验证网关语义的一致性
     * @param processNode 流程节点
     * @param gateway 网关节点
     * @return 验证结果
     */
    public static boolean validateGatewaySemantics(ProcessNode processNode, GatewayNodeCanvas gateway) {
        if (processNode == null || gateway == null) {
            return false;
        }
        
        List<String> flowIds = gateway.getFlowIds();
        if (flowIds == null || flowIds.isEmpty()) {
            log.warn("网关 {} 没有分支", gateway.getId());
            return false;
        }
        
        Integer gatewayType = gateway.getGatewayType();
        
        if (gatewayType != null && gatewayType == GATEWAY_TYPE_EXCLUSIVE) {
            // 唯一分支：检查条件完备性
            return validateExclusiveGatewayConditions(processNode, gateway);
        } else if (gatewayType != null && gatewayType == GATEWAY_TYPE_PARALLEL) {
            // 并行分支：检查分支尾部一致性
            return validateParallelGatewayTails(processNode, gateway);
        }
        
        return true;
    }
    
    /**
     * 验证唯一分支的条件完备性
     * @param processNode 流程节点
     * @param gateway 网关节点
     * @return 验证结果
     */
    private static boolean validateExclusiveGatewayConditions(ProcessNode processNode, GatewayNodeCanvas gateway) {
        List<String> flowIds = gateway.getFlowIds();
        boolean hasElse = false;
        
        for (String flowId : flowIds) {
            BaseNodeCanvas branchLeaf = processNode.getFlowNodeMap().get(flowId);
            
            if (branchLeaf instanceof ConditionNodeCanvas conditionNode) {
                if (!conditionNode.hasValidConditions()) {
                    log.warn("分支叶子 {} 缺少有效条件", flowId);
                    return false;
                }
                
                // 检查是否有else条件
                List<List<ConditionGroup>> conditions = conditionNode.getOperateCondition();
                if (conditions != null) {
                    for (List<ConditionGroup> conditionGroup : conditions) {
                        for (ConditionGroup condition : conditionGroup) {
                            if ("else".equals(condition.getConditionId())) {
                                if (hasElse) {
                                    log.warn("网关 {} 有多个else分支", gateway.getId());
                                    return false;
                                }
                                hasElse = true;
                            }
                        }
                    }
                }
            }
        }
        
        return true;
    }
    
    /**
     * 验证并行分支的尾部一致性
     * @param processNode 流程节点
     * @param gateway 网关节点
     * @return 验证结果
     */
    private static boolean validateParallelGatewayTails(ProcessNode processNode, GatewayNodeCanvas gateway) {
        // 并行分支的尾部应该都以空字符串结尾（等待合流）
        // 这里可以根据具体需求实现验证逻辑
        return true;
    }
}
