package com.mlc.workflow.core.editor.model.properties;

import com.mlc.base.common.enums.workflow.NodeTypeEnum;
import com.mlc.workflow.core.editor.model.BaseNodeProperties;
import com.mlc.workflow.core.editor.model.visitor.properties.INodeVisitor;
import lombok.Getter;
import lombok.Setter;

/**
 * 邮件任务节点
 * 对应NodeTypeEnum.EMAIL (11)
 */
@Getter
@Setter
public class EmailNodeProperties extends BaseNodeProperties {

    public EmailNodeProperties() {
        this.setFlowNodeType(NodeTypeEnum.EMAIL.getValue());
        this.setName("邮件任务");
    }

    @Override
    public void accept(INodeVisitor visitor) {
        visitor.visit(this);
    }
}
