package com.mlc.workflow.core.editor.model.properties;

import com.mlc.base.common.enums.workflow.NodeTypeEnum;
import com.mlc.workflow.core.editor.model.BaseNodeProperties;
import com.mlc.workflow.core.editor.model.visitor.properties.INodeVisitor;
import lombok.Getter;
import lombok.Setter;

/**
 * 调用封装业务流程任务节点
 * 对应NodeTypeEnum.PBC (20)
 */
@Getter
@Setter
public class PbcNodeProperties extends BaseNodeProperties {

    public PbcNodeProperties() {
        this.setFlowNodeType(NodeTypeEnum.PBC.getValue());
        this.setName("调用封装业务流程任务");
    }

    @Override
    public void accept(INodeVisitor visitor) {
        visitor.visit(this);
    }
}
