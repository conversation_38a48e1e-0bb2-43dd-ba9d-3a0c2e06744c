package com.mlc.workflow.core.editor.structure.operation;

import com.mlc.workflow.core.editor.runtime.beans.ProcessNode;
import com.mlc.workflow.core.editor.model.BaseNodeCanvas;
import com.mlc.workflow.core.editor.model.canvas.capability.IRoutable;
import com.mlc.workflow.core.editor.structure.autowire.AutoWireStrategy;
import com.mlc.workflow.core.editor.structure.manager.EndOwnerManager;
import com.mlc.workflow.core.editor.structure.utils.ValidParamsUtil;
import com.mlc.workflow.core.editor.structure.executor.NodeBatchExecutor;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;

/**
 * 普通节点操作命令
 * 实现普通节点的新增、删除、修改等操作
 */
@Slf4j
public class NodeOperations {

    private final NodeBatchExecutor nodeBatchExecutor;
    private final AutoWireStrategy autoWireStrategy;

    public NodeOperations(NodeBatchExecutor nodeBatchExecutor, AutoWireStrategy autoWireStrategy) {
        this.nodeBatchExecutor = nodeBatchExecutor;
        this.autoWireStrategy = autoWireStrategy;
    }


    /**
     * 插入节点
     * 根据设计方案：使用SpliceBetween原语实现节点插入
     * @param afterNodeId 在此节点之后插入
     * @param newNode 新节点规格
     * @return 创建的节点
     */
    public BaseNodeCanvas insertNode(String afterNodeId, BaseNodeCanvas newNode) {
        ValidParamsUtil.validateNodeOperationComplete(nodeBatchExecutor, afterNodeId, newNode);

        ProcessNode workingCopy = nodeBatchExecutor.getWorkingCopy();
        BaseNodeCanvas afterNode = ValidParamsUtil.requireNodeExists(workingCopy, afterNodeId, "插入点");
        IRoutable routableAfterNode = ValidParamsUtil.requireRoutable(afterNode, "插入点节点");

        String originalNextId = routableAfterNode.getNextId();

        // 创建新节点
        nodeBatchExecutor.createNode(newNode);

        // 使用AutoWireStrategy的SpliceBetween原语
        // 取得A=afterNode与B=A.nextId，构造新节点N，执行SpliceBetween([A], N, N, B)
        List<BaseNodeCanvas> prevNodes = Arrays.asList(afterNode);
        autoWireStrategy.spliceBetween(workingCopy, prevNodes, newNode, newNode, originalNextId);

        // 更新涉及的节点到批处理器
        nodeBatchExecutor.updateNode(afterNodeId, afterNode);
        nodeBatchExecutor.updateNode(newNode.getId(), newNode);
        
        // 如果有下一个节点，也需要更新
        if (originalNextId != null && !originalNextId.trim().isEmpty() && 
            !EndOwnerManager.END_OWNER_ID.equals(originalNextId)) {
            BaseNodeCanvas nextNode = workingCopy.getFlowNodeMap().get(originalNextId);
            if (nextNode != null) {
                nodeBatchExecutor.updateNode(originalNextId, nextNode);
            }
        }

        log.debug("使用SpliceBetween原语成功在节点 {} 后插入新节点 {}", afterNodeId, newNode.getId());
        return newNode;
    }

    /**
     * 删除节点
     * 根据设计方案：使用Replace(node, ∅)原语实现节点删除
     * @param nodeId 要删除的节点ID
     */
    public void deleteNode(String nodeId) {
        ValidParamsUtil.validateNodeOperationComplete(nodeBatchExecutor, nodeId, null);

        ProcessNode workingCopy = nodeBatchExecutor.getWorkingCopy();
        BaseNodeCanvas nodeToDelete = ValidParamsUtil.requireNodeExists(workingCopy, nodeId, "要删除的节点");
        ValidParamsUtil.requireRoutable(nodeToDelete, "要删除的节点");

        log.info("开始删除节点: {}", nodeId);

        // 使用AutoWireStrategy的deleteNode原语
        // 根据设计方案：找prevs(nodeId)与next=node.nextId，执行Replace(node, ∅)等价：prevs[*].nextId = next
        AutoWireStrategy.DeleteNodeContext deleteContext = autoWireStrategy.deleteNode(workingCopy, nodeToDelete);

        // 更新所有涉及的节点到批处理器
        for (BaseNodeCanvas prevNode : deleteContext.getPrevNodes()) {
            nodeBatchExecutor.updateNode(prevNode.getId(), prevNode);
        }

        // 如果有新的EndOwner，更新它
        if (deleteContext.getNewEndOwner() != null) {
            nodeBatchExecutor.updateNode(deleteContext.getNewEndOwner().getId(), deleteContext.getNewEndOwner());
        }

        // 如果有下一个节点且不是EndOwner，更新它
        if (deleteContext.getNextId() != null && !deleteContext.getNextId().trim().isEmpty() && 
            !EndOwnerManager.END_OWNER_ID.equals(deleteContext.getNextId())) {
            BaseNodeCanvas nextNode = workingCopy.getFlowNodeMap().get(deleteContext.getNextId());
            if (nextNode != null) {
                nodeBatchExecutor.updateNode(deleteContext.getNextId(), nextNode);
            }
        }

        // 删除节点
        nodeBatchExecutor.deleteNode(nodeId);
        
        log.debug("使用Replace(node, ∅)原语成功删除节点 {}，是否为EndOwner: {}", 
                nodeId, deleteContext.isEndOwner());
    }

    /**
     * 更新节点
     * @param nodeId 节点ID
     * @param updates 更新内容
     */
    public void updateNode(String nodeId, Map<String, Object> updates) {
        ValidParamsUtil.validateNodeOperationComplete(nodeBatchExecutor, nodeId, null);
        ValidParamsUtil.validateUpdateParams(updates);

        ProcessNode workingCopy = nodeBatchExecutor.getWorkingCopy();
        BaseNodeCanvas node = ValidParamsUtil.requireNodeExists(workingCopy, nodeId, "要更新的节点");

        // 应用更新
        applyUpdatesToNode(node, updates);

        // 更新节点
        nodeBatchExecutor.updateNode(nodeId, node);
        log.debug("更新节点 {} 完成", nodeId);
    }


    /**
     * 应用更新到节点
     */
    private void applyUpdatesToNode(BaseNodeCanvas node, Map<String, Object> updates) {
        for (Map.Entry<String, Object> entry : updates.entrySet()) {
            String key = entry.getKey();
            Object value = entry.getValue();

            switch (key) {
                case "name":
                    if (value instanceof String) {
                        node.setName((String) value);
                    }
                    break;
                case "nextId":
                    if (node instanceof IRoutable routableNode && value instanceof String) {
                        routableNode.setNextId((String) value);
                    }
                    break;
                case "prveId":
                    if (node instanceof IRoutable routableNode && value instanceof String) {
                        routableNode.setPrveId((String) value);
                    }
                    break;
                default:
                    log.debug("跳过更新属性: {} = {}", key, value);
                    break;
            }
        }
    }

    /**
     * 移动节点到新位置
     * @param processNode 流程节点
     * @param nodeId 要移动的节点ID
     * @param newAfterNodeId 新的前驱节点ID
     * @deprecated 此方法尚未重构为使用nodeBatchExecutor
     */
    @Deprecated
    public void moveNode(ProcessNode processNode, String nodeId, String newAfterNodeId) {
        ValidParamsUtil.requireAllNonNull(processNode, "processNode", nodeId, "nodeId", newAfterNodeId, "newAfterNodeId");

        BaseNodeCanvas nodeToMove = ValidParamsUtil.requireNodeExists(processNode, nodeId, "要移动的节点");

        // 先断开原有连接
        autoWireStrategy.detach(processNode, nodeToMove, nodeToMove);

        // 连接到新位置
        BaseNodeCanvas newAfterNode = processNode.getFlowNodeMap().get(newAfterNodeId);
        if (newAfterNode instanceof IRoutable routableAfter) {
            String originalNext = routableAfter.getNextId();
            autoWireStrategy.spliceBetween(processNode, Arrays.asList(newAfterNode),
                                           nodeToMove, nodeToMove, originalNext);
        }

        log.debug("移动节点 {} 到节点 {} 之后", nodeId, newAfterNodeId);
    }

}
