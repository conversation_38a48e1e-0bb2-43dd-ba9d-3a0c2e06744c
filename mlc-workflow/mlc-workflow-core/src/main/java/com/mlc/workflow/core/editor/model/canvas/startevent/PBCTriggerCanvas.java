package com.mlc.workflow.core.editor.model.canvas.startevent;

import com.mlc.workflow.core.editor.model.canvas.StartEventNodeCanvas;
import com.mlc.workflow.core.editor.model.visitor.canvas.ICanvasNodeVisitor;
import lombok.Getter;
import lombok.Setter;

/**
 * 按日期字段触发节点属性 (appType=6)
 */
@Getter
@Setter
public class PBCTriggerCanvas extends StartEventNodeCanvas {

    public PBCTriggerCanvas() {
        super();
        this.setAppType(17);
        this.setName("按日期字段触发");
    }

    /**
     * 应用ID
     */
    private String appId;

    /**
     * 是否回调
     */
    private Boolean isCallBack;

    /**
     * 参数总数
     */
    private int count;

    @Override
    public void accept(ICanvasNodeVisitor visitor) {
        visitor.visit(this);
    }
}
