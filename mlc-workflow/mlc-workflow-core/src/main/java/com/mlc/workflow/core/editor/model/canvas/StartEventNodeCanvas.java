package com.mlc.workflow.core.editor.model.canvas;

import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.mlc.base.common.enums.workflow.NodeTypeEnum;
import com.mlc.workflow.core.editor.model.canvas.startevent.DateFieldTriggerCanvas;
import com.mlc.workflow.core.editor.model.canvas.startevent.ExternalUserEventTriggerCanvas;
import com.mlc.workflow.core.editor.model.canvas.startevent.PBCTriggerCanvas;
import com.mlc.workflow.core.editor.model.canvas.startevent.ScheduleTriggerCanvas;
import com.mlc.workflow.core.editor.model.canvas.startevent.SheetEventTriggerCanvas;
import com.mlc.workflow.core.editor.model.canvas.startevent.UserEventTriggerCanvas;
import com.mlc.workflow.core.editor.model.canvas.startevent.WebhookTriggerCanvas;
import com.mlc.workflow.core.editor.runtime.beans.Account;
import com.mlc.workflow.core.editor.model.canvas.capability.IHasAccounts;
import com.mlc.workflow.core.editor.model.visitor.canvas.ICanvasNodeVisitor;
import io.nop.api.core.annotations.data.DataBean;
import lombok.EqualsAndHashCode;

import java.util.ArrayList;
import java.util.List;
import lombok.Getter;
import lombok.Setter;

/**
 * 起始事件节点 (typeId=0)
 * 工作流的起始节点，触发工作流的执行
 * @see NodeTypeEnum#START
 */
@Getter
@Setter
@EqualsAndHashCode(callSuper = true)
@DataBean
@JsonTypeInfo(
    use = JsonTypeInfo.Id.NAME,
    include = JsonTypeInfo.As.EXISTING_PROPERTY,
    property = "appType",
    visible = true,
    defaultImpl = PBCTriggerCanvas.class)
@JsonSubTypes({
    @JsonSubTypes.Type(value = SheetEventTriggerCanvas.class, name = "1"),
    @JsonSubTypes.Type(value = ScheduleTriggerCanvas.class, name = "5"),
    @JsonSubTypes.Type(value = DateFieldTriggerCanvas.class, name = "6"),
    @JsonSubTypes.Type(value = WebhookTriggerCanvas.class, name = "7"),
    @JsonSubTypes.Type(value = UserEventTriggerCanvas.class, name = "20"),
    @JsonSubTypes.Type(value = ExternalUserEventTriggerCanvas.class, name = "23"),
    @JsonSubTypes.Type(value = PBCTriggerCanvas.class, name = "17")
})
public class StartEventNodeCanvas extends FlowNodeCanvas implements IHasAccounts {

    public StartEventNodeCanvas() {
        this.setTypeId(NodeTypeEnum.START.getValue());
        this.setName("起始事件节点");
    }

    /**
     * 应用类型
     */
    private Integer appType;

    /**
     * 触发器名称（用于子流程）
     */
    private String triggerName;
    
    /**
     * 触发器节点ID（用于子流程）
     */
    private String triggerNodeId;

    /**
     * 账户列表（起始节点可能包含账户信息）
     */
    private List<Account> accounts = new ArrayList<>();

    /**
     * 实现访问者模式的accept方法
     * @param visitor Canvas节点访问者
     */
    @Override
    public void accept(ICanvasNodeVisitor visitor) {
        visitor.visit(this);
    }
}
