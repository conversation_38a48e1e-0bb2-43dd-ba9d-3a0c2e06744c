package com.mlc.workflow.core.editor.structure.operation;

import com.mlc.workflow.core.editor.model.BaseNodeCanvas;
import com.mlc.workflow.core.editor.model.canvas.ConditionNodeCanvas;
import com.mlc.workflow.core.editor.model.canvas.GatewayNodeCanvas;
import com.mlc.workflow.core.editor.runtime.beans.ProcessNode;
import com.mlc.workflow.core.editor.runtime.beans.ConditionGroup;
import com.mlc.workflow.core.editor.model.canvas.capability.IRoutable;
import com.mlc.workflow.core.editor.structure.autowire.AutoWireStrategy;
import com.mlc.workflow.core.editor.structure.autowire.AutoWireStrategy.DetachContext;
import com.mlc.workflow.core.editor.structure.manager.EndOwnerManager;
import com.mlc.workflow.core.editor.structure.utils.GatewaySemanticsStrategyUtil;
import com.mlc.workflow.core.editor.structure.utils.ValidParamsUtil;
import com.mlc.workflow.core.editor.structure.service.WorkflowQueryService;
import com.mlc.workflow.core.editor.structure.service.WorkflowQueryService.LinearChain;
import com.mlc.workflow.core.editor.structure.executor.NodeBatchExecutor;
import org.apache.commons.lang3.StringUtils;
import java.util.ArrayList;
import java.util.Collections;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Set;
import lombok.extern.slf4j.Slf4j;

/**
 * 网关操作命令
 * 实现网关的新增、删除、类型切换等操作
 */
@Slf4j
public class GatewayOperations {

    private final AutoWireStrategy autoWireStrategy;
    private final NodeBatchExecutor nodeBatchExecutor;

    public GatewayOperations(NodeBatchExecutor nodeBatchExecutor, AutoWireStrategy autoWireStrategy) {
        this.autoWireStrategy = autoWireStrategy;
        this.nodeBatchExecutor = nodeBatchExecutor;
    }

    /**
     * 放置策略枚举
     */
    public enum PlacementStrategy {
        LEFT_PLACEMENT,  // 左侧放置（移动原有链段到左分支）
        NO_MOVE         // 不移动（在原位置插入网关）
    }

    /**
     * 新增网关（默认并行）
     *
     * @param atNodeId 插入点节点ID
     * @param placement 放置策略
     * @return 创建的网关节点
     */
    public GatewayNodeCanvas addGateway(String atNodeId, PlacementStrategy placement) {
        return addGateway(atNodeId, placement, GatewaySemanticsStrategyUtil.GATEWAY_TYPE_PARALLEL);
    }

    /**
     * 新增网关
     *
     * @param atNodeId 插入点节点ID
     * @param placement 放置策略
     * @param gatewayType 网关类型
     * @return 创建的网关节点
     */
    public GatewayNodeCanvas addGateway(String atNodeId, PlacementStrategy placement, Integer gatewayType) {
        ValidParamsUtil.validateGatewayOperationParams(atNodeId, "atNodeId");
        ValidParamsUtil.validateExecutorState(nodeBatchExecutor);
        ValidParamsUtil.validateGatewayType(gatewayType);

        ProcessNode workingCopy = nodeBatchExecutor.getWorkingCopy();
        BaseNodeCanvas atNode = ValidParamsUtil.requireNodeExists(workingCopy, atNodeId, "插入点");
        IRoutable routableAtNode = ValidParamsUtil.requireRoutable(atNode, "插入点节点");

        String originalNextId = routableAtNode.getNextId();

        GatewayNodeCanvas gateway = new GatewayNodeCanvas();
        gateway.setName("网关");
        gateway.setGatewayType(gatewayType);

        ConditionNodeCanvas leftBranch = new ConditionNodeCanvas();
        leftBranch.setName("分支1");

        ConditionNodeCanvas rightBranch = new ConditionNodeCanvas();
        rightBranch.setName("分支2");

        if (gatewayType == GatewaySemanticsStrategyUtil.GATEWAY_TYPE_EXCLUSIVE) {
            setDefaultConditionForBranch(leftBranch, 0);
            setDefaultConditionForBranch(rightBranch, 1);
        }

        leftBranch.setPrveId(gateway.getId());
        rightBranch.setPrveId(gateway.getId());
        gateway.getFlowIds().add(leftBranch.getId());
        gateway.getFlowIds().add(rightBranch.getId());

        nodeBatchExecutor.createNode(gateway);
        nodeBatchExecutor.createNode(leftBranch);
        nodeBatchExecutor.createNode(rightBranch);

        autoWireStrategy.spliceBetween(workingCopy, Collections.singletonList(atNode),
            gateway, gateway, originalNextId);

        nodeBatchExecutor.updateNode(atNode.getId(), atNode);
        if (!StringUtils.isBlank(originalNextId) && !isEndOwner(originalNextId)) {
            BaseNodeCanvas originalNext = workingCopy.getFlowNodeMap().get(originalNextId);
            if (originalNext != null) {
                nodeBatchExecutor.updateNode(originalNextId, originalNext);
            }
        }

        Set<BaseNodeCanvas> dirtyNodes = new LinkedHashSet<>();
        dirtyNodes.add(gateway);
        dirtyNodes.add(leftBranch);
        dirtyNodes.add(rightBranch);

        if (placement == PlacementStrategy.LEFT_PLACEMENT) {
            dirtyNodes.addAll(handleLeftPlacement(workingCopy, gateway, leftBranch, rightBranch, originalNextId));
        } else {
            dirtyNodes.addAll(handleNoMove(gateway, leftBranch, rightBranch));
        }

        registerUpdates(dirtyNodes);

        log.debug("在节点 {} 后新增网关 {}，策略: {}", atNodeId, gateway.getId(), placement);
        return gateway;
    }

    private Set<BaseNodeCanvas> handleLeftPlacement(ProcessNode processNode, GatewayNodeCanvas gateway,
        ConditionNodeCanvas leftBranch, ConditionNodeCanvas rightBranch, String originalNextId) {
        Set<BaseNodeCanvas> dirtyNodes = new LinkedHashSet<>();
        dirtyNodes.add(gateway);
        dirtyNodes.add(leftBranch);
        dirtyNodes.add(rightBranch);

        leftBranch.setNextId(EndOwnerManager.BRANCH_END_ID);
        rightBranch.setNextId(EndOwnerManager.BRANCH_END_ID);

        if (StringUtils.isBlank(originalNextId)) {
            gateway.setNextId(EndOwnerManager.BRANCH_END_ID);
            log.debug("左侧放置：原节点无下游，两个分支为空");
            return dirtyNodes;
        }

        if (isEndOwner(originalNextId)) {
            gateway.setNextId(EndOwnerManager.END_OWNER_ID);
            log.debug("左侧放置：原节点指向结束，网关 {} 继承 EndOwner", gateway.getId());
            return dirtyNodes;
        }

        LinearChain chain = WorkflowQueryService.collectLinearChain(processNode, originalNextId);
        if (chain.isEmpty()) {
            log.warn("左侧放置：未找到可移动的链段(head={})，保持空分支", originalNextId);
            gateway.setNextId(originalNextId);
            return dirtyNodes;
        }

        BaseNodeCanvas chainHead = chain.head();
        BaseNodeCanvas chainTail = chain.tail();
        dirtyNodes.addAll(chain.nodes());

        DetachContext detachContext = autoWireStrategy.detach(processNode, chainHead, chainTail);

        autoWireStrategy.spliceBetween(processNode, Collections.singletonList(leftBranch),
            chainHead, chainTail, EndOwnerManager.BRANCH_END_ID);

        String downstreamAfterGateway = detachContext.getOriginalNext();
        if (StringUtils.isBlank(downstreamAfterGateway)) {
            gateway.setNextId(EndOwnerManager.BRANCH_END_ID);
        } else if (isEndOwner(downstreamAfterGateway)) {
            gateway.setNextId(EndOwnerManager.END_OWNER_ID);
        } else {
            gateway.setNextId(downstreamAfterGateway);
            BaseNodeCanvas nextNode = processNode.getFlowNodeMap().get(downstreamAfterGateway);
            if (nextNode instanceof IRoutable routableNext) {
                routableNext.setPrveId(gateway.getId());
                dirtyNodes.add(nextNode);
            }
        }

        if (chainHead instanceof IRoutable headRoutable) {
            headRoutable.setPrveId(leftBranch.getId());
        }

        dirtyNodes.addAll(detachContext.getPrevNodes());
        log.debug("左侧放置：移动链段 {} -> {} 到分支，网关 {} 下游={}",
            chainHead.getId(), chainTail.getId(), gateway.getId(), gateway.getNextId());
        return dirtyNodes;
    }

    private Set<BaseNodeCanvas> handleNoMove(GatewayNodeCanvas gateway,
        ConditionNodeCanvas leftBranch, ConditionNodeCanvas rightBranch) {
        Set<BaseNodeCanvas> dirtyNodes = new LinkedHashSet<>();
        leftBranch.setNextId(EndOwnerManager.BRANCH_END_ID);
        rightBranch.setNextId(EndOwnerManager.BRANCH_END_ID);
        dirtyNodes.add(leftBranch);
        dirtyNodes.add(rightBranch);
        dirtyNodes.add(gateway);
        log.debug("不移动策略：保持原主链，两个分支为空");
        return dirtyNodes;
    }

    /**
     * 删除网关
     *
     * @param gatewayId 网关ID
     */
    public void deleteGateway(String gatewayId) {
        ValidParamsUtil.validateGatewayOperationComplete(nodeBatchExecutor, gatewayId, null);

        ProcessNode workingCopy = nodeBatchExecutor.getWorkingCopy();
        GatewayNodeCanvas gateway = ValidParamsUtil.requireGatewayExists(workingCopy, gatewayId);

        List<String> flowIds = gateway.getFlowIds();
        if (flowIds == null || flowIds.isEmpty()) {
            throw new IllegalStateException("网关没有分支，无法删除");
        }

        if (flowIds.size() > 1) {
            throw new IllegalStateException("网关有多个分支，请先删除分支至只剩1条");
        }

        String branchLeafId = flowIds.get(0);
        BaseNodeCanvas branchLeafNode = workingCopy.getFlowNodeMap().get(branchLeafId);
        ConditionNodeCanvas branchLeaf = branchLeafNode instanceof ConditionNodeCanvas
            ? (ConditionNodeCanvas) branchLeafNode : null;

        flattenGateway(workingCopy, gateway, branchLeaf);

        if (branchLeafNode != null) {
            nodeBatchExecutor.deleteNode(branchLeafNode.getId());
        }
        nodeBatchExecutor.deleteNode(gatewayId);

        log.debug("删除网关 {}，已完成扁平化", gatewayId);
    }

    private void flattenGateway(ProcessNode processNode, GatewayNodeCanvas gateway,
        ConditionNodeCanvas branchLeaf) {
        if (branchLeaf == null || StringUtils.isBlank(branchLeaf.getNextId())) {
            flattenEmptyBranch(processNode, gateway);
            return;
        }

        flattenWithBranch(processNode, gateway, branchLeaf, branchLeaf.getNextId());
    }

    private void flattenEmptyBranch(ProcessNode processNode, GatewayNodeCanvas gateway) {
        AutoWireStrategy.DeleteNodeContext deleteContext = autoWireStrategy.deleteNode(processNode, gateway);

        for (BaseNodeCanvas prevNode : deleteContext.getPrevNodes()) {
            nodeBatchExecutor.updateNode(prevNode.getId(), prevNode);
        }

        if (deleteContext.getNewEndOwner() != null) {
            nodeBatchExecutor.updateNode(deleteContext.getNewEndOwner().getId(), deleteContext.getNewEndOwner());
        }

        String nextId = deleteContext.getNextId();
        if (!StringUtils.isBlank(nextId) && !isEndOwner(nextId)) {
            BaseNodeCanvas nextNode = processNode.getFlowNodeMap().get(nextId);
            if (nextNode != null) {
                nodeBatchExecutor.updateNode(nextId, nextNode);
            }
        }

        log.debug("扁平化空分支网关 {}，前驱数量 {}", gateway.getId(), deleteContext.getPrevNodes().size());
    }

    private void flattenWithBranch(ProcessNode processNode, GatewayNodeCanvas gateway,
        ConditionNodeCanvas branchLeaf, String branchHeadId) {
        LinearChain branchChain = WorkflowQueryService.collectLinearChain(processNode, branchHeadId);
        if (branchChain.isEmpty()) {
            log.warn("扁平化网关 {} 时分支链为空，回退到空分支处理", gateway.getId());
            flattenEmptyBranch(processNode, gateway);
            return;
        }

        BaseNodeCanvas branchHead = branchChain.head();
        BaseNodeCanvas branchTail = branchChain.tail();
        String gatewayNextId = gateway.getNextId();

        List<BaseNodeCanvas> prevNodes = WorkflowQueryService.findPrevNodes(processNode, gateway.getId());

        autoWireStrategy.replace(processNode, gateway, gateway, branchHead, branchTail);

        if (branchHead instanceof IRoutable headRoutable) {
            if (!prevNodes.isEmpty()) {
                headRoutable.setPrveId(prevNodes.get(prevNodes.size() - 1).getId());
            } else {
                headRoutable.setPrveId(null);
            }
        }
        branchLeaf.setNextId(EndOwnerManager.BRANCH_END_ID);

        Set<BaseNodeCanvas> dirtyNodes = new LinkedHashSet<>();
        dirtyNodes.addAll(prevNodes);
        dirtyNodes.addAll(branchChain.nodes());

        if (!StringUtils.isBlank(gatewayNextId) && !isEndOwner(gatewayNextId)) {
            BaseNodeCanvas nextNode = processNode.getFlowNodeMap().get(gatewayNextId);
            if (nextNode != null) {
                dirtyNodes.add(nextNode);
            }
        }

        registerUpdates(dirtyNodes);
        log.debug("扁平化网关 {} 成功，分支头 {} 接入主链", gateway.getId(), branchHead.getId());
    }

    private void registerUpdates(Set<BaseNodeCanvas> nodes) {
        for (BaseNodeCanvas node : nodes) {
            if (node != null) {
                nodeBatchExecutor.updateNode(node.getId(), node);
            }
        }
    }

    private boolean isEndOwner(String value) {
        return EndOwnerManager.END_OWNER_ID.equals(value);
    }

    /**
     * 修改网关类型
     *
     * @param gatewayId 网关ID
     * @param toType 目标类型
     */
    public void switchGatewayType(String gatewayId, Integer toType) {
        ValidParamsUtil.validateGatewayOperationComplete(nodeBatchExecutor, gatewayId, toType);

        ProcessNode workingCopy = nodeBatchExecutor.getWorkingCopy();
        GatewayNodeCanvas gateway = ValidParamsUtil.requireGatewayExists(workingCopy, gatewayId);

        GatewaySemanticsStrategyUtil.switchGatewayType(workingCopy, gateway, toType);

        nodeBatchExecutor.updateNode(gatewayId, gateway);

        List<String> flowIds = gateway.getFlowIds();
        if (flowIds != null) {
            for (String flowId : flowIds) {
                BaseNodeCanvas branchNode = workingCopy.getFlowNodeMap().get(flowId);
                if (branchNode != null) {
                    nodeBatchExecutor.updateNode(flowId, branchNode);
                    log.debug("更新分支节点: {}", flowId);
                }
            }
        }

        log.debug("网关 {} 类型已切换为 {}", gatewayId, toType);
    }

    private void setDefaultConditionForBranch(ConditionNodeCanvas branch, int index) {
        if (branch.getOperateCondition() == null || branch.getOperateCondition().isEmpty()) {
            List<List<ConditionGroup>> defaultConditions = new ArrayList<>();
            List<ConditionGroup> conditionGroup = new ArrayList<>();

            ConditionGroup condition = new ConditionGroup();
            condition.setNodeId("system");
            condition.setNodeName("系统");

            if (index == 2 - 1) {
                condition.setFiledId("else");
                condition.setFiledValue("其他情况");
                condition.setConditionId("default_else");
                condition.setValue("else");
            } else {
                condition.setFiledId("condition_" + (index + 1));
                condition.setFiledValue("始终成立");
                condition.setConditionId("default_condition_" + (index + 1));
                condition.setValue("true");
            }

            conditionGroup.add(condition);
            defaultConditions.add(conditionGroup);
            branch.setOperateCondition(defaultConditions);

            log.debug("为分支 {} 生成默认条件: {}", branch.getId(), condition.getFiledId());
        }
    }
}
