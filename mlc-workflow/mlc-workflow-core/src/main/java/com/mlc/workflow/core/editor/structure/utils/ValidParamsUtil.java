package com.mlc.workflow.core.editor.structure.utils;

import com.mlc.workflow.core.editor.model.BaseNodeCanvas;
import com.mlc.workflow.core.editor.model.canvas.GatewayNodeCanvas;
import com.mlc.workflow.core.editor.model.canvas.capability.IRoutable;
import com.mlc.workflow.core.editor.runtime.beans.ProcessNode;
import com.mlc.workflow.core.editor.structure.executor.NodeBatchExecutor;
import com.mlc.workflow.core.editor.structure.service.WorkflowQueryService;
import java.util.HashSet;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * 参数校验工具类
 * 统一参数校验逻辑，减少重复代码
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class ValidParamsUtil {

    /**
     * 校验ProcessNode不为空
     * @param processNode 流程节点
     * @param paramName 参数名称
     */
    public static void requireNonNull(ProcessNode processNode, String paramName) {
        if (processNode == null) {
            throw new IllegalArgumentException(paramName + "不能为空");
        }
    }

    /**
     * 校验BaseNodeCanvas不为空
     * @param node 节点
     * @param paramName 参数名称
     */
    public static void requireNonNull(BaseNodeCanvas node, String paramName) {
        if (node == null) {
            throw new IllegalArgumentException(paramName + "不能为空");
        }
    }

    /**
     * 校验字符串不为空
     * @param str 字符串
     * @param paramName 参数名称
     */
    public static void requireNonEmpty(String str, String paramName) {
        if (str == null || str.trim().isEmpty()) {
            throw new IllegalArgumentException(paramName + "不能为空");
        }
    }

    /**
     * 校验列表不为空
     * @param list 列表
     * @param paramName 参数名称
     */
    public static void requireNonNull(List<?> list, String paramName) {
        if (list == null) {
            throw new IllegalArgumentException(paramName + "不能为空");
        }
    }

    /**
     * 校验节点是否可路由
     * @param node 节点
     * @param paramName 参数名称
     * @return 可路由节点
     */
    public static IRoutable requireRoutable(BaseNodeCanvas node, String paramName) {
        requireNonNull(node, paramName);
        if (!(node instanceof IRoutable)) {
            throw new IllegalArgumentException(paramName + "必须是可路由的");
        }
        return (IRoutable) node;
    }

    /**
     * 校验节点在流程中存在
     * @param processNode 流程节点
     * @param nodeId 节点ID
     * @param paramName 参数名称
     * @return 找到的节点
     */
    public static BaseNodeCanvas requireNodeExists(ProcessNode processNode, String nodeId, String paramName) {
        requireNonNull(processNode, "processNode");
        requireNonEmpty(nodeId, "nodeId");
        
        BaseNodeCanvas node = processNode.getFlowNodeMap().get(nodeId);
        if (node == null) {
            throw new IllegalArgumentException("找不到" + paramName + "节点: " + nodeId);
        }
        return node;
    }

    /**
     * 校验AutoWireStrategy的基本参数
     * @param processNode 流程节点
     * @param head 头节点
     * @param tail 尾节点
     */
    public static void validateAutoWireParams(ProcessNode processNode, BaseNodeCanvas head, BaseNodeCanvas tail) {
        requireNonNull(processNode, "processNode");
        requireNonNull(head, "head");
        requireNonNull(tail, "tail");
    }

    /**
     * 校验网关操作的基本参数
     * @param nodeId 节点ID
     * @param paramName 参数名称
     */
    public static void validateGatewayOperationParams(String nodeId, String paramName) {
        requireNonEmpty(nodeId, paramName);
    }

    /**
     * 校验网关类型
     * @param gatewayType 网关类型
     */
    public static void validateGatewayType(Integer gatewayType) {
        if (gatewayType == null) {
            throw new IllegalArgumentException("网关类型不能为空");
        }
        if (!gatewayType.equals(GatewaySemanticsStrategyUtil.GATEWAY_TYPE_PARALLEL) &&
            !gatewayType.equals(GatewaySemanticsStrategyUtil.GATEWAY_TYPE_EXCLUSIVE)) {
            throw new IllegalArgumentException("不支持的网关类型: " + gatewayType);
        }
    }


    /**
     * 校验批量执行器状态
     * @param executor 批量执行器
     */
    public static void validateExecutorState(NodeBatchExecutor executor) {
        if (executor == null) {
            throw new IllegalStateException("批量执行器未初始化");
        }
    }

    /**
     * 校验多个参数不为空（通用空值检查）
     * @param params 参数数组，格式为 [param1, "param1Name", param2, "param2Name", ...]
     */
    public static void requireAllNonNull(Object... params) {
        if (params.length % 2 != 0) {
            throw new IllegalArgumentException("参数必须成对出现：值和名称");
        }
        
        for (int i = 0; i < params.length; i += 2) {
            Object value = params[i];
            String name = (String) params[i + 1];
            if (value == null) {
                throw new IllegalArgumentException(name + "不能为空");
            }
        }
    }

    /**
     * 校验并查找网关节点
     * @param processNode 流程节点
     * @param gatewayId 网关ID
     * @return 网关节点
     */
    public static GatewayNodeCanvas requireGatewayExists(ProcessNode processNode, String gatewayId) {
        requireNonNull(processNode, "processNode");
        requireNonEmpty(gatewayId, "gatewayId");
        
        GatewayNodeCanvas gateway = WorkflowQueryService.findGateway(processNode, gatewayId);
        if (gateway == null) {
            throw new IllegalArgumentException("找不到网关节点: " + gatewayId);
        }
        return gateway;
    }

    /**
     * 校验并要求节点为特定类型
     * @param node 节点
     * @param expectedType 期望类型
     * @param nodeId 节点ID（用于错误信息）
     * @return 转换后的节点
     */
    @SuppressWarnings("unchecked")
    public static <T extends BaseNodeCanvas> T requireNodeType(BaseNodeCanvas node, Class<T> expectedType, String nodeId) {
        requireNonNull(node, "node");
        if (!expectedType.isInstance(node)) {
            throw new IllegalArgumentException("节点 " + nodeId + " 类型错误，期望: " + expectedType.getSimpleName() + "，实际: " + node.getClass().getSimpleName());
        }
        return (T) node;
    }

    /**
     * 校验分支叶子节点属于指定网关
     * @param gateway 网关节点
     * @param branchLeafId 分支叶子ID
     */
    public static void requireBranchBelongsToGateway(GatewayNodeCanvas gateway, String branchLeafId) {
        requireNonNull(gateway, "gateway");
        requireNonEmpty(branchLeafId, "branchLeafId");
        
        List<String> flowIds = gateway.getFlowIds();
        if (flowIds == null || !flowIds.contains(branchLeafId)) {
            throw new IllegalArgumentException("分支叶子 " + branchLeafId + " 不属于网关 " + gateway.getId());
        }
    }

    /**
     * 校验列表顺序的有效性（用于分支重排序）
     * @param currentOrder 当前顺序
     * @param newOrder 新顺序
     */
    public static void validateOrderValidity(List<String> currentOrder, List<String> newOrder) {
        requireNonNull(currentOrder, "currentOrder");
        requireNonNull(newOrder, "newOrder");
        
        if (newOrder.size() != currentOrder.size()) {
            throw new IllegalArgumentException("新顺序长度与当前顺序不匹配");
        }
        
        if (!new HashSet<>(newOrder).containsAll(currentOrder) || !new HashSet<>(currentOrder).containsAll(newOrder)) {
            throw new IllegalArgumentException("新顺序包含无效元素");
        }
    }

    /**
     * 校验分支操作的完整参数
     * @param executor 批量执行器
     * @param gatewayId 网关ID
     * @param branchLeafId 分支叶子ID（可选）
     */
    public static void validateBranchOperationComplete(NodeBatchExecutor executor, String gatewayId, String branchLeafId) {
        validateExecutorState(executor);
        requireNonEmpty(gatewayId, "gatewayId");
        if (branchLeafId != null) {
            requireNonEmpty(branchLeafId, "branchLeafId");
        }
    }

    /**
     * 校验节点操作的完整参数
     * @param executor 批量执行器
     * @param nodeId 节点ID
     * @param node 节点对象（可选）
     */
    public static void validateNodeOperationComplete(NodeBatchExecutor executor, String nodeId, BaseNodeCanvas node) {
        validateExecutorState(executor);
        requireNonEmpty(nodeId, "nodeId");
        if (node != null) {
            requireNonNull(node, "node");
        }
    }

    /**
     * 校验网关操作的完整参数
     * @param executor 批量执行器
     * @param nodeId 节点ID
     * @param gatewayType 网关类型（可选）
     */
    public static void validateGatewayOperationComplete(NodeBatchExecutor executor, String nodeId, Integer gatewayType) {
        validateExecutorState(executor);
        validateGatewayOperationParams(nodeId, "nodeId");
        if (gatewayType != null) {
            validateGatewayType(gatewayType);
        }
    }

    /**
     * 校验更新操作参数
     * @param updates 更新映射
     */
    public static void validateUpdateParams(Map<String, Object> updates) {
        if (updates == null) {
            throw new IllegalArgumentException("updates不能为空");
        }
        if (updates.isEmpty()) {
            throw new IllegalArgumentException("更新参数不能为空");
        }
    }

    /**
     * 校验位置参数的有效性
     * @param position 位置
     * @param maxSize 最大大小
     * @return 规范化的位置（-1转换为maxSize）
     */
    public static int validateAndNormalizePosition(int position, int maxSize) {
        if (position < -1) {
            throw new IllegalArgumentException("位置不能小于-1");
        }
        return position == -1 ? maxSize : Math.min(position, maxSize);
    }
}
