package com.mlc.workflow.core.editor.model.canvas;

import com.mlc.base.common.enums.workflow.NodeTypeEnum;
import com.mlc.workflow.core.editor.model.BaseNodeCanvas;
import com.mlc.workflow.core.editor.model.visitor.canvas.ICanvasNodeVisitor;
import io.nop.api.core.annotations.data.DataBean;
import lombok.Getter;
import lombok.Setter;

/**
 * 界面推送任务节点
 * 对应NodeTypeEnum.PUSH (17)
 */
@Getter
@Setter
@DataBean
public class PushNodeCanvas extends BaseNodeCanvas {

    public PushNodeCanvas() {
        this.setTypeId(NodeTypeEnum.PUSH.getValue());
        this.setName("界面推送任务");
    }
    
    @Override
    public void accept(ICanvasNodeVisitor visitor) {
        visitor.visit(this);
    }
}
