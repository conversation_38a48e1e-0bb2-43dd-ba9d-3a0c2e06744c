package com.mlc.workflow.core.editor.model.properties;

import com.mlc.base.common.enums.workflow.NodeTypeEnum;
import com.mlc.workflow.core.editor.model.BaseNodeProperties;
import com.mlc.workflow.core.editor.model.visitor.properties.INodeVisitor;
import java.util.List;
import java.util.Map;
import lombok.Getter;
import lombok.Setter;

/**
 * 审批节点 (typeId=4)
 * 用于审批任务
 */
@Getter
@Setter
public class ApprovalNodeProperties extends BaseNodeProperties {

    public ApprovalNodeProperties() {
        this.setFlowNodeType(NodeTypeEnum.APPROVAL.getValue());
        this.setName("审批任务");
    }

    /**
     * 多级类型
     */
    private Integer multipleLevelType;

    /**
     * 多级
     */
    private Integer multipleLevel;

    /**
     * 账户列表
     */
    private List<String> accounts;

    /**
     * 会签类型
     * multipleLevelType !== 0 && countersignType === 0 ? 1 : countersignType,
     */
    private Integer countersignType;

    /**
     * 条件
     */
    private String condition;

    /**
     * 操作类型列表
     */
    private List<String> operationTypeList;

    /**
     * 忽略必填
     */
    private Boolean ignoreRequired;

    /**
     * 是否回调
     */
    private Boolean isCallBack;

    /**
     * 回调类型
     */
    private Integer callBackType;

    /**
     * 回调多级
     */
    private Integer callBackMultipleLevel;

    /**
     * 表单属性
     */
    private Map<String, Object> formProperties;

    /**
     * 通过按钮名称
     */
    private String passBtnName;

    /**
     * 驳回按钮名称
     */
    private String overruleBtnName;

    /**
     * 退回按钮名称
     */
    private String returnBtnName;

    /**
     * 权限
     */
    private String auth;

    /**
     * 批量审批
     */
    private Boolean batchApprove;

    /**
     * 快速审批
     */
    private Boolean fastApprove;

    /**
     * 允许上传附件
     */
    private Boolean allowUploadAttachment;

    /**
     * 计划
     */
    private String schedule;

    /**
     * 通过发送消息
     */
    private Boolean passSendMessage;

    /**
     * 通过消息
     */
    private String passMessage;

    /**
     * 驳回发送消息
     */
    private Boolean overruleSendMessage;

    /**
     * 驳回消息
     */
    private String overruleMessage;

    /**
     * 回调节点类型
     */
    private Integer callBackNodeType;

    /**
     * 回调节点ID列表
     */
    private List<String> callBackNodeIds;

    /**
     * 加密
     */
    private Boolean encrypt;

    /**
     * 操作用户范围
     */
    private String operationUserRange;

    /**
     * 意见模板
     */
    private String opinionTemplate;

    /**
     * 流程节点映射
     */
    private Map<String, Object> flowNodeMap;

    /**
     * 用户任务空映射
     */
    private Map<String, Object> userTaskNullMap;

    /**
     * 候选用户映射
     */
    private Map<String, Object> candidateUserMap;

    /**
     * 添加不允许查看
     */
    private Boolean addNotAllowView;

    /**
     * 签名操作类型
     */
    private Integer signOperationType;

    /**
     * 说明
     */
    private String explain;
    
    @Override
    public void accept(INodeVisitor visitor) {
        visitor.visit(this);
    }
}
