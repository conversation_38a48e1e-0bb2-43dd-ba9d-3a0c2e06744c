package com.mlc.workflow.core.editor.model.properties;

import com.mlc.base.common.enums.workflow.NodeTypeEnum;
import com.mlc.workflow.core.editor.model.BaseNodeProperties;
import com.mlc.workflow.core.editor.model.visitor.properties.INodeVisitor;
import lombok.Getter;
import lombok.Setter;

/**
 * 调用已集成 API 任务节点
 * 对应NodeTypeEnum.API (25)
 */
@Getter
@Setter
public class ApiNodeProperties extends BaseNodeProperties {

    public ApiNodeProperties() {
        this.setFlowNodeType(NodeTypeEnum.API.getValue());
        this.setName("调用已集成 API 任务");
    }

    @Override
    public void accept(INodeVisitor visitor) {
        visitor.visit(this);
    }
}
