package com.mlc.workflow.core.editor;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.mlc.workflow.core.editor.model.canvas.StartEventNodeCanvas;
import com.mlc.base.core.serialization.JacksonProcessorModule;
import com.mlc.base.core.serialization.generic.ProcessorRegistry;
import com.mlc.base.core.serialization.strategies.PropMetaValidationStrategy;
import com.mlc.workflow.core.editor.model.canvas.startevent.SheetEventTriggerCanvas;
import io.nop.api.core.annotations.meta.PropMeta;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;

/**
 * 通用注解处理框架使用示例
 */
@Slf4j
public class AnnotationUsageTest {

    private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper();

    @BeforeAll
    public static void setup() {
        ProcessorRegistry registry = new ProcessorRegistry();
        registry.registerProcessor(PropMeta.class, new PropMetaValidationStrategy());
        OBJECT_MAPPER.registerModule(new JacksonProcessorModule(registry));
    }

    /**
     * 示例1：使用默认配置
     */
    @Test
    public void example1() {

        try {
            // 创建自定义注册表

            StartEventNodeCanvas node = new SheetEventTriggerCanvas();
            node.setTriggerNodeId("1");
            node.setTriggerName("测试节点");
            node.setAppType(1);

            String json = OBJECT_MAPPER.writeValueAsString(node);
            log.info("序列化结果1: {}", json);
            assert json != null;
            
        } catch (Exception e) {
            log.error("示例1执行失败", e);
        }
    }
    
    /**
     * 示例2：自定义注解处理器
     */
    @Test
    public void example2() {

        try {
            TestEntity entity = new TestEntity();
            entity.setMandatoryField("111");

            String json = OBJECT_MAPPER.writeValueAsString(entity);
            log.info("序列化结果2: {}", json);
            assert json != null;

        } catch (Exception e) {
            log.error("示例2执行失败", e);
        }
    }



    @Target(ElementType.FIELD)
    @Retention(RetentionPolicy.RUNTIME)
    public @interface AutoGenerate {
        String prefix() default "auto";
        boolean useTimestamp() default true;
        int length() default 0;
    }

    @Setter
    @Getter
    public static class TestEntity {
        @PropMeta(mandatory = true)
        private String mandatoryField;

        @AutoGenerate(prefix = "GEN", useTimestamp = true)
        private String generatedId;
    }
}



