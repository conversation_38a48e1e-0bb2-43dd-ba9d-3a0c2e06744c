package com.mlc.workflow.core.editor.structure;

import com.mlc.workflow.core.editor.model.canvas.ApprovalNodeCanvas;
import com.mlc.workflow.core.editor.model.canvas.GatewayNodeCanvas;
import com.mlc.workflow.core.editor.model.canvas.StartEventNodeCanvas;
import com.mlc.workflow.core.editor.model.canvas.startevent.SheetEventTriggerCanvas;
import com.mlc.workflow.core.editor.runtime.beans.ProcessNode;
import com.mlc.workflow.core.editor.structure.utils.WorkflowValidatorUtil;
import com.mlc.base.common.utils.ValidationCollector;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.ArrayList;
import java.util.HashMap;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 工作流验证器测试
 * 验证各种流程结构的校验功能
 */
public class WorkflowValidatorTest {
    
    private WorkflowValidatorUtil validator;

    @BeforeEach
    void setUp() {
        validator = new WorkflowValidatorUtil();
    }
    
    @Test
    void testValidWorkflow() {
        // 测试有效的工作流
        ProcessNode validWorkflow = createValidWorkflow();

        ValidationCollector collector = new ValidationCollector();
        validator.validate(validWorkflow, collector);
        
        assertTrue(collector.isValid(), "有效工作流应该通过验证");
        assertTrue(collector.getErrors().isEmpty(), "有效工作流不应该有错误");
    }
    
    @Test
    void testNullProcessNode() {
        // 测试空流程节点
        ValidationCollector result = new ValidationCollector();
        validator.validate(null, result);
        
        assertFalse(result.isValid());
        assertEquals(1, result.getErrors().size());
        assertTrue(result.getErrors().get(0).contains("流程节点不能为空"));
    }
    
    @Test
    void testMissingStartEvent() {
        // 测试缺少开始事件,不设置startEventId
        ProcessNode processNode = new ProcessNode();
        processNode.setId("test-process");
        processNode.setFlowNodeMap(new HashMap<>());

        ValidationCollector result = new ValidationCollector();
        validator.validate(processNode, result);

        assertFalse(result.isValid());
        assertTrue(result.getErrors().stream().anyMatch(error ->
            error.contains("流程缺少开始事件ID")));
    }

    @Test
    void testEmptyFlowNodeMap() {
        // 测试空的节点映射
        ProcessNode processNode = new ProcessNode();
        processNode.setId("test-process");
        processNode.setStartEventId("start-1");
        processNode.setFlowNodeMap(new HashMap<>());

        ValidationCollector result = new ValidationCollector();
        validator.validate(processNode, result);
        
        assertFalse(result.isValid());
        assertTrue(result.getErrors().stream().anyMatch(error -> 
            error.contains("流程节点映射为空")));
    }
    
    @Test
    void testStartEventNotFound() {
        // 测试开始事件节点不存在
        ProcessNode processNode = new ProcessNode();
        processNode.setId("test-process");
        processNode.setStartEventId("start-1");
        processNode.setFlowNodeMap(new HashMap<>());
        
        // 添加其他节点但不添加开始事件
        ApprovalNodeCanvas approval = new ApprovalNodeCanvas();
        approval.setId("approval-1");
        approval.setName("审批");
        approval.setNextId("99");
        processNode.getFlowNodeMap().put("approval-1", approval);

        ValidationCollector result = new ValidationCollector();
        validator.validate(processNode, result);
        
        assertFalse(result.isValid());
        assertTrue(result.getErrors().stream().anyMatch(error -> 
            error.contains("找不到开始事件节点")));
    }
    
    @Test
    void testWrongStartEventType() {
        // 测试开始事件节点类型错误
        ProcessNode processNode = new ProcessNode();
        processNode.setId("test-process");
        processNode.setStartEventId("start-1");
        processNode.setFlowNodeMap(new HashMap<>());
        
        // 用审批节点作为开始事件
        ApprovalNodeCanvas wrongStart = new ApprovalNodeCanvas();
        wrongStart.setId("start-1");
        wrongStart.setName("错误的开始");
        wrongStart.setNextId("99");
        processNode.getFlowNodeMap().put("start-1", wrongStart);

        ValidationCollector result = new ValidationCollector();
        validator.validate(processNode, result);
        
        assertFalse(result.isValid());
        assertTrue(result.getErrors().stream().anyMatch(error -> 
            error.contains("开始事件节点类型错误")));
    }
    
    @Test
    void testNoEndOwner() {
        // 测试缺少EndOwner
        ProcessNode processNode = new ProcessNode();
        processNode.setId("test-process");
        processNode.setStartEventId("start-1");
        processNode.setFlowNodeMap(new HashMap<>());
        
        StartEventNodeCanvas start = new SheetEventTriggerCanvas();
        start.setId("start-1");
        start.setName("开始");
        start.setNextId("approval-1");
        
        ApprovalNodeCanvas approval = new ApprovalNodeCanvas();
        approval.setId("approval-1");
        approval.setName("审批");
        approval.setPrveId("start-1");
        // 不设置nextId为99
        
        processNode.getFlowNodeMap().put("start-1", start);
        processNode.getFlowNodeMap().put("approval-1", approval);

        ValidationCollector result = new ValidationCollector();
        validator.validate(processNode, result);
        
        assertFalse(result.isValid());
        assertTrue(result.getErrors().stream().anyMatch(error -> 
            error.contains("流程缺少EndOwner")));
    }
    
    @Test
    void testMultipleEndOwners() {
        // 测试多个EndOwner
        ProcessNode processNode = new ProcessNode();
        processNode.setId("test-process");
        processNode.setStartEventId("start-1");
        processNode.setFlowNodeMap(new HashMap<>());
        
        StartEventNodeCanvas start = new SheetEventTriggerCanvas();
        start.setId("start-1");
        start.setName("开始");
        start.setNextId("approval-1");
        
        ApprovalNodeCanvas approval1 = new ApprovalNodeCanvas();
        approval1.setId("approval-1");
        approval1.setName("审批1");
        approval1.setPrveId("start-1");
        approval1.setNextId("99"); // EndOwner 1
        
        ApprovalNodeCanvas approval2 = new ApprovalNodeCanvas();
        approval2.setId("approval-2");
        approval2.setName("审批2");
        approval2.setNextId("99"); // EndOwner 2
        
        processNode.getFlowNodeMap().put("start-1", start);
        processNode.getFlowNodeMap().put("approval-1", approval1);
        processNode.getFlowNodeMap().put("approval-2", approval2);

        ValidationCollector result = new ValidationCollector();
        validator.validate(processNode, result);
        
        assertFalse(result.isValid());
        assertTrue(result.getErrors().stream().anyMatch(error -> 
            error.contains("流程有多个EndOwner")));
    }
    
    @Test
    void testGatewayWithoutBranches() {
        // 测试没有分支的网关
        ProcessNode processNode = createValidWorkflow();
        
        // 添加一个没有分支的网关
        GatewayNodeCanvas emptyGateway = new GatewayNodeCanvas();
        emptyGateway.setId("gateway-empty");
        emptyGateway.setName("空网关");
        emptyGateway.setGatewayType(1);
        emptyGateway.setFlowIds(new ArrayList<>()); // 空分支列表
        
        processNode.getFlowNodeMap().put("gateway-empty", emptyGateway);

        ValidationCollector result = new ValidationCollector();
        validator.validate(processNode, result);
        
        // 打印实际的错误信息用于调试
        System.out.println("Gateway validation errors: " + result.getErrors());
        
        assertFalse(result.isValid());
        // 检查是否有网关相关的错误
        assertTrue(result.getErrors().stream().anyMatch(error -> 
            error.contains("网关") || error.contains("分支") || error.contains("gateway")));
    }
    
    @Test
    void testOrphanNode() {
        // 测试悬空节点
        ProcessNode processNode = createValidWorkflow();
        
        // 添加一个没有前驱的悬空节点
        ApprovalNodeCanvas orphan = new ApprovalNodeCanvas();
        orphan.setId("orphan-1");
        orphan.setName("悬空节点");
        orphan.setNextId("99");
        // 不设置任何前驱关系
        
        processNode.getFlowNodeMap().put("orphan-1", orphan);

        ValidationCollector result = new ValidationCollector();
        validator.validate(processNode, result);
        
        // 打印实际的错误信息用于调试
        System.out.println("Orphan node validation errors: " + result.getErrors());
        
        assertFalse(result.isValid());
        // 检查是否有悬空节点或多个EndOwner的错误
        assertTrue(result.getErrors().stream().anyMatch(error -> 
            error.contains("悬空") || error.contains("多个EndOwner") || error.contains("orphan")));
    }
    
    @Test
    void testInvalidNextIdReference() {
        // 测试无效的nextId引用
        ProcessNode processNode = new ProcessNode();
        processNode.setId("test-process");
        processNode.setStartEventId("start-1");
        processNode.setFlowNodeMap(new HashMap<>());
        
        StartEventNodeCanvas start = new SheetEventTriggerCanvas();
        start.setId("start-1");
        start.setName("开始");
        start.setNextId("non-existent-node"); // 指向不存在的节点
        
        processNode.getFlowNodeMap().put("start-1", start);

        ValidationCollector result = new ValidationCollector();
        validator.validate(processNode, result);
        
        // 打印实际的错误信息用于调试
        System.out.println("Invalid nextId validation errors: " + result.getErrors());
        
        assertFalse(result.isValid());
        // 检查是否有无效引用或EndOwner相关的错误
        assertTrue(result.getErrors().stream().anyMatch(error -> 
            error.contains("nextId") || error.contains("不存在") || error.contains("EndOwner")));
    }
    
    /**
     * 创建一个有效的工作流
     */
    private ProcessNode createValidWorkflow() {
        ProcessNode processNode = new ProcessNode();
        processNode.setId("valid-process");
        processNode.setStartEventId("start-1");
        processNode.setFlowNodeMap(new HashMap<>());
        
        // 开始事件
        StartEventNodeCanvas start = new SheetEventTriggerCanvas();
        start.setId("start-1");
        start.setName("开始");
        start.setNextId("approval-1");
        
        // 审批节点
        ApprovalNodeCanvas approval = new ApprovalNodeCanvas();
        approval.setId("approval-1");
        approval.setName("审批");
        approval.setPrveId("start-1");
        approval.setNextId("99");
        
        processNode.getFlowNodeMap().put("start-1", start);
        processNode.getFlowNodeMap().put("approval-1", approval);
        
        return processNode;
    }
}
