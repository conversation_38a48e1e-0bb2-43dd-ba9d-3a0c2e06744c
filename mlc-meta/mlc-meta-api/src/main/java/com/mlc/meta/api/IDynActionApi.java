package com.mlc.meta.api;

import io.nop.graphql.core.reflection.GraphQLBizModel;

public interface IDynActionApi {

    /**
     * 生成并重新加载模型
     * @param modelDefinitionId 模型定义ID
     */
    void generateAndReloadModel(String modelDefinitionId);

    /**
     * 根据业务对象名称获取GraphQL模型
     *
     * @param isView 是否为视图
     * @param bizObjName 业务对象名称
     */
    GraphQLBizModel getBizModel(Boolean isView, String bizObjName);
}
