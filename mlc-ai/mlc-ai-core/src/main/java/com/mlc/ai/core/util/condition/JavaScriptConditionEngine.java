package com.mlc.ai.core.util.condition;

import com.mlc.base.common.enums.ai.ConditionEngineType;
import lombok.extern.slf4j.Slf4j;

import javax.script.ScriptEngine;
import javax.script.ScriptEngineManager;
import javax.script.ScriptException;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * JavaScript条件表达式引擎
 * 支持复杂的JavaScript表达式评估
 */
@Slf4j
public class JavaScriptConditionEngine implements IConditionEngine {
    
    /**
     * 脚本引擎缓存，避免重复创建
     */
    private static final Map<String, ScriptEngine> ENGINE_CACHE = new ConcurrentHashMap<>();
    
    /**
     * 获取脚本引擎
     */
    private ScriptEngine getScriptEngine() {
        String threadName = Thread.currentThread().getName();
        return ENGINE_CACHE.computeIfAbsent(threadName, k -> {
            ScriptEngineManager manager = new ScriptEngineManager();
            ScriptEngine engine = manager.getEngineByName("nashorn");
            if (engine == null) {
                // 如果nashorn不可用，尝试使用graal.js
                engine = manager.getEngineByName("graal.js");
            }
            if (engine == null) {
                throw new RuntimeException("无法找到JavaScript脚本引擎");
            }
            return engine;
        });
    }
    
    @Override
    public boolean evaluate(String expression, Map<String, Object> context) throws ConditionEvaluationException {
        if (expression == null || expression.trim().isEmpty()) {
            throw new ConditionEvaluationException("条件表达式不能为空");
        }
        
        try {
            ScriptEngine engine = getScriptEngine();
            
            // 清除之前的变量
            engine.getBindings(javax.script.ScriptContext.ENGINE_SCOPE).clear();
            
            // 注入上下文变量
            for (Map.Entry<String, Object> entry : context.entrySet()) {
                engine.put(entry.getKey(), entry.getValue());
            }
            
            // 添加常用的工具函数
            addUtilityFunctions(engine);
            
            // 评估表达式
            Object result = engine.eval(expression);
            
            // 转换结果为布尔值
            return convertToBoolean(result);
            
        } catch (ScriptException e) {
            log.error("JavaScript表达式评估失败: {}", expression, e);
            throw new ConditionEvaluationException("JavaScript表达式评估失败: " + e.getMessage(), e);
        } catch (Exception e) {
            log.error("条件评估过程中发生未知错误: {}", expression, e);
            throw new ConditionEvaluationException("条件评估失败: " + e.getMessage(), e);
        }
    }
    
    @Override
    public ConditionEngineType getEngineType() {
        return ConditionEngineType.JAVASCRIPT;
    }
    
    @Override
    public ValidationResult validateExpression(String expression) {
        if (expression == null || expression.trim().isEmpty()) {
            return ValidationResult.invalid("表达式不能为空");
        }
        
        try {
            ScriptEngine engine = getScriptEngine();
            
            // 尝试编译表达式以检查语法
            engine.eval("function __validate() { return " + expression + "; }");
            
            return ValidationResult.valid();
        } catch (ScriptException e) {
            return ValidationResult.invalid("JavaScript语法错误: " + e.getMessage());
        } catch (Exception e) {
            return ValidationResult.invalid("表达式验证失败: " + e.getMessage());
        }
    }
    
    /**
     * 添加常用的工具函数到脚本引擎
     */
    private void addUtilityFunctions(ScriptEngine engine) throws ScriptException {
        // 添加字符串工具函数
        engine.eval("""
            function isEmpty(str) {
                return str == null || str === '';
            }
            
            function isNotEmpty(str) {
                return !isEmpty(str);
            }
            
            function contains(str, substr) {
                return str != null && str.indexOf(substr) !== -1;
            }
            
            function startsWith(str, prefix) {
                return str != null && str.indexOf(prefix) === 0;
            }
            
            function endsWith(str, suffix) {
                return str != null && str.lastIndexOf(suffix) === str.length - suffix.length;
            }
            
            // 数组工具函数
            function inArray(value, array) {
                return array != null && array.indexOf(value) !== -1;
            }
            
            function arraySize(array) {
                return array != null ? array.length : 0;
            }
            
            // 数值工具函数
            function between(value, min, max) {
                return value >= min && value <= max;
            }
            
            function isNumber(value) {
                return typeof value === 'number' && !isNaN(value);
            }
            
            // 日期工具函数
            function now() {
                return new Date().getTime();
            }
            
            function daysBetween(date1, date2) {
                return Math.abs(date1 - date2) / (1000 * 60 * 60 * 24);
            }
            """);
    }
    
    /**
     * 将结果转换为布尔值
     */
    private boolean convertToBoolean(Object result) {
        if (result == null) {
            return false;
        }
        
        if (result instanceof Boolean) {
            return (Boolean) result;
        }
        
        if (result instanceof Number) {
            return ((Number) result).doubleValue() != 0.0;
        }
        
        if (result instanceof String) {
            String str = (String) result;
            return !str.isEmpty() && !"false".equalsIgnoreCase(str) && !"0".equals(str);
        }
        
        // 其他类型默认为true（非null即为true）
        return true;
    }
} 