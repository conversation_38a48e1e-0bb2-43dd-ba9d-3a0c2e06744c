package com.mlc.ai.core.util.condition;

import com.mlc.base.common.enums.ai.ConditionEngineType;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 简单条件表达式引擎
 * 支持基本的比较操作，格式: variable operator value
 * 例如: count > 5, status == 'success', value <= 100
 */
@Slf4j
public class SimpleConditionEngine implements IConditionEngine {
    
    /**
     * 简单条件表达式的正则模式
     * 支持格式: variable operator value
     */
    private static final Pattern CONDITION_PATTERN = Pattern.compile(
        "^\\s*([a-zA-Z_][a-zA-Z0-9_.]*)\\s*(==|!=|>=|<=|>|<|contains|startsWith|endsWith|in)\\s*(.+)\\s*$"
    );
    
    /**
     * 数字模式
     */
    private static final Pattern NUMBER_PATTERN = Pattern.compile("^-?\\d+(\\.\\d+)?$");
    
    /**
     * 字符串模式（单引号或双引号包围）
     */
    private static final Pattern STRING_PATTERN = Pattern.compile("^['\"](.*)['\"]\s*$");
    
    /**
     * 数组模式（方括号包围，逗号分隔）
     */
    private static final Pattern ARRAY_PATTERN = Pattern.compile("^\\[(.*)\\]$");
    
    @Override
    public boolean evaluate(String expression, Map<String, Object> context) throws ConditionEvaluationException {
        if (expression == null || expression.trim().isEmpty()) {
            throw new ConditionEvaluationException("条件表达式不能为空");
        }
        
        // 支持多个条件用 && 或 || 连接
        if (expression.contains("&&")) {
            return evaluateAndConditions(expression, context);
        } else if (expression.contains("||")) {
            return evaluateOrConditions(expression, context);
        } else {
            return evaluateSingleCondition(expression.trim(), context);
        }
    }
    
    @Override
    public ConditionEngineType getEngineType() {
        return ConditionEngineType.SIMPLE;
    }
    
    @Override
    public ValidationResult validateExpression(String expression) {
        if (expression == null || expression.trim().isEmpty()) {
            return ValidationResult.invalid("表达式不能为空");
        }
        
        try {
            // 检查是否包含逻辑操作符
            if (expression.contains("&&") || expression.contains("||")) {
                return validateComplexExpression(expression);
            } else {
                return validateSingleExpression(expression.trim());
            }
        } catch (Exception e) {
            return ValidationResult.invalid("表达式验证失败: " + e.getMessage());
        }
    }
    
    /**
     * 评估AND条件（所有条件都必须为true）
     */
    private boolean evaluateAndConditions(String expression, Map<String, Object> context) 
            throws ConditionEvaluationException {
        String[] conditions = expression.split("&&");
        for (String condition : conditions) {
            if (!evaluateSingleCondition(condition.trim(), context)) {
                return false;
            }
        }
        return true;
    }
    
    /**
     * 评估OR条件（任一条件为true即可）
     */
    private boolean evaluateOrConditions(String expression, Map<String, Object> context) 
            throws ConditionEvaluationException {
        String[] conditions = expression.split("\\|\\|");
        for (String condition : conditions) {
            if (evaluateSingleCondition(condition.trim(), context)) {
                return true;
            }
        }
        return false;
    }
    
    /**
     * 评估单个条件
     */
    private boolean evaluateSingleCondition(String expression, Map<String, Object> context) 
            throws ConditionEvaluationException {
        Matcher matcher = CONDITION_PATTERN.matcher(expression);
        if (!matcher.matches()) {
            throw new ConditionEvaluationException("无效的条件表达式格式: " + expression);
        }
        
        String variable = matcher.group(1);
        String operator = matcher.group(2);
        String valueStr = matcher.group(3);
        
        // 获取变量值
        Object variableValue = getVariableValue(variable, context);
        
        // 解析期望值
        Object expectedValue = parseValue(valueStr);
        
        // 执行比较
        return performComparison(variableValue, operator, expectedValue);
    }
    
    /**
     * 获取变量值，支持嵌套属性访问
     */
    private Object getVariableValue(String variable, Map<String, Object> context) {
        if (variable.contains(".")) {
            // 支持嵌套属性访问，如 user.name
            String[] parts = variable.split("\\.");
            Object current = context.get(parts[0]);
            
            for (int i = 1; i < parts.length && current != null; i++) {
                if (current instanceof Map) {
                    current = ((Map<?, ?>) current).get(parts[i]);
                } else {
                    // 尝试通过反射获取属性值
                    current = getPropertyValue(current, parts[i]);
                }
            }
            return current;
        } else {
            return context.get(variable);
        }
    }
    
    /**
     * 通过反射获取对象属性值
     */
    private Object getPropertyValue(Object obj, String propertyName) {
        try {
            String getterName = "get" + Character.toUpperCase(propertyName.charAt(0)) + propertyName.substring(1);
            return obj.getClass().getMethod(getterName).invoke(obj);
        } catch (Exception e) {
            log.debug("无法获取属性值: {}.{}", obj.getClass().getSimpleName(), propertyName);
            return null;
        }
    }
    
    /**
     * 解析值
     */
    private Object parseValue(String valueStr) {
        valueStr = valueStr.trim();
        
        // 检查是否为字符串
        Matcher stringMatcher = STRING_PATTERN.matcher(valueStr);
        if (stringMatcher.matches()) {
            return stringMatcher.group(1);
        }
        
        // 检查是否为数组
        Matcher arrayMatcher = ARRAY_PATTERN.matcher(valueStr);
        if (arrayMatcher.matches()) {
            String arrayContent = arrayMatcher.group(1).trim();
            if (arrayContent.isEmpty()) {
                return new String[0];
            }
            return arrayContent.split("\\s*,\\s*");
        }
        
        // 检查是否为数字
        if (NUMBER_PATTERN.matcher(valueStr).matches()) {
            try {
                if (valueStr.contains(".")) {
                    return Double.parseDouble(valueStr);
                } else {
                    return Long.parseLong(valueStr);
                }
            } catch (NumberFormatException e) {
                // 如果解析失败，当作字符串处理
            }
        }
        
        // 检查布尔值
        if ("true".equalsIgnoreCase(valueStr)) {
            return true;
        } else if ("false".equalsIgnoreCase(valueStr)) {
            return false;
        }
        
        // 检查null
        if ("null".equalsIgnoreCase(valueStr)) {
            return null;
        }
        
        // 默认当作字符串处理
        return valueStr;
    }
    
    /**
     * 执行比较操作
     */
    private boolean performComparison(Object variableValue, String operator, Object expectedValue) {
        return switch (operator) {
            case "==" -> objectEquals(variableValue, expectedValue);
            case "!=" -> !objectEquals(variableValue, expectedValue);
            case ">" -> compareNumbers(variableValue, expectedValue) > 0;
            case ">=" -> compareNumbers(variableValue, expectedValue) >= 0;
            case "<" -> compareNumbers(variableValue, expectedValue) < 0;
            case "<=" -> compareNumbers(variableValue, expectedValue) <= 0;
            case "contains" -> stringContains(variableValue, expectedValue);
            case "startsWith" -> stringStartsWith(variableValue, expectedValue);
            case "endsWith" -> stringEndsWith(variableValue, expectedValue);
            case "in" -> valueInArray(variableValue, expectedValue);
            default -> throw new IllegalArgumentException("不支持的操作符: " + operator);
        };
    }
    
    /**
     * 对象相等比较
     */
    private boolean objectEquals(Object a, Object b) {
        if (a == null && b == null) return true;
        if (a == null || b == null) return false;
        
        // 如果都是数字，进行数值比较
        if (isNumber(a) && isNumber(b)) {
            return compareNumbers(a, b) == 0;
        }
        
        return a.toString().equals(b.toString());
    }
    
    /**
     * 数字比较
     */
    private int compareNumbers(Object a, Object b) {
        if (!isNumber(a) || !isNumber(b)) {
            throw new IllegalArgumentException("无法比较非数字值: " + a + " 和 " + b);
        }
        
        BigDecimal numA = new BigDecimal(a.toString());
        BigDecimal numB = new BigDecimal(b.toString());
        return numA.compareTo(numB);
    }
    
    /**
     * 字符串包含检查
     */
    private boolean stringContains(Object str, Object substr) {
        if (str == null || substr == null) return false;
        return str.toString().contains(substr.toString());
    }
    
    /**
     * 字符串开头检查
     */
    private boolean stringStartsWith(Object str, Object prefix) {
        if (str == null || prefix == null) return false;
        return str.toString().startsWith(prefix.toString());
    }
    
    /**
     * 字符串结尾检查
     */
    private boolean stringEndsWith(Object str, Object suffix) {
        if (str == null || suffix == null) return false;
        return str.toString().endsWith(suffix.toString());
    }
    
    /**
     * 值在数组中检查
     */
    private boolean valueInArray(Object value, Object array) {
        if (value == null || array == null) return false;
        
        if (array instanceof String[] arr) {
            String valueStr = value.toString();
            for (String item : arr) {
                if (valueStr.equals(item.trim())) {
                    return true;
                }
            }
        }
        return false;
    }
    
    /**
     * 检查是否为数字
     */
    private boolean isNumber(Object obj) {
        if (obj == null) return false;
        return obj instanceof Number || NUMBER_PATTERN.matcher(obj.toString()).matches();
    }
    
    /**
     * 验证复杂表达式
     */
    private ValidationResult validateComplexExpression(String expression) {
        String[] parts;
        if (expression.contains("&&")) {
            parts = expression.split("&&");
        } else {
            parts = expression.split("\\|\\|");
        }
        
        for (String part : parts) {
            ValidationResult result = validateSingleExpression(part.trim());
            if (!result.isValid()) {
                return result;
            }
        }
        return ValidationResult.valid();
    }
    
    /**
     * 验证单个表达式
     */
    private ValidationResult validateSingleExpression(String expression) {
        Matcher matcher = CONDITION_PATTERN.matcher(expression);
        if (!matcher.matches()) {
            return ValidationResult.invalid("无效的条件表达式格式: " + expression + 
                ". 期望格式: variable operator value");
        }
        
        String operator = matcher.group(2);
        String[] supportedOperators = {"==", "!=", ">=", "<=", ">", "<", "contains", "startsWith", "endsWith", "in"};
        
        boolean operatorSupported = false;
        for (String supportedOp : supportedOperators) {
            if (supportedOp.equals(operator)) {
                operatorSupported = true;
                break;
            }
        }
        
        if (!operatorSupported) {
            return ValidationResult.invalid("不支持的操作符: " + operator);
        }
        
        return ValidationResult.valid();
    }
} 