package com.mlc.ai.core.util.condition;

import com.mlc.base.common.enums.ai.ConditionEngineType;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 条件引擎工厂
 * 负责创建和管理不同类型的条件引擎实例
 */
public class ConditionEngineFactory {
    
    /**
     * 引擎实例缓存
     */
    private static final Map<ConditionEngineType, IConditionEngine> ENGINE_CACHE = new ConcurrentHashMap<>();
    
    /**
     * 获取条件引擎实例
     *
     * @param engineType 引擎类型
     * @return 条件引擎实例
     * @throws IllegalArgumentException 如果引擎类型不支持
     */
    public static IConditionEngine getEngine(ConditionEngineType engineType) {
        if (engineType == null) {
            throw new IllegalArgumentException("引擎类型不能为null");
        }
        
        return ENGINE_CACHE.computeIfAbsent(engineType, type -> switch (type) {
            case JAVASCRIPT -> new JavaScriptConditionEngine();
            case SIMPLE -> new SimpleConditionEngine();
            default -> throw new IllegalArgumentException("不支持的条件引擎类型: " + type);
        });
    }
    
    /**
     * 获取默认的条件引擎（JavaScript引擎）
     *
     * @return 默认条件引擎
     */
    public static IConditionEngine getDefaultEngine() {
        return getEngine(ConditionEngineType.JAVASCRIPT);
    }
}