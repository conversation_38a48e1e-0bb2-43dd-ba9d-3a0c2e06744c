package com.mlc.ai.task.scheduler;

import com.mlc.ai.task.context.ExecutionContext;
import com.mlc.ai.task.engine.ITaskGraph;
import com.mlc.ai.task.scheduler.policy.ITaskSchedulerPolicy;
import com.mlc.ai.task.engine.TaskStatusEvent;
import com.mlc.ai.task.model.basic.BaseTaskModel;
import com.mlc.ai.task.model.basic.BaseTaskModel.TaskStatus;
import com.mlc.ai.task.scheduler.manager.SchedulerStatusManager;
import com.mlc.ai.task.scheduler.manager.TaskQueueManager;
import com.mlc.ai.task.scheduler.manager.TaskDependencyChecker;
import com.mlc.ai.task.scheduler.status.SchedulerStatus;
import com.mlc.ai.task.workflow.WorkflowEngine;
import java.time.Duration;
import java.util.List;
import java.util.UUID;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Flux;
import reactor.core.publisher.FluxSink;
import reactor.core.publisher.Mono;


/**
 * 任务调度器
 * 负责：
 * 1. 协调各个管理器组件
 * 2. 控制任务执行流程
 * 3. 处理任务状态变化事件
 * 4. 管理调度器生命周期
 */
@Slf4j
@Getter
public class TaskScheduler {

    /**
     * 调度器唯一标识
     */
    private final String schedulerId = UUID.randomUUID().toString();

    /**
     * DAG引擎
     */
    private final ITaskGraph dagEngine;

    /**
     * 工作流引擎
     */
    private final WorkflowEngine workflowEngine;

    /**
     * 执行上下文
     */
    private final ExecutionContext executionContext;

    /**
     * 调度策略
     */
    private final ITaskSchedulerPolicy schedulerPolicy;

    /**
     * 状态管理器
     */
    private final SchedulerStatusManager schedulerStatusManager;

    /**
     * 任务队列管理器
     */
    private final TaskQueueManager queueManager;

    /**
     * 任务依赖检查器
     */
    private final TaskDependencyChecker dependencyChecker;

    /**
     * 任务状态变化事件流，用于监听任务状态变化
     */
    private Flux<TaskStatusEvent> statusChangeFlux;

    /**
     * 重新调度延迟时间
     */
    private static final Duration RESCHEDULE_DELAY = Duration.ofMillis(100);

    /**
     * 构造函数
     *
     * @param workflowEngine 工作流引擎
     */
    public TaskScheduler(WorkflowEngine workflowEngine) {
        this.workflowEngine = workflowEngine;
        this.dagEngine = workflowEngine.getDagEngine();
        this.executionContext = workflowEngine.getExecutionContext();
        this.schedulerPolicy = executionContext.getTaskSchedulerPolicy();

        // 初始化管理器组件
        this.queueManager = new TaskQueueManager(dagEngine);
        this.dependencyChecker = new TaskDependencyChecker(dagEngine, queueManager, executionContext.getExecutionId());
        this.schedulerStatusManager = new SchedulerStatusManager(schedulerId, queueManager, dependencyChecker);

        this.initializeCall();
    }

    /**
     * 初始化状态变化监听器
     */
    public void initializeCall() {
        // 添加任务状态变化监听器
        statusChangeFlux = Flux.create(sink -> dagEngine.addStatusChangeListener(sink::next));
        statusChangeFlux.subscribe(this::onTaskStatusChange);
    }

    /**
     * 注册完成回调
     *
     * @param callback 回调函数
     */
    public void registerComplete(Runnable callback) {
        schedulerStatusManager.registerCompletionCallback(callback);
    }

    /**
     * 获取当前状态
     *
     * @return 当前状态
     */
    public SchedulerStatus getStatus() {
        return schedulerStatusManager.getCurrentStatus();
    }

    /**
     * 启动调度器
     *
     * @return 执行结果流
     */
    public Flux<String> start() {
        if (!schedulerStatusManager.canStart()) {
            return Flux.error(new IllegalStateException("调度器已经在运行中"));
        }

        if (!dagEngine.isValid()) {
            return Flux.error(new IllegalArgumentException("DAG包含循环依赖，无法执行"));
        }

        // 初始化调度状态
        schedulerStatusManager.changeStatus(SchedulerStatus.RUNNING);
        schedulerStatusManager.setSchedulingCompleted(false);

        log.info("[{}] 开始调度任务流: {}", executionContext.getExecutionId(), dagEngine.getName());

        // 初始化任务队列
        queueManager.initializeQueue();

        // 创建执行流
        return Flux.create(sink -> {
            // 注册完成回调
            registerComplete(() -> {
                boolean hasFailures = dependencyChecker.hasFailedTasks();

                if (hasFailures) {
                    schedulerStatusManager.changeStatus(SchedulerStatus.FAILED);
                    sink.next("😭️ 任务流执行失败");
                } else {
                    schedulerStatusManager.changeStatus(SchedulerStatus.COMPLETED);
                    sink.next("🥳 任务流执行成功");
                }

                sink.complete();
            });

            // 启动任务调度
            scheduleNextTasks(sink);
        });
    }

    /**
     * 调度下一批任务执行
     *
     * @param sink 输出接收器
     */
    private void scheduleNextTasks(FluxSink<String> sink) {
        // 首先检查是否已标记完成，避免重复调度
        if (schedulerStatusManager.getSchedulingCompleted()) {
            return;
        }

        if (handleFailureState()) {
            return;
        }

        if (tryCompleteIfIdle()) {
            return;
        }

        int availableSlots = calculateAvailableSlots();
        if (availableSlots <= 0) {
            scheduleWithDelay(sink);
            return;
        }

        boolean dispatched = dispatchTasks(availableSlots, sink);

        if (!dispatched && !queueManager.isQueueEmpty()) {
            scheduleWithDelay(sink);
        }
    }

    private boolean handleFailureState() {
        if (schedulerStatusManager.isFailed() && !schedulerStatusManager.getSchedulingCompleted()) {
            schedulerStatusManager.setSchedulingCompleted(true);
            log.info("[{}] 任务流执行失败，不再调度新任务", executionContext.getExecutionId());
            schedulerStatusManager.notifyCompletion();
            return true;
        }
        return false;
    }

    private boolean tryCompleteIfIdle() {
        if (!queueManager.isIdle()) {
            return false;
        }

        boolean allFinished = dependencyChecker.areAllTasksFinished();
        if (allFinished && !schedulerStatusManager.getSchedulingCompleted()) {
            schedulerStatusManager.setSchedulingCompleted(true);
            log.info("[{}] 任务流执行完成，触发完成回调", executionContext.getExecutionId());
            schedulerStatusManager.notifyCompletion();
            return true;
        }

        return allFinished && schedulerStatusManager.getSchedulingCompleted();
    }

    private int calculateAvailableSlots() {
        return Math.max(0, schedulerPolicy.getMaxParallelism() - queueManager.getRunningTaskCount());
    }

    private boolean dispatchTasks(int availableSlots, FluxSink<String> sink) {
        boolean dispatched = false;
        for (int i = 0; i < availableSlots; i++) {
            BaseTaskModel selectedTask = queueManager.selectNextTask(schedulerPolicy);
            if (selectedTask == null) {
                break;
            }

            dispatched = true;
            log.debug("[{}] 选择任务执行: {} (优先级: {})", executionContext.getExecutionId(),
                      selectedTask.getId(), selectedTask.getConfig().getPriority());

            executeTask(selectedTask, sink);
        }
        return dispatched;
    }

    private void executeTask(BaseTaskModel selectedTask, FluxSink<String> sink) {
        workflowEngine.executeTask(selectedTask)
                .doOnError(error -> log.error("[{}] 任务执行出错: {}", executionContext.getExecutionId(), selectedTask.getId(), error))
                .doFinally(ignored -> scheduleNextTasks(sink))
                .subscribe(
                        sink::next,
                        error -> { /* 错误已在 doOnError 中记录 */ },
                        () -> { }
                );
    }

    private void scheduleWithDelay(FluxSink<String> sink) {
        Mono.delay(RESCHEDULE_DELAY)
            .subscribe(ignored -> scheduleNextTasks(sink));
    }

    /**
     * 取消调度
     */
    public void cancel() {
        if (schedulerStatusManager.isRunning()) {
            schedulerStatusManager.changeStatus(SchedulerStatus.CANCELLED);
        }
    }

    /**
     * 设置调度器失败状态
     */
    public void setFailed() {
        if (schedulerStatusManager.isRunning()) {
            schedulerStatusManager.changeStatus(SchedulerStatus.FAILED);
        }
    }

    /**
     * 检查任务的后继任务是否可以执行
     *
     * @param taskId 任务ID
     */
    private void checkSuccessors(String taskId) {
        List<String> readySuccessors = dependencyChecker.checkSuccessors(taskId);
        log.debug("[{}] 任务 {} 完成后，有 {} 个后继任务准备就绪",
                 executionContext.getExecutionId(), taskId, readySuccessors.size());
    }

    /**
     * 任务状态变化处理
     *
     * @param event 状态变化事件
     */
    private void onTaskStatusChange(TaskStatusEvent event) {
        String taskId = event.getTaskId();
        TaskStatus oldStatus = event.getOldStatus();
        TaskStatus newStatus = event.getNewStatus();

        log.debug("[{}] 任务 {} 状态变更: {} -> {}",
                  executionContext.getExecutionId(), taskId, oldStatus, newStatus);

        BaseTaskModel task = refreshTaskStatus(taskId, newStatus);

        // 状态策略可能会改变依赖关系，因此放在本地处理前
        executionContext.getStatusStrategyManager().handleTaskStatusChange(event, this);

        switch (newStatus) {
            case COMPLETED, SKIPPED -> handleTaskCompletion(taskId);
            case FAILED -> handleTaskFailure(task, taskId);
            case TIMEOUT -> handleTaskTimeout(task, taskId);
            case CANCELLED -> log.info("[{}] 任务 {} 被取消", executionContext.getExecutionId(), taskId);
            default -> {}
        }
    }

    private BaseTaskModel refreshTaskStatus(String taskId, TaskStatus newStatus) {
        BaseTaskModel task = dagEngine.getTasks().get(taskId);
        if (task == null) {
            log.warn("[{}] 无法更新任务状态，未找到任务: {}", executionContext.getExecutionId(), taskId);
            return null;
        }

        task.setStatus(newStatus);
        log.debug("[{}] 更新任务 {} 状态为: {}", executionContext.getExecutionId(), taskId, newStatus);
        return task;
    }

    private void handleTaskCompletion(String taskId) {
        queueManager.decrementRunningTasks();
        checkSuccessors(taskId);
    }

    private void handleTaskFailure(BaseTaskModel task, String taskId) {
        queueManager.decrementRunningTasks();
        boolean continueScheduling = task != null && schedulerPolicy.shouldContinueScheduling(task);
        if (!continueScheduling) {
            setFailed();
        } else {
            checkSuccessors(taskId);
        }

        log.error("[{}] 任务 {} 执行失败", executionContext.getExecutionId(), taskId);
    }

    private void handleTaskTimeout(BaseTaskModel task, String taskId) {
        queueManager.decrementRunningTasks();
        boolean continueScheduling = task != null && schedulerPolicy.shouldContinueScheduling(task);
        if (!continueScheduling) {
            setFailed();
        }

        log.error("[{}] 任务 {} 超时", executionContext.getExecutionId(), taskId);
    }
}
