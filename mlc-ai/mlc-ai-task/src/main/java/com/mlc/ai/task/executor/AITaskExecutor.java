package com.mlc.ai.task.executor;

import com.mlc.base.integrated.ai.client.ChatClientCacheManager;
import com.mlc.ai.task.context.ExecutionContext;
import com.mlc.ai.task.context.AIContextExecutor;
import com.mlc.ai.task.model.AITaskModel;
import com.mlc.ai.task.model.basic.BaseTaskModel;
import lombok.extern.slf4j.Slf4j;
import org.dromara.hutool.extra.spring.SpringUtil;
import reactor.core.publisher.Flux;

/**
 * AI任务执行器
 */
@Slf4j
public class AITaskExecutor implements ITaskExecutor {

    @Override
    public Flux<String> execute(BaseTaskModel task, ExecutionContext context) {
        if (!(task instanceof AITaskModel aiTask)) {
            return Flux.error(new IllegalArgumentException("任务类型不匹配，期望 AITaskModel"));
        }

        log.info("[{}] 开始执行AI任务: {}", context.getExecutionId(), task.getName());

        // 创建 LLM 上下文执行器
        AIContextExecutor llmExecutor = new AIContextExecutor(SpringUtil.getBean(ChatClientCacheManager.class));

        // 可以根据任务配置添加自定义处理器
        justInLLMExecutor(llmExecutor, aiTask, context);

        return llmExecutor.execute(aiTask, context);
    }

    /**
     * 配置 LLM 执行器
     */
    protected void justInLLMExecutor(AIContextExecutor llmExecutor, AITaskModel aiTask, ExecutionContext context) {

    }
}