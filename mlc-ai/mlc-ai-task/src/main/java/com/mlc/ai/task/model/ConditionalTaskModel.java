package com.mlc.ai.task.model;

import com.mlc.base.common.enums.ai.ConditionEngineType;
import com.mlc.ai.task.model.basic.BaseTaskModel;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.SuperBuilder;

import java.util.List;
import java.util.Map;

/**
 * 条件分支任务模型
 * 根据条件表达式的评估结果选择不同的执行路径
 * 支持多个分支条件，每个分支都有自己的条件表达式
 */
@Data
@SuperBuilder
@EqualsAndHashCode(callSuper = true)
public class ConditionalTaskModel extends BaseTaskModel {
    
    /**
     * 分支配置列表
     * 每个分支包含条件表达式和分支标识
     */
    @Builder.Default
    private List<BranchCondition> branchConditions = List.of();
    
    /**
     * 默认分支标识
     * 当所有条件都不满足时执行的分支
     */
    private String defaultBranch;
    
    /**
     * 条件表达式引擎类型
     */
    @Builder.Default
    private ConditionEngineType engineType = ConditionEngineType.JAVASCRIPT;
    
    /**
     * 条件任务配置
     */
    @Builder.Default
    private ConditionalTaskConfig config = ConditionalTaskConfig.builder().build();
    
    @Override
    public TaskType getTaskType() {
        return TaskType.CONDITIONAL;
    }
    
    @Override
    public ConditionalTaskConfig getConfig() {
        return config;
    }
    
    /**
     * 分支条件配置
     */
    @Data
    @Builder
    public static class BranchCondition {
        /**
         * 分支标识
         */
        private String branchId;
        
        /**
         * 条件表达式
         * JavaScript: upstreamTask.output.value > 100 && params.threshold > 50
         * 简单条件: value > 100, status == 'success', count >= 5
         */
        private String conditionExpression;
        
        /**
         * 条件描述（可选）
         */
        private String description;
        
        /**
         * 分支优先级（数字越小优先级越高）
         */
        @Builder.Default
        private int priority = 0;
        
        /**
         * 额外的分支参数
         */
        @Builder.Default
        private Map<String, Object> branchParams = Map.of();
    }
    

    /**
     * 条件任务配置
     */
    @Data
    @SuperBuilder
    @EqualsAndHashCode(callSuper = true)
    public static class ConditionalTaskConfig extends BaseTaskConfig {
    }
} 