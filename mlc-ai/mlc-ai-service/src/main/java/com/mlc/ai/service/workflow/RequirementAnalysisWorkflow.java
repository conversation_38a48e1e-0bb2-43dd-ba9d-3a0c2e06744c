package com.mlc.ai.service.workflow;

import com.mlc.base.common.enums.ai.AiModelTypeEnum;
import com.mlc.base.common.enums.ai.ConditionEngineType;
import com.mlc.ai.service.MlcAiServiceConstants;
import com.mlc.ai.service.workflow.task.BoundaryAnalysisTaskExecutor;
import com.mlc.ai.service.workflow.task.ClarifyingQuestionsTaskExecutor;
import com.mlc.ai.service.workflow.task.DagNodeManagerTaskExecutor;
import com.mlc.ai.task.context.ExecutionContext;
import com.mlc.ai.task.executor.ConditionalTaskExecutor;
import com.mlc.ai.task.manager.WorkflowManager;
import com.mlc.ai.task.model.AITaskModel.AITaskConfig;
import com.mlc.ai.task.model.AITaskModel.PromptModelConfig;
import com.mlc.ai.task.model.ConditionalTaskModel;
import com.mlc.ai.task.model.NormalTaskModel;
import com.mlc.ai.task.model.AITaskModel;
import com.mlc.ai.task.model.NormalTaskModel.NormalTaskConfig;
import com.mlc.ai.task.scheduler.strategy.ConditionBranchStrategy;
import com.mlc.ai.core.util.BranchConfigBuilder;
import com.mlc.base.integrated.ai.config.ChatClientConfig;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Flux;

/**
 * 需求分析工作流
 * 整合边界分析和澄清问题生成的完整流程
 */
@Slf4j
public class RequirementAnalysisWorkflow {

    private final WorkflowManager workflowManager;

    public RequirementAnalysisWorkflow() {
        this.workflowManager = WorkflowManager.INSTANCE;
    }

    /**
     * 注册任务执行器
     */
    private void registerExecutors(ExecutionContext executionContext) {
        // 注册边界分析和澄清问题生成的执行器
        executionContext.registerExecutor("BoundaryAnalysisTaskExecutor", new BoundaryAnalysisTaskExecutor());
        executionContext.registerExecutor("ClarifyingQuestionsTaskExecutor", new ClarifyingQuestionsTaskExecutor());

        // 注册条件分支执行器-系统
        executionContext.registerExecutor("ConditionalTaskExecutor", new ConditionalTaskExecutor());
        
        // 注册DAG节点管理执行器
        executionContext.registerExecutor("DagNodeManagerTaskExecutor", new DagNodeManagerTaskExecutor());
        log.info("需求分析工作流执行器注册完成");

        // 创建状态策略管理器
        executionContext.addStatusStrategy(new ConditionBranchStrategy());
    }

    /**
     * 执行需求分析工作流
     * 
     * @param userRawRequest 用户原始请求
     * @return 执行结果流
     */
    public Flux<String> executeRequirementAnalysis(String userRawRequest) {
        // 创建执行上下文
        ExecutionContext executionContext = ExecutionContext.getInstance();

        // 创建工作流
        String dagEngineId = this.createRequirementAnalysisWorkflow(userRawRequest);

        this.registerExecutors(executionContext);

        // 放入用户原始请求
        executionContext.setAttribute(MlcAiServiceConstants.USER_RAW_REQUEST_CONTEXT, userRawRequest);

        // 执行工作流
        return workflowManager.executeWorkflow(dagEngineId, executionContext);
    }

    /**
     * 创建需求分析工作流
     */
    private String createRequirementAnalysisWorkflow(String userRawRequest) {
        // 创建工作流
        String dagEngineId = workflowManager.createWorkflow(
            "需求分析工作流", 
            "处理用户原始需求的边界分析和澄清问题生成"
        );

        // 1. 边界分析任务
        String boundaryAnalysisTaskId = workflowManager.addTask(dagEngineId, createBoundaryAnalysisTask(userRawRequest));

        // 2. 条件分支任务 - 判断是否需要澄清问题
        String conditionTaskId = workflowManager.addTask(dagEngineId, createConditionTask());

        // 3. 澄清问题生成任务
        String clarifyingQuestionsTaskId = workflowManager.addTask(dagEngineId, createClarifyingQuestionsTask());

        // 4. DAG节点管理任务
        String dagNodeManagerTaskId = workflowManager.addTask(dagEngineId, createDagNodeManagerTask());

        // 建立任务依赖关系
        workflowManager.addDependency(dagEngineId, boundaryAnalysisTaskId, conditionTaskId);
        workflowManager.addDependency(dagEngineId, conditionTaskId, clarifyingQuestionsTaskId);
        workflowManager.addDependency(dagEngineId, clarifyingQuestionsTaskId, dagNodeManagerTaskId);

        log.info("需求分析工作流创建完成，工作流ID: {}", dagEngineId);
        return dagEngineId;
    }

    /**
     * 创建边界分析任务
     * 温度：0.2，目的是在边界分析时保持一定的确定性，避免过于随机的输出。
     * 您这个提示词的设计已经非常出色地限制了模型的行为空间。您需要的是模型忠实地执行这些指令，而不是进行"创造性解读"。因此，最小化随机性是关键。
     */
    private AITaskModel createBoundaryAnalysisTask(String userRawRequest) {
        return AITaskModel.builder()
            .name("用户需求分析任务")
            .modelName("gemini")
            .userRawRequest(userRawRequest)
//            .multipleResults(true)
            .executorKey("BoundaryAnalysisTaskExecutor")
            .chatClientConfig(ChatClientConfig.builder()
                                              .modelType(AiModelTypeEnum.GEMINI)
                                              .temperature(0.2)
                                              .build()
            )
            .promptModelConfig(PromptModelConfig.builder()
                               .promptPath("/mlc/ai/prompt/Outline/1_0/Boundary_Analysis_CoT_v2.prompt.yaml")
                               .build())
          .build();
    }

    /**
     * 创建条件分支任务
     */
    private ConditionalTaskModel createConditionTask() {
        // 创建分支条件
        ConditionalTaskModel.BranchCondition needsClarificationBranch = ConditionalTaskModel.BranchCondition.builder()
            .branchId("needsClarification")
            .conditionExpression("BoundaryAnalysisTaskExecutor_needsClarification == true")
            .description("需要澄清问题的分支")
            .priority(1)
            .build();

        return ConditionalTaskModel.builder()
            .name("策略条件判断")
            .branchConditions(List.of(needsClarificationBranch))
            .engineType(ConditionEngineType.JAVASCRIPT)
            .executorKey("ConditionalTaskExecutor")
            .build();
    }

    /**
     * 创建澄清问题生成任务
     * 温度：0.3，目的是在生成澄清问题时保持一定的确定性，同时允许一些灵活性。
     * 这个任务的核心是"推导"和"遵循规则"，但与纯粹的提取任务相比，它增加了"生成自然语言问题"和"智能推荐"的元素。
     * 因此，参数设置需要在"确定性"和"轻微的生成灵活性"之间找到一个平衡点。您的提示词结构已经非常好，这些参数主要是为了微调模型在生成那些需要一些"润色"的部分时的表现。
     */
    private AITaskModel createClarifyingQuestionsTask() {
        return AITaskModel.builder()
            .name("澄清问题生成任务")
            .modelName("gemini")
            .executorKey("ClarifyingQuestionsTaskExecutor")
//            .multipleResults(true)
            .config(AITaskConfig.builder()
                                .branchConfig(BranchConfigBuilder.forBranch("needsClarification"))
                                .build())
            .chatClientConfig(ChatClientConfig.builder()
                                             .modelType(AiModelTypeEnum.GEMINI)
                                             .temperature(0.3)
                                             .build()
            )
            .promptModelConfig(PromptModelConfig.builder()
                 .promptPath("/mlc/ai/prompt/Outline/2_0/Clarifying_Questions_CoT_v1.prompt.yaml")
                 .build())
            .build();
    }

    /**
     * 创建DAG节点管理任务
     */
    private NormalTaskModel createDagNodeManagerTask() {
        NormalTaskConfig config = NormalTaskConfig.builder().build();

        return NormalTaskModel.builder()
            .name("写入记忆上下文")
            .config(config)
            .executorKey("DagNodeManagerTaskExecutor")
            .build();
    }
}