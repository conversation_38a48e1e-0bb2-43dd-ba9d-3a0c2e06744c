package com.mlc.ai.service.workflow.task;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.mlc.ai.prompt.store.dag.node.IdentifyAtomicIntentNode;
import com.mlc.ai.prompt.store.dag.util.QAPair;
import com.mlc.ai.prompt.store.cache.DagGraphCacheManager;
import com.mlc.ai.service.MlcAiServiceConstants;
import com.mlc.ai.task.context.ExecutionContext;
import com.mlc.ai.task.context.AIContextExecutor;
import com.mlc.ai.task.executor.AITaskExecutor;
import com.mlc.ai.task.model.AITaskModel;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.util.List;
import java.util.ArrayList;
import java.util.Map;
import java.util.Optional;

/**
 * 结构化需求任务执行器
 * 负责将澄清问答记录转换为结构化的原子意图和概念
 * 使用 Prompt_3_Structured_Requirements_CoT.prompt.yaml 进行分析
 */
@Slf4j
public class StructuredRequirementsTaskExecutor extends AITaskExecutor {

    private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper();

    private static String fixedQes = """
        [
          {
            "question": "您计划设计的这个'车辆管理系统模型'，其核心业务目标或主要用户场景是什么？它期望解决哪些关键问题或满足哪些核心需求？",
            "alignment_focus": "ORM",
            "potential_recommendations": ["例如：车辆调度、维修保养记录、客户租赁管理、违章处理等"],
            "rationale": "用户提及进行'车辆管理系统模型'的实体设计，首先需要明确该系统的整体业务目标和范围，这是ORM建模的基础。",
            "user_answer": "主要是用于公司内部车辆的管理，包括车辆的调度、维修记录和违章处理。目标是提高车辆使用效率，减少管理成本。"
          },
          {
            "question": "基于这些核心目标，除了您已提到的'客户'实体，这个'车辆管理系统模型'还需要管理和追踪哪些最核心的业务对象或概念？（这些通常会成为系统的核心实体/表，例如 '车辆' 本身，或者与车辆相关的 '订单'、'维修记录' 等）",
            "alignment_focus": "Entity",
            "potential_recommendations": ["车辆", "订单", "维修记录", "驾驶员", "租赁合同"],
            "rationale": "在明确系统核心目标后，需要识别出系统的主要业务对象，这些将构成模型的核心实体。用户已提及'客户'，此问题旨在引导用户思考其他必要的实体。",
            "user_answer": "除了客户，还需要车辆、维修记录和订单。可能还需要驾驶员信息，不过这个要看具体需求。"
          },
          {
            "question": "对于您计划添加的'客户'实体，它在车辆管理系统中具体代表什么核心概念或业务对象，并预计承担哪些核心职责？",
            "alignment_focus": "Entity",
            "potential_recommendations": [],
            "rationale": "用户明确要添加'客户'实体，需要澄清其在业务中的核心概念和主要职责，这是定义该实体的基础。",
            "user_answer": "客户实体代表租赁或使用我们车辆的个人或公司。主要职责是记录客户的基本信息、租赁历史和联系方式。"
          },
          {
            "question": "这个'客户'实体，为了完整地发挥其作用，最需要和哪些其他核心实体（例如您可能考虑的 '车辆'、'订单' 等）建立业务关联？您初步设想这些关联是什么类型的（比如一个客户可以有多辆车，或者一个客户可以有多个订单），以及这些关联背后的业务逻辑是什么？",
            "alignment_focus": "Relation",
            "potential_recommendations": ["车辆", "订单", "租赁记录"],
            "rationale": "明确'客户'实体与其他核心实体的主要关系及其业务含义，这对构建模型结构至关重要。",
            "user_answer": "客户应该和订单关联，一个客户可以有多个订单。可能还需要和车辆关联，记录客户租赁过的车辆。业务逻辑是为了跟踪客户的使用情况和历史记录。"
          },
          {
            "question": "关于您提到的对'字段A'和'字段B'进行统计求和，这个统计结果（以及可能的字段A和字段B本身）您认为应该由哪个核心业务实体来承载或管理？是'客户'实体，还是与'车辆'、'订单'或其他业务概念相关的实体？",
            "alignment_focus": "Entity",
            "potential_recommendations": ["客户", "车辆", "订单", "特定的统计/报表实体"],
            "rationale": "用户提及'字段A和字段B的统计和'，需要明确这些数据或统计结果归属于哪个实体，以确保数据模型的完整性和准确性。",
            "user_answer": "这个统计应该由订单实体来管理，因为字段A和B是和订单相关的数据，比如订单金额和租赁时长。"
          }
        ]
        """;

    private static final String CONSOLIDATED_QA_PAIRS_KEY_IN_NODE_OUTPUT = "consolidated_qa_pairs_key";

    @Override
    protected void justInLLMExecutor(AIContextExecutor llmExecutor, AITaskModel aiTask, ExecutionContext context) {
        String executionId = context.getExecutionId();
        String qaLoopId = context.getAttribute(MlcAiServiceConstants.QA_LOOP_ID);

        if (qaLoopId == null) {
            log.error("[{}] 未在上下文中找到澄清循环节点qaLoopId", executionId);
            throw new IllegalStateException("未在上下文中找到澄清循环节点qaLoopId");
        }

        List<QAPair> consolidatedQAPairs = this.getConsolidatedQAPairsFromCache(executionId, qaLoopId);

        if (consolidatedQAPairs.isEmpty()) {
            log.warn("[{}] 从澄清循环节点 {} 的缓存输出中未获取到有效的问答记录", executionId, qaLoopId);
        }

        try {
            String qaJsonInput = OBJECT_MAPPER.writeValueAsString(consolidatedQAPairs);
            log.info("[{}] 构建结构化需求分析输入，包含 {} 个问答对，从澄清循环节点 {} 的缓存输出获取", executionId, consolidatedQAPairs.size(), qaLoopId);
            aiTask.setUserRawRequest(fixedQes);
        } catch (Exception e) {
            log.error("[{}] 构建结构化需求分析输入失败", executionId, e);
            throw new RuntimeException("构建结构化需求分析输入失败: " + e.getMessage(), e);
        }

        llmExecutor.withStreamProcessor(AIContextExecutor::codeBlockExtractorProcessor)
        .withResultProcessor((response, task, ctx) -> {
            log.info("[{}] 开始处理结构化需求分析结果 for executionId {}", executionId, executionId);
            try {
                IdentifyAtomicIntentNode structuredResult = OBJECT_MAPPER.readValue(response, IdentifyAtomicIntentNode.class);
//                List<IdentifyAtomicIntentNode> intentNodes = IdentifyAtomicIntentNode.convertToIntentNodes(structuredResult);
//                context.setAttribute(StructuredRequirementsContextKeys.IDENTIFIED_INTENT_NODES, intentNodes);
//                context.setAttribute(StructuredRequirementsContextKeys.STRUCTURED_REQUIREMENTS_RESULT, structuredResult);
                log.info("[{}] 结构化需求分析完成，识别出 {} 个原子意图", executionId, structuredResult);
                return String.format("结构化需求分析完成，识别出 %d 个原子意图", structuredResult.getIdentifiedConcepts().size());
            } catch (IOException e) {
                log.error("[{}] 解析结构化需求分析结果失败", executionId, e);
                throw new RuntimeException("解析结构化需求分析结果失败: " + e.getMessage(), e);
            }
        });
    }

    /**
     * 从缓存中获取澄清循环节点的问答对列表
     * @param executionId 执行ID
     * @param qaLoopId 澄清循环节点ID
     * @return 问答对列表
     */
    @SuppressWarnings("unchecked")
    private List<QAPair> getConsolidatedQAPairsFromCache(String executionId, String qaLoopId) {
        Optional<Map<String, Object>> cachedOutputOpt = DagGraphCacheManager.INSTANCE.getNodeOutput(executionId, qaLoopId);

        if (cachedOutputOpt.isEmpty()) {
            log.error("[{}] 未找到澄清循环节点 {}的缓存输出。该节点的输出应该已被缓存。", executionId, qaLoopId);
            throw new IllegalStateException("未找到澄清循环节点 " + qaLoopId + " (ID: " + qaLoopId + ") 的缓存输出");
        }

        Map<String, Object> cycleNodeOutput = cachedOutputOpt.get();

        Object qaPairsObject = cycleNodeOutput.get(CONSOLIDATED_QA_PAIRS_KEY_IN_NODE_OUTPUT);
        if (qaPairsObject == null) {
            log.warn("[{}] 澄清循环节点 {}的缓存输出中未找到键 '{}'", executionId, qaLoopId, CONSOLIDATED_QA_PAIRS_KEY_IN_NODE_OUTPUT);
            return new ArrayList<>();
        }

        if (!(qaPairsObject instanceof List qaPairsList)) {
            log.warn("[{}] 澄清循环节点 {} 的缓存输出中键 '{}' 对应的值不是List类型，实际类型: {}",
                     executionId, qaLoopId, CONSOLIDATED_QA_PAIRS_KEY_IN_NODE_OUTPUT, qaPairsObject.getClass().getName());
            return new ArrayList<>();
        }
        
        return qaPairsList;
    }
}