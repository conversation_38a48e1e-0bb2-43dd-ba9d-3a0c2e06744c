package com.mlc.custom;

import static io.nop.api.core.exceptions.ErrorCode.define;

import io.nop.api.core.exceptions.ErrorCode;

public interface CustomErrors {

    ErrorCode ERR_CUSTOM_INVALID_QUERY_FIELD = define("custom.err.invalid-query-field","查询条件中的field字段不合法");

    ErrorCode ERR_CUSTOM_INVALID_INPUT = define("custom.err.invalid-input","无效的输入参数");

    ErrorCode ERR_CUSTOM_INVALID_QUERY = define("custom.err.invalid-query","无效或者不合法的查询语句，请检查");

    ErrorCode ERR_CUSTOM_INVALID_GROUP_QUERY = define("custom.err.invalid-group-query","此视图不支持分组查询");

    ErrorCode ERR_CUSTOM_GROUP_FIELD_NOT_EXIST = define("custom.err.group-field-not-exist","分组字段不存在");

    ErrorCode ERR_CUSTOM_INVALID_GROUP_ITEM = define("custom.err.invalid-group-item","无效的分组数据项");
    
    ErrorCode ERR_CUSTOM_QUERY_EXECUTION_FAILED = define("custom.err.query-execution-failed","查询执行失败");
    
    ErrorCode ERR_CUSTOM_INVALID_QUERY_METHOD = define("custom.err.invalid-query-method","无效的查询方法");
}
