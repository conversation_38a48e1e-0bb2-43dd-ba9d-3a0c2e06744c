package com.mlc.custom;

public interface CustomConstants {

    String VAR_PREFIX = "@@var:";

    String FILTER_PREFIX = "@@filterEql";

    String DERIVATION_PREFIX = "@@derivation:";

    String PRE_EXECUTION = "pre-execution";

    String TAG_ACTION_PATH = "/custom/delta/xlib/common-var-list.xlib";

    String TABLE_NAME = "tableName";

    String CSV_LIST_WITH_NULL = "csv-list-with-null";

    String VAR_EXT_PATH = "/dict/var-ext.dict.yaml";

    String FETCH_POLY_RESULT = "fetchPolyResult";

    String FIND_VIEW_GROUP = "findViewGroup";
}
