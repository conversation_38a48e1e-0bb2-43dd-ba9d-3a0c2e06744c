package com.mlc.custom.crud;

import static com.mlc.base.common.beans.group.GroupItemDataBean.ROWS_NAME;
import static com.mlc.base.common.beans.group.GroupItemDataBean.TOTAL_NUM_NAME;
import static io.nop.biz.BizConstants.BIZ_OBJ_NAME_THIS_OBJ;
import static io.nop.biz.BizConstants.METHOD_FIND_COUNT;
import static io.nop.graphql.core.GraphQLConstants.ATTR_GRAPHQL_QUERY_METHOD;
import static io.nop.graphql.core.GraphQLConstants.SYS_OPERATION_FETCH_RESULTS;

import com.mlc.base.common.beans.group.GroupItemDataBean;
import com.mlc.base.common.meta.IObjGroupModel;
import com.mlc.custom.CustomConstants;
import com.mlc.custom.CustomErrors;
import com.mlc.base.common.meta.group.ObjGroupItemModel;
import com.mlc.base.common.meta.group.ObjGroupModel;
import com.mlc.custom.graphQL.CustomGraphQLExecutor;
import io.nop.api.core.annotations.biz.BizModel;
import io.nop.api.core.annotations.biz.BizMutation;
import io.nop.api.core.annotations.biz.BizQuery;
import io.nop.api.core.annotations.core.Description;
import io.nop.api.core.annotations.core.Name;
import io.nop.api.core.annotations.core.Optional;
import io.nop.api.core.annotations.graphql.GraphQLReturn;
import io.nop.api.core.beans.FieldSelectionBean;
import io.nop.api.core.beans.FilterBeans;
import io.nop.api.core.beans.TreeBean;
import io.nop.api.core.beans.query.OrderFieldBean;
import io.nop.api.core.beans.query.QueryBean;
import io.nop.api.core.beans.query.QueryFieldBean;
import io.nop.api.core.convert.ConvertHelper;
import io.nop.api.core.exceptions.NopException;
import io.nop.api.core.util.FutureHelper;
import io.nop.auth.api.utils.AuthHelper;
import io.nop.biz.BizConstants;
import io.nop.commons.util.StringHelper;
import io.nop.biz.crud.CrudBizModel;
import io.nop.commons.functional.IAsyncFunctionInvoker;
import io.nop.core.context.IServiceContext;
import io.nop.core.reflect.bean.BeanTool;
import io.nop.graphql.core.IGraphQLExecutionContext;
import io.nop.graphql.core.IGraphQLHook;
import io.nop.graphql.core.ast.GraphQLOperationType;
import io.nop.graphql.core.ast.GraphQLType;
import io.nop.graphql.core.engine.GraphQLEngine;
import io.nop.graphql.core.engine.IGraphQLEngine;
import io.nop.graphql.core.engine.IGraphQLExecutor;
import io.nop.graphql.core.parse.GraphQLDocumentParser;
import io.nop.orm.support.DynamicOrmEntity;
import io.nop.xlang.xmeta.IObjMeta;
import io.nop.xlang.xmeta.IObjPropMeta;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.CompletionStage;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.util.CollectionUtils;

@Setter
@Slf4j
@BizModel("")
public class ViewCrudBizModel extends CrudBizModel<DynamicOrmEntity> {

    private IGraphQLEngine graphQLEngine;

    private IAsyncFunctionInvoker operationInvoker;

    private IGraphQLHook graphQLHook;


    /**
     * 保存数据并且更新子表数据
     * <p>
     *  {mName=AAAAAA,
     *    shebeiToMany=[
     *        {number=111111, sname=11111, chargeUser=["1"]},
     *        {number=2222, sname=2222, oneToManyLingJian=[{id=7ab81}, {id=dasda2}], chargeUser=["2"]}
     *     ]
     *  }
     * <p>
     * 全量保存，但是增加了针对子表数据的更新 <br>
     * 例如：一对多一般情况下主子表都是新增数据，有种特殊情况：子表数据可能是选择现有数据，这时候就需要更新子表数据 <br>
     * 源码位置：CascadeFlusher.java:addOwnerJoinProps
     * 操作原理：存在模型信息之后，一切根据模型信息可以推导的知识不应该需要显式表达，
     *         子表对象是放到IOrmEntitySet集合中，关联了主表实体作为owner，当 orm 发现子表对象的owner属性是相对主表时，就会自动更新子表对主表的外键关联成主表的主键值
     * </p>
     */
    @BizMutation
    @GraphQLReturn(bizObjName = BIZ_OBJ_NAME_THIS_OBJ)
    public DynamicOrmEntity saveAndUpdateToMany(@Name("data") Map<String, Object> data, IServiceContext context) {
        return super.save(data, context);
    }

    /**
     * 调用示例:
     * const payload =
     *      `query getResult($query: Map) {
     *        data:caigoumain_iqosm21_view_1be3f9d9a85f4188be319003f97bf970__fetchPolyResult(){
     *           relationLingjian(query: $query){
     *             items{name}
     *           }
     *         }
     *       }`;
     * params = { query: { limit: 10, offset: 0, filter: { "$type": "eq", "name": "name", "value": 123 } } }
     *
     * <p>
     * 灵活性:
     * 动态多对多允许一个实体动态关联多个类型的对象，这种多样性通常超出了数据库外键约束的能力范围。
     * 数据库无法直接支持“一个外键字段动态指向多个表”的关系，因此多态关联的实现依赖于中间表和类型字段。
     * 非严格约束:
     * 数据库通常无法为多态字段（如 taggable_type 和 taggable_id）施加外键约束，因为它们的目标表动态变化。
     * 数据一致性检查（如删除操作时的级联影响）需要在应用层逻辑中完成，而不是数据库的外键规则。
     * </p>
     */
    @Description("查询多态结果")
    @BizQuery
    public Object fetchPolyResult(@Name("fieldSelection") Object fieldSelection, IServiceContext context) {
        if (fieldSelection == null) {
            throw new NopException(CustomErrors.ERR_CUSTOM_INVALID_INPUT)
                .param("fieldSelection", "字段选择不能为空");
        }

        FieldSelectionBean selection = (FieldSelectionBean) fieldSelection;

        // 验证字段选择中的查询方法
        validateQueryMethodsInSelection(selection);
        
        // 应用数据权限过滤
        selection = applyDataAuthToSelection(selection, getAuthObjName(CustomConstants.FETCH_POLY_RESULT), context);
        
        try {
            IGraphQLExecutor executor = new CustomGraphQLExecutor(operationInvoker, graphQLHook, null, graphQLEngine);

            GraphQLType gqlType = new GraphQLDocumentParser().parseType(null, super.getBizObjName());
            IGraphQLExecutionContext executionContext = graphQLEngine.newGraphQLContextFromContext(context);
            ((GraphQLEngine)graphQLEngine).initForReturnType(executionContext, GraphQLOperationType.query,
                                                             SYS_OPERATION_FETCH_RESULTS, null, gqlType, selection);

            CompletionStage<Object> objectCompletionStage = executor.fetchResult(null, executionContext);
            return FutureHelper.syncGet(objectCompletionStage);
        } catch (Exception e) {
            log.error("执行多态查询失败: bizObjName={}", getBizObjName(), e);
            throw new NopException(CustomErrors.ERR_CUSTOM_QUERY_EXECUTION_FAILED).param("bizObjName", getBizObjName());
        }
    }
    
    /**
     * 验证字段选择中的查询方法
     * @param selection 字段选择
     */
    protected void validateQueryMethodsInSelection(FieldSelectionBean selection) {
        if (selection == null || selection.getFields() == null) {
            return;
        }
        
        IObjMeta objMeta = getThisObj().getObjMeta();
        if (objMeta == null) {
            return;
        }

        boolean hasQueryMethod = false;
        for (String fieldName : selection.getFields().keySet()) {
            IObjPropMeta propMeta = objMeta.getProp(fieldName);
            if (propMeta != null) {
                String queryMethod = (String) propMeta.prop_get(ATTR_GRAPHQL_QUERY_METHOD);
                if (!StringHelper.isEmpty(queryMethod)) {
                    validateQueryMethod(fieldName, queryMethod);
                    hasQueryMethod = true;
                }
            }
        }
        if (!hasQueryMethod) {
            throw new NopException(CustomErrors.ERR_CUSTOM_INVALID_QUERY);
        }
    }
    
    /**
     * 验证查询方法是否合法
     * @param fieldName 字段名
     * @param queryMethod 查询方法
     */
    protected void validateQueryMethod(String fieldName, String queryMethod) {
        if (!Set.of(BizConstants.METHOD_FIND_PAGE, BizConstants.METHOD_FIND_LIST,
                    BizConstants.METHOD_FIND_FIRST, BizConstants.METHOD_FIND_COUNT).contains(queryMethod)) {
            throw new NopException(CustomErrors.ERR_CUSTOM_INVALID_QUERY_METHOD)
                .param("fieldName", fieldName)
                .param("queryMethod", queryMethod);
        }
    }


    /**
     * 应用数据权限过滤
     * @param selection 原始字段选择
     * @param authObjName 权限对象名
     * @param context 服务上下文
     * @return 应用权限过滤后的字段选择
     */
    protected FieldSelectionBean applyDataAuthToSelection(FieldSelectionBean selection, String authObjName, IServiceContext context) {
        if (context.getDataAuthChecker() == null || selection == null) {
            return selection;
        }
        
        // 对于复杂的多态查询，需要对每个子查询都应用数据权限
        FieldSelectionBean authSelection = new FieldSelectionBean();
        if (selection.getFields() != null) {
            authSelection.setFields(new LinkedHashMap<>(selection.getFields()));
        }
        if (selection.getArgs() != null) {
            authSelection.setArgs(new LinkedHashMap<>(selection.getArgs()));
        }

        if (authSelection.getFields() != null) {
            authSelection.getFields().forEach((fieldName, fieldSelection) -> {
                if (fieldSelection != null) {
                    applyAuthToFieldArgs(fieldName, fieldSelection, authObjName, context);
                }
            });
        }
        
        return authSelection;
    }
    
    /**
     * 应用权限过滤
     * @param fieldName 字段名
     * @param fieldSelection 字段选择
     * @param authObjName 权限对象名
     * @param context 服务上下文
     */
    protected void applyAuthToFieldArgs(String fieldName, FieldSelectionBean fieldSelection, String authObjName, IServiceContext context) {
        if (fieldSelection.getArgs() == null) {
            return;
        }
        
        Object queryArg = fieldSelection.getArgs().get("query");
        if (queryArg instanceof QueryBean query) {

            IObjPropMeta propMeta = getThisObj().getObjMeta().getProp(fieldName);

            // 添加数据权限过滤到查询条件中
            QueryBean authQuery = AuthHelper.appendFilter(
                context.getDataAuthChecker(), query, authObjName,
                (String) propMeta.prop_get(ATTR_GRAPHQL_QUERY_METHOD), context
            );

            fieldSelection.getArgs().put("query", authQuery);
        }
    }

    /**
     * 分组查询服务函数，根据 meta 元信息推导出分组查询的结果
     * @param groupField 分组ID
     * @param query 查询条件
     * @param selection 字段选择
     * @param context 上下文
     * @return 分组查询结果
     *
     * 注释：基础meta为根据模型生成的xmeta，而 xmeta 可以作为视图，每个视图都是独立存在的，可以在独立的视图上施加对象级别的转换
     * 最终的形式为 base xmeta + object data, 最终是基于当前视图的产物
     * <p>
     * Object Data => Entity + Meta => Agg(Entity) + Agg(Meta)
     * Selection 作用于基础对象结构字段选择，而分组查询的结果是基于 object data 的结果，所以 selection 作用于 object data
     */
    @BizQuery
    @GraphQLReturn(bizObjName = BIZ_OBJ_NAME_THIS_OBJ)
    public List<GroupItemDataBean<DynamicOrmEntity>> findViewGroup(@Name(ObjGroupModel.GROUP_FIELD) String groupField,
        @Optional @Name("query") QueryBean query, FieldSelectionBean selection, IServiceContext context) {

        if (StringHelper.isEmpty(groupField)) {
            throw new NopException(CustomErrors.ERR_CUSTOM_INVALID_INPUT).param(ObjGroupModel.GROUP_FIELD, "分组字段不能为空");
        }

        QueryBean queryBean = query;
        if (queryBean == null) queryBean = new QueryBean();

        IObjMeta objMeta = getThisObj().requireObjMeta();
        // 是否有分组相关的元信息
        Object viewGroups = objMeta.prop_get(ObjGroupModel.GROUP_MODEL);
        if(viewGroups == null){
            throw new NopException(CustomErrors.ERR_CUSTOM_INVALID_GROUP_QUERY);
        }

        IObjGroupModel objGroupModel = BeanTool.castBeanToType(viewGroups, IObjGroupModel.class);
        
        // 验证分组ID是否匹配
        if (!Objects.equals(groupField, objGroupModel.getGroupField())) {
            throw new NopException(CustomErrors.ERR_CUSTOM_GROUP_FIELD_NOT_EXIST)
                .param(ObjGroupModel.GROUP_FIELD, groupField)
                .param("expectedGroupId", objGroupModel.getGroupField());
        }

        Boolean isBeforeGroup = objGroupModel.getIsBeforeGroup(); // 是否需要先进行前置分组查询
        if (isBeforeGroup == Boolean.TRUE){
            // 前置分组,需要先对要查询的字段进行分组
            this.buildBeforeGroupQuery(objMeta, objGroupModel, queryBean);
        }

        List<GroupItemDataBean<DynamicOrmEntity>> bodyData = new ArrayList<>();
        
        // 获取分组项列表
        List<ObjGroupItemModel> groupItems = objGroupModel.getGroupItems();
        if (CollectionUtils.isEmpty(groupItems)) {
            return bodyData;
        }
        
        Boolean isStatisticsEmpty = objGroupModel.getIsStatisticsEmpty();
        
        for (ObjGroupItemModel groupItemMeta : groupItems) {
            QueryBean cloneQuery = queryBean.cloneInstance();
            
            // 构建分组过滤条件
            TreeBean findFilter;
            if (isStatisticsEmpty == Boolean.TRUE && StringHelper.isEmpty(groupItemMeta.getKey())) {
                // 统计空值情况
                findFilter = FilterBeans.isNull(objGroupModel.getGroupField());
            } else {
                // 先用 contains，后续根据 control 类型进行优化
                findFilter = FilterBeans.contains(objGroupModel.getGroupField(), groupItemMeta.getKey());
            }
            cloneQuery.addFilter(findFilter);

            GroupItemDataBean<DynamicOrmEntity> groupItemData = new GroupItemDataBean<>();
            BeanUtils.copyProperties(groupItemMeta, groupItemData);
            bodyData.add(groupItemData);

            Long totalNum = 0L;
            // 查询分组统计数量
            if (selection.hasSourceField(TOTAL_NUM_NAME)) {
                totalNum = this.findGroupCount(objGroupModel.getGroupField(), findFilter, context);
                groupItemData.setTotalNum(totalNum);
            }

            // 查询分组数据
            if (selection.hasSourceField(ROWS_NAME) && (totalNum > 0 || !selection.hasSourceField(TOTAL_NUM_NAME))) {
                List<DynamicOrmEntity> pageData = super.findList(cloneQuery, selection, context);
                if (!CollectionUtils.isEmpty(pageData)) {
                    groupItemData.setRows(pageData);
                }
            }
        }
        return bodyData;
    }
    
    /**
     * 构建前置分组查询条件
     * @param objMeta 对象元数据
     * @param objGroupModel 分组模型
     * @param queryBean 查询条件
     */
    protected void buildBeforeGroupQuery(IObjMeta objMeta, IObjGroupModel objGroupModel, QueryBean queryBean) {
        // 前置分组查询逻辑：对于非预定义选项的字段（如文本字段），需要先查询出所有可能的分组值
        String groupField = objGroupModel.getGroupField();
        
        // 检查分组字段是否存在于对象元数据中
        IObjPropMeta propMeta = objMeta.getProp(groupField);
        if (propMeta == null) {
            throw new NopException(CustomErrors.ERR_CUSTOM_GROUP_FIELD_NOT_EXIST)
                .param("fieldName", groupField);
        }
        
        // 为前置分组添加必要的查询条件和排序
        if (queryBean.getOrderBy() == null || queryBean.getOrderBy().isEmpty()) {
            queryBean.addOrderBy(List.of(OrderFieldBean.forField(groupField)));
        }
    }
    
    /**
     * 查询分组统计数量
     * @param groupField 分组字段
     * @param filter 过滤条件
     * @param context 服务上下文
     * @return 统计数量
     */
    protected Long findGroupCount(String groupField, TreeBean filter, IServiceContext context) {
        try {
            QueryBean groupCountQuery = new QueryBean();
            groupCountQuery.setSourceName(getEntityName());
            groupCountQuery.addField(QueryFieldBean.forField(groupField).count().alias(TOTAL_NUM_NAME));
            groupCountQuery.setFilter(filter);
            groupCountQuery.addGroupField(groupField);

            // 添加数据权限过滤
            String authObjName = getAuthObjName(METHOD_FIND_COUNT);
            groupCountQuery = AuthHelper.appendFilter(
                context.getDataAuthChecker(), groupCountQuery, authObjName, METHOD_FIND_COUNT, context);

            // 添加对象元数据的过滤条件
            IObjMeta objMeta = getThisObj().getObjMeta();
            if (objMeta != null && objMeta.getFilter() != null) {
                groupCountQuery.addFilter(objMeta.getFilter().cloneInstance());
            }
            
            Map<String, Object> groupCountMap = orm().findFirstByQuery(groupCountQuery);
            
            if (groupCountMap != null && groupCountMap.get(TOTAL_NUM_NAME) != null) {
                Object count = groupCountMap.get(TOTAL_NUM_NAME);
                return ConvertHelper.toLong(count, NopException::new);
            }
            
            return 0L;
        } catch (Exception e) {
            log.warn("查询分组统计数量失败：groupField={}, filter={}", groupField, filter, e);
            return 0L;
        }
    }

}
