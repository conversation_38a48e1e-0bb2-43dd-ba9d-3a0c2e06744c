package com.mlc.custom.crud.helper;


import com.mlc.custom.CustomErrors;
import com.mlc.base.common.meta.group.ObjGroupModel;
import io.nop.api.core.beans.TreeBean;
import io.nop.api.core.exceptions.NopException;
import io.nop.core.lang.sql.SQL;
import io.nop.orm.dao.DaoQueryHelper;
import io.nop.xlang.xmeta.IObjMeta;


public class ViewGroupHelper {

    /**
     * -- 使用 CTE 进行统计
     * WITH grouped_illegal_items AS (
     *     SELECT illegal_items AS `key`, COUNT(*) AS totalNum
     *     FROM vm_violation_registration
     *     GROUP BY illegal_items
     * )
     * -- 将统计数据和详细数据关联
     * SELECT
     *     g.key, g.totalNum, v.rowid,v.illegal_items, v.violator,
     * FROM grouped_illegal_items g
     * JOIN vm_violation_registration v
     * ON g.key = v.illegal_items
     * WHERE v.illegal_items = '["beafd2af-87a3-4d53-998f-c00638fc2be2"]'
     */
    public static SQL.SqlBuilder buildGroupQuerySql(IObjMeta objMeta, ObjGroupModel viewGroup, TreeBean filter) {
        if (viewGroup == null) {
            throw new NopException(CustomErrors.ERR_CUSTOM_INVALID_GROUP_ITEM);
        }

        SQL.SqlBuilder sb = SQL.begin();

        String entityName = objMeta.getEntityName();
        if (entityName == null)
            entityName = objMeta.getName();

        String groupId = viewGroup.getGroupField();

        sb.sql(" with grouped_items as (");
        sb.sql(" select " + groupId + " as keyName, count(*) as totalNum ");
        sb.sql(" from " + entityName + " ");
        sb.sql(" group by " + groupId + " ");
        sb.sql(" ) ");

        sb.sql(" select g.keyName, g.totalNum, v.violator, v.ownerid "); // v.violator, v.ownerid 应该为 selection 选择字段
        sb.sql(" from grouped_items g ");
        sb.sql(" join " + entityName + " v ");
        sb.sql(" on g.keyName = v." + groupId + " ");
        if (filter != null) {
            sb.where();
            DaoQueryHelper.appendFilter(sb, "v", filter);
        }
        return sb;
    }

    public static void buildBeforeGroupQuery(IObjMeta objMeta, ObjGroupModel viewGroup, TreeBean filter) {

    }
}
