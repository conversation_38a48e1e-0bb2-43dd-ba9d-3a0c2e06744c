package com.mlc.uop.service.dto.department;

import io.nop.api.core.annotations.data.DataBean;
import io.nop.api.core.annotations.meta.PropMeta;
import java.util.List;
import lombok.Getter;
import lombok.Setter;


@DataBean
@Getter
@Setter
public class MoveDeptRequest {

    /**
     * 排好序的 部门Ids
     */
    private List<String> sortedDepartmentIds;

    /**
     * 移动到的父部门Id
     */
    private String moveToParentId;

    /**
     * 移动的部门Id
     */
    private String movingDepartmentId;


    @PropMeta(mandatory = true)
    public void setSortedDepartmentIds(List<String> sortedDepartmentIds) {
        this.sortedDepartmentIds = sortedDepartmentIds;
    }

    @PropMeta(mandatory = true)
    public void setMovingDepartmentId(String movingDepartmentId) {
        this.movingDepartmentId = movingDepartmentId;
    }
}
