package com.mlc.uop.service.dto.tenant;

import lombok.Data;

@Data
public class TenantSettingResponse {

    /* 租户code */
    private String regCode;
    /* 邀请审核 */
    private Boolean userAuditEnabled;
    /* 允许 code 搜索加入 */
    private Boolean allowProjectCodeJoin;
    /* 人员加入组织时需要填写公司信息 */
    private Boolean userFillCompanyEnabled;
    /* 人员加入组织需要选择部门 */
    private Boolean userFillDepartmentEnabled;
    /* 人员加入组织需要填写职位 */
    private Boolean userFillJobEnabled;
    /* 人员加入组织需要选择工作地点 */
    private Boolean userFillWorkSiteEnabled;
    /* 人员加入组织需要填写工号 */
    private Boolean userFillJobNumberEnabled;
    /* 是否允许所有人查看组织架构 */
    private Boolean allowStructureForAll;
    /* 是否允许用户自己编辑组织架构 */
    private Boolean allowStructureSelfEdit;
}
