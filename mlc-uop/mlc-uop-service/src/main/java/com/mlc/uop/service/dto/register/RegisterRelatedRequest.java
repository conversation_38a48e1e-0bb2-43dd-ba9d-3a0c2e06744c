package com.mlc.uop.service.dto.register;


import java.util.Map;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.experimental.Accessors;
import org.dromara.hutool.core.bean.BeanUtil;
import org.springframework.beans.BeanUtils;


/**
 * 统一用户注册信息，包括所有的属性
 */
@Data
@Accessors(chain = true)
@RequiredArgsConstructor(staticName = "of")
public class RegisterRelatedRequest {

    private String account;

    private String password;

    private String fullName;

    private String email;

    private String departmentIds;

    private String jobIds;

    private String roleIds;

    private String workSiteId;

    private String contactPhone;

    private String jobNumber;

    public RegisterRelatedRequest(Object source) {
        // map转bean
        if (source instanceof Map<?,?> sourceMap) {
            BeanUtil.fillBeanWithMap(sourceMap, this, null);
        } else {
            BeanUtils.copyProperties(source,this);
        }

    }
}
