package com.mlc.uop.service.dto.userInfo;


import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class ContactSettingRequest {

    private Integer dataRange;

    private List<String> filterAccountIds;

    private Boolean filterFriend;

    private String filterProjectId;

    private Boolean includeMySelf;

    private Boolean includeSystemField;

    private Boolean includeUndefinedAndMySelf;

    private String keywords;

    private Boolean onlyMyJoin;

    private String parentId;

    private Integer pageIndex;

    private Integer pageSize;

    private List<String> prefixAccountIds;

    private String projectId;

    private String filterCustomize;

    // 转换pageIndex成offset
    public Integer getOffset() {
        return pageSize * (pageIndex == null || pageIndex == 0 ? 0 : pageIndex - 1);
    }
}
