package com.mlc.base.integrated.ai.config;

import com.mlc.base.common.enums.ai.AiModelTypeEnum;
import lombok.Builder;
import java.util.HashMap;
import java.util.Map;
import lombok.Getter;
import lombok.Setter;

/**
 * ChatClient 配置类
 */
@Getter
@Setter
@Builder
public class ChatClientConfig {
    
    /**
     * 模型类型
     */
    private AiModelTypeEnum modelType;

    /**
     * 模型温度参数 (0.0-2.0)
     * 控制生成文本的随机性，值越高越随机
     */
    @Builder.Default
    private Double temperature = 0.7;

    /**
     * 最大令牌数
     * 限制生成文本的最大长度
     */
    private Integer maxTokens;

    /**
     * Top-p 参数 (0.0-1.0)
     * 核采样参数，控制词汇选择的多样性
     */
    private Double topP;

    /**
     * 频率惩罚 (-2.0-2.0)
     * 减少重复内容的生成
     */
    private Double frequencyPenalty;

    /**
     * 存在惩罚 (-2.0-2.0)
     * 鼓励生成新话题的内容
     */
    private Double presencePenalty;

    /**
     * 是否启用内存对话
     */
    @Builder.Default
    private boolean enableMemory = true;

    /**
     * 是否启用日志记录
     */
    @Builder.Default
    private boolean enableLogging = true;

    /**
     * 会话ID
     */
    private String sessionId;

    /**
     * 自定义系统提示词
     */
    private String systemPrompt;

    /**
     * 额外的 ChatClient 选项
     */
    @Builder.Default
    private Map<String, Object> additionalOptions = new HashMap<>();
}
