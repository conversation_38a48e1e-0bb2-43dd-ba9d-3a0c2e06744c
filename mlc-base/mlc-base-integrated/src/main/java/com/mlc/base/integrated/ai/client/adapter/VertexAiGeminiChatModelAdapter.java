package com.mlc.base.integrated.ai.client.adapter;

import com.mlc.base.common.enums.ai.AiModelTypeEnum;
import com.mlc.base.integrated.ai.config.ChatClientConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.chat.model.ChatModel;

/**
 * Vertex AI Gemini ChatModel 适配器
 */
@Slf4j
public class VertexAiGeminiChatModelAdapter implements IChatModelAdapter {

    @Override
    public boolean supports(AiModelTypeEnum modelType, ChatModel chatModel) {
        return modelType == AiModelTypeEnum.GEMINI;
//               chatModel instanceof VertexAiGeminiChatModel
    }

    @Override
    public void configureChatOptions(ChatClient.Builder builder, ChatModel chatModel, ChatClientConfig config) {
//        if (!(chatModel instanceof VertexAiGeminiChatModel)) {
//            log.warn("ChatModel 不是 VertexAiGeminiChatModel 类型，跳过配置");
//            return;
//        }
//
//        VertexAiGeminiChatOptions options = buildVertexAiGeminiChatOptions(config);
//        if (options != null) {
//            builder.defaultOptions(options);
//            log.debug("已配置 Vertex AI Gemini Chat Options: 温度={}, 最大令牌={}",
//                config.getTemperature(), config.getMaxTokens());
//        }
    }


//    private VertexAiGeminiChatOptions buildVertexAiGeminiChatOptions(ChatClientConfig config) {
//        VertexAiGeminiChatOptions.Builder optionsBuilder = VertexAiGeminiChatOptions.builder();
//
//        if (config.getTemperature() != null) {
//            optionsBuilder.temperature(config.getTemperature());
//        }
//        if (config.getMaxTokens() != null) {
//            optionsBuilder.maxOutputTokens(config.getMaxTokens());
//        }
//        if (config.getTopP() != null) {
//            optionsBuilder.topP(config.getTopP());
//        }
//
//        return optionsBuilder.build();
//    }
} 