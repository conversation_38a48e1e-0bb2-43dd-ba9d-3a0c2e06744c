package com.mlc.base.integrated.ai.client.adapter;

import com.mlc.base.integrated.ai.config.ChatClientConfig;
import com.mlc.base.common.enums.ai.AiModelTypeEnum;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.chat.model.ChatModel;

/**
 * ChatModel 适配器接口
 * 用于抽象不同模型的配置逻辑，支持扩展新的模型类型
 */
public interface IChatModelAdapter {

    /**
     * 判断是否支持指定的模型类型
     * 
     * @param modelType 模型类型
     * @param chatModel ChatModel 实例
     * @return 是否支持
     */
    boolean supports(AiModelTypeEnum modelType, ChatModel chatModel);

    /**
     * 配置 ChatClient 选项
     * 
     * @param builder ChatClient 构建器
     * @param chatModel ChatModel 实例
     * @param config 配置信息
     */
    void configureChatOptions(ChatClient.Builder builder, ChatModel chatModel, ChatClientConfig config);

}