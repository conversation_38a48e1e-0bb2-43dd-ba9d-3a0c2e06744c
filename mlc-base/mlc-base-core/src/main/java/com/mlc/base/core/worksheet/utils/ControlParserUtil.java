package com.mlc.base.core.worksheet.utils;

import static com.mlc.base.common.exception.errors.CommonErrors.ERR_COMMON_WORKSHEET_CONTROL_CONVERT;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.mlc.base.core.worksheet.controls.base.BasicControl;
import io.nop.api.core.exceptions.NopException;
import java.util.List;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class ControlParserUtil {

    public static final ControlParserUtil INSTANCE = new ControlParserUtil();

    private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper();

    /**
     * 控件列表JSON字符串转换为控件对象列表
     * @param jsonListStr 控件列表JSON字符串
     * @return 控件对象列表
     */
    public List<BasicControl> parseControlList(String jsonListStr) {
        try {
            return OBJECT_MAPPER.readValue(jsonListStr, new TypeReference<>() {});
        } catch (JsonProcessingException e) {
            log.error("控件转换异常，前端传入的控件列表：{}", jsonListStr, e);
            throw new NopException(ERR_COMMON_WORKSHEET_CONTROL_CONVERT, e);
        }
    }

    /**
     * 解析单个节点
     * @param nodeJson 节点JSON
     * @return 基础节点
     */
    public BasicControl parseControl(JsonNode nodeJson) {
        try {
            return OBJECT_MAPPER.treeToValue(nodeJson, BasicControl.class);
        } catch (Exception e) {
            log.error("解析单个节点失败", e);
            throw new RuntimeException("解析节点失败: " + e.getMessage(), e);
        }
    }

    /**
     * 对象转换为JSON字符串
     */
    public String toJsonString(List<BasicControl> obj) {
        try {
            OBJECT_MAPPER.setSerializationInclusion(JsonInclude.Include.NON_NULL);
            return OBJECT_MAPPER.writeValueAsString(obj);
        } catch (JsonProcessingException e) {
            log.error("对象转换为JSON字符串失败", e);
            throw new NopException(ERR_COMMON_WORKSHEET_CONTROL_CONVERT, e);
        }
    }
}
