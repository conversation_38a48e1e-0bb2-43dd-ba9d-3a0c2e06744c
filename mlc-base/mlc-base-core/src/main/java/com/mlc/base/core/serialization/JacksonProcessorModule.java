package com.mlc.base.core.serialization;

import com.fasterxml.jackson.core.Version;
import com.fasterxml.jackson.databind.module.SimpleModule;
import com.mlc.base.core.serialization.generic.ProcessorRegistry;
import com.mlc.base.core.serialization.generic.ProcessBeanSerializerModifier;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

/**
 * 通用注解支持的Jackson模块
 */
@Getter
@Slf4j
public class JacksonProcessorModule extends SimpleModule {

    /**
     *  获取注解处理器注册表
     */
    private final ProcessorRegistry registry;
    
    public JacksonProcessorModule(ProcessorRegistry registry) {
        super("AnnotationProcessorModule", new Version(1, 0, 0, null, null, null));
        this.registry = registry;
        
        log.info("初始化通用注解Jackson模块");
        
        setSerializerModifier(new ProcessBeanSerializerModifier(registry));
    }

}



