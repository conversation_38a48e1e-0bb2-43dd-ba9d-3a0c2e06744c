package com.mlc.base.core.serialization.strategies;

import java.lang.annotation.Annotation;
import java.lang.reflect.Field;

/**
 * 注解处理策略接口
 */
public interface IProcessorStrategy<T extends Annotation> {

    /**
     * 处理注解逻辑
     * 
     * @param fieldName 字段名
     * @param originalValue 原始值
     * @param annotation 注解实例
     * @param field 字段信息
     * @param targetObject 目标对象
     * @return 处理后的值
     */
    Object process(String fieldName, Object originalValue, T annotation, Field field, Object targetObject);
}



