package com.mlc.base.core.auth.model.base;

import com.mlc.base.core.auth.model.base.info.DeptInfo;
import com.mlc.base.core.auth.model.base.info.RoleInfo;
import com.mlc.base.integrated.redis.config.codec.KryoSerialize;
import java.io.Serial;
import java.util.List;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@KryoSerialize
public class TenantUserSession extends BaseUserSession {

    @Serial
    private static final long serialVersionUID = -7148128229540431843L;

    /**
     * 随着当前用户的租户 ID 变化而变化的，例如：用户登录、切换到不同的租户，那么这里的租户 id 也会变化
     */
    private String tenantId;

    private List<DeptInfo> deptInfoList;

    private List<RoleInfo> roleInfoList;

    public TenantUserSession() {
        super(null, null, null);
    }

    public TenantUserSession(String tenantId, String userId,
                                 String account, String fullname) {
        super(userId, account, fullname);
        this.tenantId = tenantId;
    }

    public TenantUserSession(String tenantId, BaseUserSession baseUserSession) {
        this(tenantId, baseUserSession.getUserId(), baseUserSession.getAccount(),
             baseUserSession.getFullname());
    }
}
