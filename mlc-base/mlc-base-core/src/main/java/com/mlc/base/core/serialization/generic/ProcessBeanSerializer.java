package com.mlc.base.core.serialization.generic;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.fasterxml.jackson.databind.jsontype.TypeSerializer;
import com.mlc.base.core.serialization.strategies.IProcessorStrategy;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.lang.annotation.Annotation;
import java.lang.reflect.Field;
import java.util.HashMap;
import java.util.Map;

/**
 * 通用注解Bean序列化器
 */
@Slf4j
public class ProcessBeanSerializer extends JsonSerializer<Object> {
    
    private final Map<String, AnnotationInfo> annotationCache = new HashMap<>();
    private final JsonSerializer<Object> defaultSerializer;
    private final ProcessorRegistry registry;
    
    public ProcessBeanSerializer(JsonSerializer<Object> defaultSerializer, Class<?> targetType, ProcessorRegistry registry) {
        this.defaultSerializer = defaultSerializer;
        this.registry = registry;
        cacheAnnotations(targetType);
    }

    /**
     * 注解信息缓存类
     */
    private record AnnotationInfo(Annotation annotation, Class<? extends Annotation> annotationType, Field field) { }
    
    /**
     * 缓存类中所有已注册的注解信息
     */
    private void cacheAnnotations(Class<?> clazz) {
        if (clazz == null) return;
        
        Field[] fields = clazz.getDeclaredFields();
        for (Field field : fields) {
            Annotation[] annotations = field.getAnnotations();
            for (Annotation annotation : annotations) {
                Class<? extends Annotation> annotationType = annotation.annotationType();
                // 只缓存已注册的注解类型
                if (registry.hasProcessor(annotationType)) {
                    annotationCache.put(field.getName(), new AnnotationInfo(annotation, annotationType, field));
                    log.debug("缓存注解信息: 字段={}, 注解类型={}", field.getName(), annotationType.getSimpleName());
                    // 每个字段只处理第一个已注册的注解
                    break;
                }
            }
        }
        
        cacheAnnotations(clazz.getSuperclass());
    }
    
    @Override
    public void serialize(Object bean, JsonGenerator gen, SerializerProvider provider)
            throws IOException {

        // 在序列化前进行注解处理
        Object enrichedBean = processAnnotations(bean);

        defaultSerializer.serialize(enrichedBean, gen, provider);
    }
    
    @Override
    public void serializeWithType(Object bean, JsonGenerator gen, SerializerProvider provider, TypeSerializer typeSer)
            throws IOException {
        Object enrichedBean = processAnnotations(bean);
        defaultSerializer.serializeWithType(enrichedBean, gen, provider, typeSer);
    }

    /**
     * 处理对象中的所有注解
     */
    private Object processAnnotations(Object bean) {
        if (bean == null || annotationCache.isEmpty()) {
            return bean;
        }
        
        try {
            Class<?> clazz = bean.getClass();
            
            for (Map.Entry<String, AnnotationInfo> entry : annotationCache.entrySet()) {
                String fieldName = entry.getKey();
                AnnotationInfo annotationInfo = entry.getValue();
                
                try {
                    Field field = findField(clazz, fieldName);
                    if (field != null) {
                        field.setAccessible(true);
                        Object fieldValue = field.get(bean);
                        
                        // 处理字段值
                        Object processedValue = processFieldValue(fieldName, fieldValue, annotationInfo, bean);
                        
                        // 如果值发生变化，更新字段值
                        if (processedValue != fieldValue) {
                            field.set(bean, processedValue);
                        }
                    }
                } catch (IllegalAccessException e) {
                    log.error("处理字段 {} 的注解时发生错误", fieldName, e);
                }
            }
            
            return bean;
            
        } catch (Exception e) {
            log.error("Bean注解处理失败", e);
            return bean;
        }
    }
    
    /**
     * 处理单个字段值
     */
    @SuppressWarnings("unchecked")
    private Object processFieldValue(String fieldName, Object originalValue, AnnotationInfo annotationInfo, Object targetObject) {
        Class<? extends Annotation> annotationType = annotationInfo.annotationType;
        Annotation annotation = annotationInfo.annotation;
        Field field = annotationInfo.field;

        IProcessorStrategy<Annotation> strategy = (IProcessorStrategy<Annotation>) registry.getProcessor(annotationType);
        if (strategy == null) {
            return originalValue;
        }

        try {
            return strategy.process(fieldName, originalValue, annotation, field, targetObject);
        } catch (Exception e) {
            log.error("字段 {} 注解 {} 处理失败，程序错误", fieldName, annotationType.getSimpleName(), e);
            throw new RuntimeException("字段 " + fieldName + " 注解 " + annotationType.getSimpleName() + " 处理异常", e);
        }

    }
    
    /**
     * 查找字段（包括父类字段）
     */
    private Field findField(Class<?> clazz, String fieldName) {
        while (clazz != null) {
            try {
                return clazz.getDeclaredField(fieldName);
            } catch (NoSuchFieldException e) {
                clazz = clazz.getSuperclass();
            }
        }
        return null;
    }
}
