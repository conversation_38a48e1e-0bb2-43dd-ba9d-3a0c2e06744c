package com.mlc.base.core.auth.model.role;


import com.mlc.base.integrated.redis.config.codec.KryoSerialize;
import java.io.Serial;
import java.io.Serializable;
import java.util.Set;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 用户在*租户*内的角色权限，通过租户+用户ID构建上下文权限信息，随着用户切换租户而变化
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@KryoSerialize
public class BaseUserRolePermission implements Serializable {

    @Serial
    private static final long serialVersionUID = -3044486245608111590L;

    /**
     * 角色标识
     */
    private Set<String> roleIdentitys;
    /**
     * 权限字符串
     */
    private Set<String> permissions;
}
