package com.mlc.base.core.auth.role;

import com.mlc.base.common.enums.application.AppRoleTypeEnum;
import com.mlc.base.common.enums.application.PermissionWayEnum;

import java.util.Objects;

/**
 * 权限策略接口
 */
public interface IPermissionStrategy {
    
    /**
     * 是否需要检查操作权限
     */
    boolean needCheckActionAuth();
    
    /**
     * 是否需要检查数据范围权限
     */
    boolean needCheckDataAuth();
    
    /**
     * 默认权限策略实现
     */
    class DefaultPermissionStrategy implements IPermissionStrategy {
        
        private final Integer maxRoleType;
        private final Integer maxPermissionWay;
        
        public DefaultPermissionStrategy(Integer maxRoleType, Integer maxPermissionWay) {
            this.maxRoleType = maxRoleType;
            this.maxPermissionWay = maxPermissionWay;
        }
        
        @Override
        public boolean needCheckActionAuth() {
            // 只有自定义角色且自定义权限方式才需要检查操作权限
            boolean isCustomRole = Objects.equals(maxRoleType, AppRoleTypeEnum.CUSTOM.getValue());
            if (!isCustomRole) {
                return false;
            }
            
            boolean isCustomPermissionWay = Objects.equals(maxPermissionWay, PermissionWayEnum.CUSTOM.getValue());
            return isCustomRole && isCustomPermissionWay;
        }
        
        @Override
        public boolean needCheckDataAuth() {
            // 开发者角色或自定义角色需要检查数据范围权限
            boolean isDeveloper = Objects.equals(maxRoleType, AppRoleTypeEnum.DEVELOPERS.getValue());
            boolean isCustom = Objects.equals(maxRoleType, AppRoleTypeEnum.CUSTOM.getValue());
            boolean isNotManageAllRecord = !Objects.equals(maxPermissionWay, PermissionWayEnum.MANAGE_ALL_RECORD.getValue());
            
            return isDeveloper || isCustom || isNotManageAllRecord;
        }
    }

    /**
     * 创建默认权限策略
     */
    static IPermissionStrategy createDefault(Integer maxRoleType, Integer maxPermissionWay) {
        return new DefaultPermissionStrategy(maxRoleType, maxPermissionWay);
    }
}
