package com.mlc.base.core.auth.model.base;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.mlc.base.integrated.redis.config.codec.KryoSerialize;
import java.io.Serial;
import java.util.ArrayList;
import java.util.Collection;
import lombok.Getter;
import lombok.Setter;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.userdetails.UserDetails;

/**
 * 用户 Session 基础信息
 */
@Getter
@Setter
@KryoSerialize
public class BaseUserSession implements UserDetails {

    @Serial
    private static final long serialVersionUID = 8084761221583719942L;

    protected String userId;

    protected String fullname;

    protected String account;

    public BaseUserSession(String userId, String account, String fullname) {
        this.userId = userId;
        this.account = account;
        this.fullname = fullname;
    }

//    /**
//     * 用户权限
//     */
//    protected List<GrantedAuthority> authorities = new ArrayList<>();
//
//    public void grantAppPermission(String appName) {
//        authorities.add(new SimpleGrantedAuthority(appName));
//    }

    @JsonIgnore
    @Override
    public Collection<? extends GrantedAuthority> getAuthorities() {
        return new ArrayList<>();
    }

    @Override
    public String getUsername() {
        return this.account;
    }


    @JsonIgnore
    @Override
    public String getPassword() {
        return null;
    }


    /**
     * 账户是否未过期,过期无法验证
     * 未实现此功能
     */
    @JsonIgnore
    @Override
    public boolean isAccountNonExpired() {
        return true;
    }

    /**
     * 指定用户是否解锁,锁定的用户无法进行身份验证
     * 未实现此功能
     */
    @JsonIgnore
    @Override
    public boolean isAccountNonLocked() {
        return true;
    }

    /**
     * 指示是否已过期的用户的凭据(密码),过期的凭据防止认证
     * 未实现此功能
     */
    @JsonIgnore
    @Override
    public boolean isCredentialsNonExpired() {
        return true;
    }

    /**
     * 是否可用 ,禁用的用户不能身份验证
     * 未实现此功能
     */
    @Override
    @JsonIgnore
    public boolean isEnabled() {
        return true;
    }

}
