package com.mlc.base.core.serialization.generic;

import com.mlc.base.core.serialization.strategies.IProcessorStrategy;
import java.util.HashSet;
import java.util.Set;
import lombok.extern.slf4j.Slf4j;

import java.lang.annotation.Annotation;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 注解处理器注册表
 */
@Slf4j
public class ProcessorRegistry {
    
    private final Map<Class<? extends Annotation>, IProcessorStrategy<? extends Annotation>> strategies = new ConcurrentHashMap<>();

    /**
     * 注册自定义处理器
     * 
     * @param annotationType 注解类型
     * @param strategy 验证策略
     */
    public <T extends Annotation> void registerProcessor(Class<T> annotationType, IProcessorStrategy<T> strategy) {
        strategies.put(annotationType, strategy);
        log.info("注册注解处理器: {} -> {}", annotationType.getSimpleName(), strategy.getClass().getSimpleName());
    }
    

    /**
     * 获取自定义处理器
     * 
     * @param annotationType 注解类型
     * @return 验证策略，如果不存在则返回null
     */
    @SuppressWarnings("unchecked")
    public <T extends Annotation> IProcessorStrategy<T> getProcessor(Class<T> annotationType) {
        return (IProcessorStrategy<T>) strategies.get(annotationType);
    }
    
    /**
     * 检查是否有注册的策略
     * 
     * @param annotationType 注解类型
     * @return 是否有注册的处理器
     */
    public boolean hasProcessor(Class<? extends Annotation> annotationType) {
        return strategies.containsKey(annotationType);
    }
    
    /**
     * 获取所有已注册的注解类型
     */
    public Set<Class<? extends Annotation>> getRegisteredAnnotationTypes() {
        return new HashSet<>(strategies.keySet());
    }
}



