package com.mlc.base.core.auth.model.role;

import com.mlc.base.common.enums.application.AppRoleTypeEnum;
import com.mlc.base.common.enums.application.PermissionWayEnum;
import com.mlc.base.integrated.redis.config.codec.KryoSerialize;
import java.io.Serial;

import java.io.Serializable;
import lombok.Getter;
import lombok.Setter;

/**
 * 角色行数据
 */
@Getter
@Setter
@KryoSerialize
public class RoleProfile implements Serializable {
    
    @Serial
    private static final long serialVersionUID = 1L;
    
    /**
     * 角色ID
     */
    private String roleId;
    
    /**
     * 应用角色类型
     * 应该由 permission 来确定不同维度和层级的权限，但是为简化设计，直接使用 roleType 固化了权限
     * 具体状态码参见:{@link AppRoleTypeEnum}
     */
    private Integer roleType;
    
    /**
     * 权限方式
     * {@link PermissionWayEnum}
     */
    private Integer permissionWay;
    
    // 通用权限设置
    private Boolean generalAdd;
    private Boolean generalShare;
    private Boolean generalImport;
    private Boolean generalExport;
    private Boolean generalDiscussion;
    private Boolean generalSystemPrinting;
    private Boolean generalAttachmentDownload;
    private Boolean generalLogging;
    

    /**
     * 获取角色类型枚举
     */
    public AppRoleTypeEnum getRoleTypeEnum() {
        return AppRoleTypeEnum.formValue(this.roleType);
    }
    
    /**
     * 获取权限方式枚举
     */
    public PermissionWayEnum getPermissionWayEnum() {
        return PermissionWayEnum.formValue(this.permissionWay);
    }
    
    /**
     * 检查角色是否有效
     */
    public boolean isValid() {
        return roleId != null && 
               !roleId.trim().isEmpty() && 
               roleType != null && 
               permissionWay != null;
    }
}
