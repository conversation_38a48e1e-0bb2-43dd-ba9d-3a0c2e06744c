package com.mlc.base.core.auth.role;

import com.mlc.base.common.enums.application.PermissionWayEnum;
import com.mlc.base.core.auth.model.role.RoleProfile;
import com.mlc.base.integrated.redis.config.codec.KryoSerialize;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.springframework.util.CollectionUtils;
import java.io.IOException;
import java.io.ObjectInputStream;
import java.io.ObjectOutputStream;
import java.io.Serial;
import java.io.Serializable;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 用户应用角色，代表用户在某个租户下的某个应用下的角色
 */
@Getter
@Setter
@NoArgsConstructor
@KryoSerialize
public class UserApplicationRole implements Serializable {

    @Serial
    private static final long serialVersionUID = -5236254738820712212L;

    private List<RoleProfile> roleRows;

    // 是否为应用的拥有者，有且只有一个
    private Boolean owner;

    public UserApplicationRole(List<RoleProfile> roleRows, Boolean owner) {
        this.roleRows = roleRows;
        this.owner = owner;
    }

    /**
     * 计算用户的角色类型,得出用户的角色类型的最大值
     */
    public Integer calcMaxRoleType() {
        return RoleCalculator.calculateMaxRoleType(this.roleRows, this.owner);
    }

    /**
     * 计算用户的权限方式,得出用户的权限方式的最大值
     */
    public Integer calcMaxPermissionWay() {
        return RoleCalculator.calculateMaxPermissionWay(this.roleRows);
    }

    /**
     * 是否为自定义权限方式
     */
    public Boolean currCustomPermissionWay() {
        return Objects.equals(this.calcMaxPermissionWay(), PermissionWayEnum.CUSTOM.getValue());
    }

    /**
     * 获取用户的角色ID集合
     */
    public Set<String> getRoleIds() {
        if (CollectionUtils.isEmpty(this.roleRows)) {
            return Set.of(); // 返回空集合而不是抛出异常，更加健壮
        }
        return this.roleRows.stream()
                .map(RoleProfile::getRoleId)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());
    }

    /**
     * 是否需要校验操作动作权限
     * <p>
     * 系统角色只校验数据范围权限，不校验操作权限 </br>
     * 如果某个用户是内置系统管理员身份，他的权限规定为简易模式： </br>
     * 管理员 & 运营者 & 拥有者 -> 可查看、编辑、删除所有记录  </br>
     * 开发者 -> 可查看加入的，只能编辑、删除自己拥有的记录 </br>
     * 具体状态码参见：{@link com.mlc.base.common.enums.application.AppRoleTypeEnum} </br>
     * 文档链接：<a href="https://help.mingdao.com/role/type-configuration#a1">点击跳转</a>
     */
    public Boolean needCheckActionAuth() {
        IPermissionStrategy strategy = IPermissionStrategy.createDefault(
                this.calcMaxRoleType(), 
                this.calcMaxPermissionWay()
        );
        return strategy.needCheckActionAuth();
    }

    /**
     * 是否需要校验数据范围权限
     * <p>
     * 系统开发者角色、普通用户角色需要校验数据范围权限，其他角色不需要校验
     */
    public Boolean needCheckDataAuth() {
        IPermissionStrategy strategy = IPermissionStrategy.createDefault(
                this.calcMaxRoleType(), 
                this.calcMaxPermissionWay()
        );
        return strategy.needCheckDataAuth();
    }

    /**
     * 检查角色数据是否有效
     */
    public boolean isValid() {
        if (CollectionUtils.isEmpty(this.roleRows)) {
            return false;
        }
        
        return this.roleRows.stream().allMatch(RoleProfile::isValid);
    }
    
    @Serial
    private void writeObject(ObjectOutputStream out) throws IOException {
        out.defaultWriteObject();
    }

    @Serial
    private void readObject(ObjectInputStream in) throws IOException, ClassNotFoundException {
        in.defaultReadObject();
    }
}
