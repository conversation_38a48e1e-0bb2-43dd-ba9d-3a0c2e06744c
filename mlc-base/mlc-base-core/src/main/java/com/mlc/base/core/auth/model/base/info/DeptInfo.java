package com.mlc.base.core.auth.model.base.info;

import com.mlc.base.integrated.redis.config.codec.KryoSerialize;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@KryoSerialize
@NoArgsConstructor
@AllArgsConstructor
public class DeptInfo {
    private String departmentId;

    /**
     * 是否是主部门
     */
    private Boolean primary;

    /**
     * 是否部门负责人
     */
    private Boolean principal;
}
