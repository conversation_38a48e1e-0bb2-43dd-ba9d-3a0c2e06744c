package com.mlc.base.core.auth.role;

import com.mlc.base.common.enums.application.AppRoleTypeEnum;
import com.mlc.base.common.exception.errors.RoleErrors;
import com.mlc.base.core.auth.model.role.RoleProfile;
import io.nop.api.core.exceptions.NopException;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * 角色计算器
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class RoleCalculator {
    
    /**
     * 计算用户的最大角色类型
     * 
     * @param roleRows 角色行列表
     * @param isOwner 是否为拥有者
     * @return 最大角色类型值
     */
    public static Integer calculateMaxRoleType(List<RoleProfile> roleRows, Boolean isOwner) {
        validateRoleRows(roleRows);
        
        // 拥有者拥有最高权限
        if (Boolean.TRUE.equals(isOwner)) {
            return AppRoleTypeEnum.POSSESS.getValue();
        }
        
        // 检查是否同时拥有运营者和开发者角色
        if (hasRunnerAndDeveloperRoles(roleRows)) {
            return AppRoleTypeEnum.RUNNER_DEVELOPERS.getValue();
        }
        
        // 返回角色类型的最大值
        return roleRows.stream()
                .mapToInt(RoleProfile::getRoleType)
                .max()
                .orElse(0);
    }
    
    /**
     * 计算用户的最大权限方式
     * 
     * @param roleRows 角色行列表
     * @return 最大权限方式值
     */
    public static Integer calculateMaxPermissionWay(List<RoleProfile> roleRows) {
        validateRoleRows(roleRows);
        
        return roleRows.stream()
                .mapToInt(RoleProfile::getPermissionWay)
                .max()
                .orElse(0);
    }
    
    /**
     * 检查是否同时拥有运营者和开发者角色
     */
    private static boolean hasRunnerAndDeveloperRoles(List<RoleProfile> roleRows) {
        boolean hasRunner = false;
        boolean hasDeveloper = false;
        
        for (RoleProfile roleRow : roleRows) {
            Integer roleType = roleRow.getRoleType();
            if (AppRoleTypeEnum.RUNNER.getValue().equals(roleType)) {
                hasRunner = true;
            } else if (AppRoleTypeEnum.DEVELOPERS.getValue().equals(roleType)) {
                hasDeveloper = true;
            }
            
            if (hasRunner && hasDeveloper) {
                return true;
            }
        }
        
        return false;
    }
    
    private static void validateRoleRows(List<RoleProfile> roleRows) {
        if (CollectionUtils.isEmpty(roleRows)) {
            throw new NopException(RoleErrors.ERR_ROLE_NO_PERMISSION);
        }
    }
}
