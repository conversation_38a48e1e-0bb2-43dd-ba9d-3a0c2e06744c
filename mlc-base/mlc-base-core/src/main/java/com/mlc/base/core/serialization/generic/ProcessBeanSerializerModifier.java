package com.mlc.base.core.serialization.generic;

import com.fasterxml.jackson.databind.BeanDescription;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializationConfig;
import com.fasterxml.jackson.databind.ser.BeanSerializerModifier;
import lombok.extern.slf4j.Slf4j;

import java.lang.annotation.Annotation;
import java.lang.reflect.Field;

/**
 * 通用注解Bean序列化修改器
 * 自动为包含已注册注解的类应用自定义序列化器
 */
@Slf4j
public class ProcessBeanSerializerModifier extends BeanSerializerModifier {
    
    private final ProcessorRegistry registry;
    
    public ProcessBeanSerializerModifier(ProcessorRegistry registry) {
        this.registry = registry;
    }
    
    @Override
    public JsonSerializer<?> modifySerializer(SerializationConfig config, BeanDescription beanDesc, JsonSerializer<?> serializer) {
        
        // 检查类是否包含已注册的注解
        if (hasRegisteredAnnotation(beanDesc.getBeanClass())) {
            log.debug("为类 {} 应用通用注解序列化器", beanDesc.getBeanClass().getSimpleName());
            
            @SuppressWarnings("unchecked")
            JsonSerializer<Object> objectSerializer = (JsonSerializer<Object>) serializer;
            return new ProcessBeanSerializer(objectSerializer, beanDesc.getBeanClass(), registry);
        }
        
        return serializer;
    }
    
    /**
     * 检查类是否包含已注册的注解
     */
    private boolean hasRegisteredAnnotation(Class<?> clazz) {
        while (clazz != null && clazz != Object.class) {
            Field[] fields = clazz.getDeclaredFields();
            for (Field field : fields) {
                Annotation[] annotations = field.getAnnotations();
                for (Annotation annotation : annotations) {
                    if (registry.hasProcessor(annotation.annotationType())) {
                        return true;
                    }
                }
            }
            clazz = clazz.getSuperclass();
        }
        return false;
    }
}



