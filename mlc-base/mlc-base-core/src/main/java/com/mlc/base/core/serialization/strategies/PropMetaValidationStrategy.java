package com.mlc.base.core.serialization.strategies;

import io.nop.api.core.annotations.meta.PropMeta;
import io.nop.xlang.xmeta.ISchema;
import io.nop.xlang.xmeta.SimpleSchemaValidator;
import io.nop.xlang.xmeta.reflect.ReflectObjMetaParser;
import lombok.extern.slf4j.Slf4j;

import java.lang.reflect.Field;
import org.assertj.core.util.Strings;

/**
 * PropMeta注解的验证策略实现
 */
@Slf4j
public class PropMetaValidationStrategy implements IProcessorStrategy<PropMeta> {

    @Override
    public Object process(String fieldName, Object originalValue, PropMeta annotation, Field field, Object targetObject) {

        if (annotation.mandatory()) {
           if(originalValue == null || (originalValue instanceof String && Strings.isNullOrEmpty((String) originalValue))) {
                throw new IllegalArgumentException("字段 " + fieldName + " 不能为空");
           }
        }

        ISchema iSchema = ReflectObjMetaParser.INSTANCE.buildSchemaFromPropMeta(annotation);
        SimpleSchemaValidator.INSTANCE.validate(iSchema, null, null, fieldName, originalValue, null, (msg) -> {
            log.warn("字段: {} 验证失败: {}", fieldName, msg);
            throw new IllegalArgumentException("字段: " + fieldName + " 验证失败: " + msg);
        });

        return originalValue;
    }
}



