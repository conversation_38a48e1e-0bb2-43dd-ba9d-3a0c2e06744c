package com.mlc.base.core.auth.model.login;

import io.nop.api.core.annotations.data.DataBean;
import io.nop.api.core.annotations.meta.PropMeta;
import lombok.Getter;
import lombok.Setter;


@DataBean
@Getter
@Setter
public class LoginRequest {

    private String tenantId;

    /**
     * 登录方式，用户名/密码登录、短信验证、sso等
     */
    private Integer loginType;

    /**
     * 登录账号，此账号可以是手机号、邮箱
     */
    private String account;

    /**
     * 登录密码
     */
    private String password;

    /**
     * 是否记住用户名密码
     */
    private boolean isCookie;

    /**
     * 第三方账号id标识
     */
    private String unionId;

    /**
     * 第三方账号随机码
     */
    private String state;

    /**
     * 第三方账号类型
     * <p>
     * 例如：（微信，QQ，小米），对应 uop 的中 MlcUopUserExtBind 表
     */
    private String tpType;

    @PropMeta(mandatory = true)
    public void setPassword(String password) {
        this.password = password;
    }

    @PropMeta(mandatory = true)
    public void setLoginType(Integer loginType) {
        this.loginType = loginType;
    }

    public boolean getIsCookie() {
        return isCookie;
    }
}
