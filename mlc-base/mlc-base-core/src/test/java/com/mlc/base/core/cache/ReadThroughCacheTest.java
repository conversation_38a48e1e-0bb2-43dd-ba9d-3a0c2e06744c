package com.mlc.base.core.cache;

import com.mlc.base.core.cache.CacheBuilder;
import com.mlc.base.core.cache.IMinimalCache;
import com.mlc.base.core.cache.ICacheInterceptor;
import com.mlc.base.core.cache.IKeyPolicy;
import com.mlc.base.core.cache.ReadThroughCache;
import com.mlc.base.core.cache.layers.DatabaseCacheLayer;
import com.mlc.base.core.cache.layers.LocalCacheLayer;
import com.mlc.base.core.cache.layers.RedisCacheLayer;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.List;
import java.util.function.Function;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

/**
 * ReadThroughCache测试用例
 */
public class ReadThroughCacheTest {

    @Mock
    private io.nop.commons.cache.ICache<String, String> localCache;
    
    @Mock
    private io.nop.commons.cache.ICache<String, String> redisCache;
    
    @Mock
    private Function<String, String> dataLoader;
    
    private ReadThroughCache<String, String> cache;
    
    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }
    
    @Test
    void testGetFromFirstLayer() {
        // 准备数据
        String key = "test-key";
        String value = "test-value";
        
        when(localCache.get(key)).thenReturn(value);
        
        // 创建缓存
        cache = new ReadThroughCache<>(List.of(
            new LocalCacheLayer<>(localCache),
            new RedisCacheLayer<>(redisCache),
            new DatabaseCacheLayer<>(dataLoader)
        ));
        
        // 执行测试
        String result = cache.get(key);
        
        // 验证结果
        assertEquals(value, result);
        verify(localCache).get(key);
        verifyNoInteractions(redisCache, dataLoader);
    }
    
    @Test
    void testGetFromSecondLayerWithBackfill() {
        // 准备数据
        String key = "test-key";
        String value = "test-value";
        
        when(localCache.get(key)).thenReturn(null);
        when(redisCache.get(key)).thenReturn(value);
        
        // 创建缓存
        cache = new ReadThroughCache<>(List.of(
            new LocalCacheLayer<>(localCache),
            new RedisCacheLayer<>(redisCache),
            new DatabaseCacheLayer<>(dataLoader)
        ));
        
        // 执行测试
        String result = cache.get(key);
        
        // 验证结果
        assertEquals(value, result);
        verify(localCache).get(key);
        verify(redisCache).get(key);
        verify(localCache).put(key, value); // 回填到本地缓存
        verifyNoInteractions(dataLoader);
    }
    
    @Test
    void testGetFromDatabaseWithBackfill() {
        // 准备数据
        String key = "test-key";
        String value = "test-value";
        
        when(localCache.get(key)).thenReturn(null);
        when(redisCache.get(key)).thenReturn(null);
        when(dataLoader.apply(key)).thenReturn(value);
        
        // 创建缓存
        cache = new ReadThroughCache<>(List.of(
            new LocalCacheLayer<>(localCache),
            new RedisCacheLayer<>(redisCache),
            new DatabaseCacheLayer<>(dataLoader)
        ));
        
        // 执行测试
        String result = cache.get(key);
        
        // 验证结果
        assertEquals(value, result);
        verify(localCache).get(key);
        verify(redisCache).get(key);
        verify(dataLoader).apply(key);
        verify(localCache).put(key, value); // 回填到本地缓存
        verify(redisCache).put(key, value); // 回填到Redis缓存
    }
    
    @Test
    void testGetNotFound() {
        // 准备数据
        String key = "test-key";
        
        when(localCache.get(key)).thenReturn(null);
        when(redisCache.get(key)).thenReturn(null);
        when(dataLoader.apply(key)).thenReturn(null);
        
        // 创建缓存
        cache = new ReadThroughCache<>(List.of(
            new LocalCacheLayer<>(localCache),
            new RedisCacheLayer<>(redisCache),
            new DatabaseCacheLayer<>(dataLoader)
        ));
        
        // 执行测试
        String result = cache.get(key);
        
        // 验证结果
        assertNull(result);
        verify(localCache).get(key);
        verify(redisCache).get(key);
        verify(dataLoader).apply(key);
        verify(localCache, never()).put(any(), any());
        verify(redisCache, never()).put(any(), any());
    }
    
    @Test
    void testPutWriteThrough() {
        // 准备数据
        String key = "test-key";
        String value = "test-value";
        
        // 创建缓存
        cache = new ReadThroughCache<>(List.of(
            new LocalCacheLayer<>(localCache),
            new RedisCacheLayer<>(redisCache),
            new DatabaseCacheLayer<>(dataLoader)
        ));
        
        // 执行测试
        cache.put(key, value);
        
        // 验证结果
        verify(localCache).put(key, value);
        verify(redisCache).put(key, value);
        // DatabaseCacheLayer默认不支持写入，所以不会调用dataLoader
    }
    
    @Test
    void testRemoveWriteThrough() {
        // 准备数据
        String key = "test-key";
        
        // 创建缓存
        cache = new ReadThroughCache<>(List.of(
            new LocalCacheLayer<>(localCache),
            new RedisCacheLayer<>(redisCache),
            new DatabaseCacheLayer<>(dataLoader)
        ));
        
        // 执行测试
        cache.remove(key);
        
        // 验证结果
        verify(localCache).remove(key);
        verify(redisCache).remove(key);
        // DatabaseCacheLayer默认不支持删除，所以不会调用dataLoader
    }
    
    @Test
    void testWithKeyPolicy() {
        // 准备数据
        String originalKey = "test-key";
        String transformedKey = "prefix:test-key";
        String value = "test-value";
        
        IKeyPolicy<String> keyPolicy = key -> "prefix:" + key;
        when(localCache.get(transformedKey)).thenReturn(value);
        
        // 创建缓存
        cache = new ReadThroughCache<>(List.of(
            new LocalCacheLayer<>(localCache)
        ), keyPolicy);
        
        // 执行测试
        String result = cache.get(originalKey);
        
        // 验证结果
        assertEquals(value, result);
        verify(localCache).get(transformedKey); // 应该使用转换后的键
    }
    
    @Test
    void testWithInterceptor() {
        // 准备数据
        String key = "test-key";
        String originalValue = "original-value";
        String interceptedValue = "intercepted-value";
        
        when(localCache.get(key)).thenReturn(originalValue);
        
        ICacheInterceptor<String, String> interceptor = new ICacheInterceptor<String, String>() {
            @Override
            public String onRetrieve(String k, java.util.function.Supplier<String> next) {
                return interceptedValue; // 拦截器返回不同的值
            }
        };
        
        // 创建缓存
        cache = new ReadThroughCache<>(List.of(
            new LocalCacheLayer<>(localCache)
        ), IKeyPolicy.identity(), List.of(interceptor));
        
        // 执行测试
        String result = cache.get(key);
        
        // 验证结果
        assertEquals(interceptedValue, result);
        // 由于拦截器直接返回值，不会调用下层缓存
        verifyNoInteractions(localCache);
    }
    
    @Test
    void testCacheBuilderSimpleUsage() {
        // 直接使用Mock对象进行简单的集成测试
        @SuppressWarnings("unchecked")
        io.nop.commons.cache.ICache<String, String> localCache = mock(io.nop.commons.cache.ICache.class);
        @SuppressWarnings("unchecked")
        io.nop.commons.cache.ICache<String, String> redisCache = mock(io.nop.commons.cache.ICache.class);
        
        // 模拟缓存行为
        when(localCache.get("prefix:key1")).thenReturn(null);
        when(redisCache.get("prefix:key1")).thenReturn(null);
        
        // 使用CacheBuilder创建缓存
        IMinimalCache<String, String> cache = CacheBuilder.<String, String>newBuilder()
                                                          .addLocalCache(localCache)
                                                          .addRedisCache(redisCache)
                                                          .addDatabaseLoader(key -> "db-value-for-" + key)
                                                          .withKeyPolicy(key -> "prefix:" + key)
                                                          .build();
        
        // 测试读取（从数据库加载）
        String result1 = cache.get("key1");
        assertEquals("db-value-for-prefix:key1", result1);
        
        // 验证调用
        verify(localCache).get("prefix:key1");
        verify(redisCache).get("prefix:key1");
        verify(localCache).put("prefix:key1", "db-value-for-prefix:key1"); // 回填到本地缓存
        verify(redisCache).put("prefix:key1", "db-value-for-prefix:key1"); // 回填到Redis缓存
        
        // 测试写入
        cache.put("key2", "manual-value");
        verify(localCache).put("prefix:key2", "manual-value");
        verify(redisCache).put("prefix:key2", "manual-value");
        
        // 测试删除
        cache.remove("key2");
        verify(localCache).remove("prefix:key2");
        verify(redisCache).remove("prefix:key2");
    }
    
    @Test
    void testEmptyLayersThrowsException() {
        assertThrows(IllegalArgumentException.class, () -> {
            new ReadThroughCache<String, String>(List.of());
        });
    }
    
    @Test
    void testNullLayersThrowsException() {
        assertThrows(IllegalArgumentException.class, () -> {
            new ReadThroughCache<String, String>(null);
        });
    }
}
