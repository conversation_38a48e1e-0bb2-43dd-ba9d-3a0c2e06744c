package com.mlc.base.common.utils.resource;

import java.util.Collection;

/**
 * 通用树结构资源接口
 * 用于表示任何具有树结构关系的资源
 *
 * @param <T> 资源类型
 * @param <R> 资源ID类型
 */
public interface ITreeResource<T, R> {

    /**
     * 获取资源ID
     *
     * @return 资源ID
     */
    R getResourceId();

    /**
     * 获取资源类型
     *
     * @return 资源类型
     */
    String getResourceType();

    /**
     * 获取父资源
     *
     * @return 父资源
     */
    T getParent();

    /**
     * 获取子资源集合
     *
     * @return 子资源集合
     */
    Collection<T> getChildren();
}