package com.mlc.base.common.utils;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 基于Jackson的深拷贝工具类
 * 使用序列化/反序列化实现深拷贝，简化维护工作
 */
@Slf4j
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class JacksonDeepCopyUtil {
    
    private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper();
    
    static {
        OBJECT_MAPPER.findAndRegisterModules();
    }
    
    /**
     * 通用深拷贝方法
     * @param original 原始对象
     * @param clazz 目标类型
     * @param <T> 泛型类型
     * @return 深拷贝的对象
     */
    public static <T> T deepCopy(T original, Class<T> clazz) {
        if (original == null) {
            return null;
        }
        
        try {
            // 序列化为JSON字符串
            String jsonString = OBJECT_MAPPER.writeValueAsString(original);
            // 反序列化为新对象
            return OBJECT_MAPPER.readValue(jsonString, clazz);
        } catch (Exception e) {
            log.error("深拷贝失败，对象类型: {}", clazz.getSimpleName(), e);
            throw new RuntimeException("深拷贝失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 深拷贝，自动推断类型
     * @param original 原始对象
     * @param <T> 泛型类型
     * @return 深拷贝的对象
     */
    @SuppressWarnings("unchecked")
    public static <T> T deepCopy(T original) {
        if (original == null) {
            return null;
        }
        
        try {
            Class<T> clazz = (Class<T>) original.getClass();
            String jsonString = OBJECT_MAPPER.writeValueAsString(original);
            return OBJECT_MAPPER.readValue(jsonString, clazz);
        } catch (Exception e) {
            log.error("深拷贝失败，对象类型: {}", original.getClass().getSimpleName(), e);
            throw new RuntimeException("深拷贝失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 深拷贝复杂泛型对象（如List、Map等）
     * @param original 原始对象
     * @param typeReference Jackson的TypeReference
     * @param <T> 泛型类型
     * @return 深拷贝的对象
     */
    public static <T> T deepCopy(T original, TypeReference<T> typeReference) {
        if (original == null) {
            return null;
        }
        
        try {
            String jsonString = OBJECT_MAPPER.writeValueAsString(original);
            return OBJECT_MAPPER.readValue(jsonString, typeReference);
        } catch (Exception e) {
            log.error("深拷贝失败", e);
            throw new RuntimeException("深拷贝失败: " + e.getMessage(), e);
        }
    }
}
