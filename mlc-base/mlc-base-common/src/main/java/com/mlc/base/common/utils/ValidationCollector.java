package com.mlc.base.common.utils;

import java.util.ArrayList;
import java.util.List;
import lombok.Getter;

/**
 * 通用验证收集器
 */
@Getter
public class ValidationCollector {
    private boolean valid;
    private final List<String> errors;
    private final List<String> warnings;
    
    public ValidationCollector() {
        this.valid = true;
        this.errors = new ArrayList<>();
        this.warnings = new ArrayList<>();
    }
    
    /**
     * 静态方法：添加错误信息
     * @param collector 收集器实例
     * @param error 错误信息
     */
    public static void addError(ValidationCollector collector, String error) {
        if (collector != null) {
            collector.errors.add(error);
            collector.valid = false;
        }
    }
    
    /**
     * 静态方法：添加警告信息
     * @param collector 收集器实例
     * @param warning 警告信息
     */
    public static void addWarning(ValidationCollector collector, String warning) {
        if (collector != null) {
            collector.warnings.add(warning);
        }
    }
    

    /**
     * 静态方法：批量添加警告信息
     * @param collector 收集器实例
     * @param warnings 警告信息列表
     */
    public static void addWarnings(ValidationCollector collector, List<String> warnings) {
        if (collector != null && warnings != null) {
            collector.warnings.addAll(warnings);
        }
    }
    

    /**
     * 静态方法：检查收集器是否有错误
     * @param collector 收集器实例
     * @return 是否有效（无错误）
     */
    public static boolean isValid(ValidationCollector collector) {
        return collector != null && collector.valid;
    }
    
    /**
     * 静态方法：获取错误数量
     * @param collector 收集器实例
     * @return 错误数量
     */
    public static int getErrorCount(ValidationCollector collector) {
        return collector != null ? collector.errors.size() : 0;
    }
    
    /**
     * 静态方法：获取警告数量
     * @param collector 收集器实例
     * @return 警告数量
     */
    public static int getWarningCount(ValidationCollector collector) {
        return collector != null ? collector.warnings.size() : 0;
    }
}
